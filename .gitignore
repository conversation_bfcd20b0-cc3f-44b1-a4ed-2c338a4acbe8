# Telegram 频道下载器 .gitignore

# 配置文件 (包含敏感信息)
config.py
*.session
*.session-journal

# 下载文件夹
downloads/
uploads/

# 日志文件
*.log
logs/

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# 虚拟环境
telegram_downloader_env/
venv/
env/
ENV/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# 系统文件
.DS_Store
Thumbs.db
desktop.ini

# 数据库文件
*.db
*.sqlite
*.sqlite3

# 临时文件
tmp/
temp/
*.tmp
*.temp

# gclone配置
gclone_config.json
*.json

# 备份文件
*.bak
*.backup

# 错误报告
error_*.txt
failed_*.txt
