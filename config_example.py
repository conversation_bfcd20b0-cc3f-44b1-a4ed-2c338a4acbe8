#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Telegram 频道下载器配置示例文件
复制此文件为 config.py 并修改相应配置
"""

# ==================== 必填配置 ====================
# 从 https://my.telegram.org/apps 获取
API_ID = 'your_api_id'
API_HASH = 'your_api_hash'

# 从 @BotFather 获取
BOT_TOKEN = 'your_bot_token'

# 你的Telegram用户ID (可以通过 @userinfobot 获取)
ADMIN_ID = 123456789

# 默认频道用户名或ID (可以在运行时通过Bot命令指定)
CHAT_ID = 'your_channel_username'

# ==================== 下载配置 ====================
# 下载保存路径
DOWNLOAD_PATH = './downloads'

# 最大并发下载数 (建议不超过20)
MAX_CONCURRENT = 10

# 重试次数
RETRY_TIMES = 3

# 重试延迟(秒)
RETRY_DELAY = 5

# ==================== 文件过滤配置 ====================
# 跳过贴纸文件
SKIP_STICKERS = True

# 跳过GIF文件
SKIP_GIFS = True

# 跳过动态贴纸
SKIP_ANIMATIONS = True

# 最大文件大小 (字节) - 默认2GB
MAX_FILE_SIZE = 2 * 1024 * 1024 * 1024

# 允许的文件扩展名 (空列表表示允许所有类型)
ALLOWED_EXTENSIONS = []
# 示例: ALLOWED_EXTENSIONS = ['.jpg', '.png', '.mp4', '.pdf', '.zip']

# 禁止的文件扩展名
BLOCKED_EXTENSIONS = ['.exe', '.bat', '.cmd', '.scr']

# ==================== Google Drive 上传配置 ====================
# 是否启用Google Drive上传
ENABLE_GDRIVE_UPLOAD = False

# gclone配置文件路径
GCLONE_CONFIG_PATH = './gclone_config.json'

# Google Drive文件夹ID
GDRIVE_FOLDER_ID = 'your_gdrive_folder_id'

# 上传后是否删除本地文件
DELETE_AFTER_UPLOAD = False

# ==================== 监控配置 ====================
# 是否启用新消息监控
ENABLE_MONITORING = False

# 监控的频道列表
MONITORED_CHATS = [
    # '@channel1',
    # '@channel2',
    # 'https://t.me/channel3'
]

# 监控间隔(秒)
MONITORING_INTERVAL = 60

# ==================== 日志配置 ====================
# 日志级别 (DEBUG, INFO, WARNING, ERROR)
LOG_LEVEL = 'INFO'

# 日志文件路径
LOG_FILE = 'telegram_downloader.log'

# 是否在控制台显示日志
CONSOLE_LOG = True

# 日志文件最大大小 (MB)
MAX_LOG_SIZE = 10

# 保留的日志文件数量
LOG_BACKUP_COUNT = 5

# ==================== 高级配置 ====================
# 会话文件名前缀
SESSION_NAME = 'downloader_session'

# Bot会话文件名前缀
BOT_SESSION_NAME = 'bot_session'

# 下载超时时间(秒)
DOWNLOAD_TIMEOUT = 300

# API请求超时时间(秒)
API_TIMEOUT = 30

# 是否使用代理
USE_PROXY = False

# 代理配置 (仅在USE_PROXY=True时生效)
PROXY_CONFIG = {
    'proxy_type': 'socks5',  # socks4, socks5, http
    'addr': '127.0.0.1',
    'port': 1080,
    'username': None,  # 可选
    'password': None   # 可选
}

# ==================== 通知配置 ====================
# 是否启用下载完成通知
ENABLE_NOTIFICATIONS = True

# 通知间隔 (每下载多少个文件通知一次)
NOTIFICATION_INTERVAL = 10

# 是否在下载失败时通知
NOTIFY_ON_ERROR = True

# ==================== 数据库配置 (可选) ====================
# 是否使用数据库记录下载历史
USE_DATABASE = False

# 数据库类型 (sqlite, mysql, postgresql)
DATABASE_TYPE = 'sqlite'

# SQLite数据库文件路径
SQLITE_DB_PATH = './download_history.db'

# MySQL/PostgreSQL配置
DATABASE_CONFIG = {
    'host': 'localhost',
    'port': 3306,
    'user': 'username',
    'password': 'password',
    'database': 'telegram_downloader'
}

# ==================== 自定义过滤器 ====================
def custom_file_filter(message, filename, file_size):
    """
    自定义文件过滤器
    
    Args:
        message: Telegram消息对象
        filename: 文件名
        file_size: 文件大小(字节)
    
    Returns:
        bool: True表示下载，False表示跳过
    """
    # 示例：跳过小于1MB的文件
    # if file_size < 1024 * 1024:
    #     return False
    
    # 示例：只下载特定日期之后的文件
    # from datetime import datetime, timedelta
    # if message.date < datetime.now() - timedelta(days=30):
    #     return False
    
    # 示例：根据文件名关键词过滤
    # keywords = ['important', 'backup', 'archive']
    # if not any(keyword in filename.lower() for keyword in keywords):
    #     return False
    
    return True

# ==================== 自定义文件名格式 ====================
def custom_filename_format(message, original_filename):
    """
    自定义文件名格式
    
    Args:
        message: Telegram消息对象
        original_filename: 原始文件名
    
    Returns:
        str: 格式化后的文件名
    """
    # 示例：添加日期前缀
    # from datetime import datetime
    # date_prefix = message.date.strftime('%Y%m%d_')
    # return f"{date_prefix}{original_filename}"
    
    # 示例：添加消息ID前缀
    # return f"msg_{message.id}_{original_filename}"
    
    return original_filename

# ==================== 自定义下载路径 ====================
def custom_download_path(message, filename):
    """
    自定义下载路径
    
    Args:
        message: Telegram消息对象
        filename: 文件名
    
    Returns:
        str: 完整的文件路径
    """
    import os
    from pathlib import Path
    
    # 示例：按日期分类
    # date_folder = message.date.strftime('%Y/%m')
    # return os.path.join(DOWNLOAD_PATH, date_folder, filename)
    
    # 示例：按文件类型分类
    # ext = Path(filename).suffix.lower()
    # type_folder = {
    #     '.jpg': 'images', '.png': 'images', '.gif': 'images',
    #     '.mp4': 'videos', '.avi': 'videos', '.mkv': 'videos',
    #     '.pdf': 'documents', '.doc': 'documents', '.txt': 'documents',
    #     '.zip': 'archives', '.rar': 'archives', '.7z': 'archives'
    # }.get(ext, 'others')
    # return os.path.join(DOWNLOAD_PATH, type_folder, filename)
    
    return os.path.join(DOWNLOAD_PATH, filename)
