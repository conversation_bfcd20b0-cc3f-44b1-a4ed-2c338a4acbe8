// OSI 3D 生物仿生艺术模拟器 - 简化版本
class OSI3DSimulator {
    constructor() {
        this.scene = null;
        this.camera = null;
        this.renderer = null;
        this.isSimulating = false;
        this.currentView = 'overview';
        this.animationId = null;
        
        // OSI层级数据
        this.layerData = {
            7: { name: "应用层", english: "Application", color: "#E6B3FF", description: "神经网络中枢" },
            6: { name: "表示层", english: "Presentation", color: "#FF6B9D", description: "转译生物器官" },
            5: { name: "会话层", english: "Session", color: "#00E5FF", description: "对话细胞集群" },
            4: { name: "传输层", english: "Transport", color: "#69F0AE", description: "循环系统节点" },
            3: { name: "网络层", english: "Network", color: "#FFD54F", description: "导航蜂群" },
            2: { name: "数据链路层", english: "Data Link", color: "#FF5722", description: "微生物群落" },
            1: { name: "物理层", english: "Physical", color: "#9C27B0", description: "菌丝网络基质" }
        };
        
        this.init();
    }
    
    async init() {
        try {
            console.log('🚀 开始初始化OSI 3D模拟器...');
            
            // 显示启动画面
            this.showStartupScreen();
            
            // 初始化3D场景
            await this.setupScene();
            
            // 创建OSI层级
            this.createOSILayers();
            
            // 生成层级标签
            this.generateLayerLabels();
            
            // 设置事件监听
            this.setupEventListeners();
            
            // 开始渲染循环
            this.startRenderLoop();
            
            // 隐藏启动画面
            setTimeout(() => this.hideStartupScreen(), 2000);
            
            console.log('✅ OSI 3D模拟器初始化完成');
            
        } catch (error) {
            console.error('❌ 初始化失败:', error);
            this.showError('初始化失败: ' + error.message);
        }
    }
    
    showStartupScreen() {
        const progressFill = document.getElementById('progress-fill');
        const progressText = document.getElementById('progress-text');
        
        let progress = 0;
        const interval = setInterval(() => {
            progress += Math.random() * 15;
            if (progress > 100) progress = 100;
            
            if (progressFill) progressFill.style.width = progress + '%';
            
            if (progress >= 100) {
                clearInterval(interval);
                if (progressText) progressText.textContent = '初始化完成！';
            }
        }, 200);
    }
    
    hideStartupScreen() {
        const startupScreen = document.getElementById('startup-screen');
        if (startupScreen) {
            startupScreen.classList.add('hidden');
        }
    }
    
    async setupScene() {
        const container = document.getElementById('three-canvas');
        if (!container) {
            throw new Error('找不到3D场景容器');
        }
        
        // 获取容器尺寸
        const rect = container.getBoundingClientRect();
        const width = rect.width || 800;
        const height = rect.height || 600;
        
        console.log(`📐 场景容器尺寸: ${width}x${height}`);
        
        // 创建场景
        this.scene = new THREE.Scene();
        this.scene.background = new THREE.Color(0x0a0b1e);
        
        // 创建摄像机
        this.camera = new THREE.PerspectiveCamera(75, width / height, 0.1, 1000);
        this.camera.position.set(30, 30, 30);
        this.camera.lookAt(0, 0, 0);
        
        // 创建渲染器
        this.renderer = new THREE.WebGLRenderer({ 
            antialias: true,
            alpha: true 
        });
        this.renderer.setSize(width, height);
        this.renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
        
        // 添加到容器
        container.appendChild(this.renderer.domElement);
        
        // 添加光照
        this.setupLighting();
        
        // 设置控制
        this.setupControls();
    }
    
    setupLighting() {
        // 环境光
        const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
        this.scene.add(ambientLight);
        
        // 方向光
        const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
        directionalLight.position.set(50, 50, 50);
        this.scene.add(directionalLight);
        
        // 点光源
        const pointLight = new THREE.PointLight(0x4facfe, 1, 100);
        pointLight.position.set(0, 20, 0);
        this.scene.add(pointLight);
    }
    
    createOSILayers() {
        Object.entries(this.layerData).forEach(([layerNum, data]) => {
            // 创建层级几何体
            const geometry = new THREE.BoxGeometry(8, 2, 8);
            const material = new THREE.MeshPhongMaterial({ 
                color: data.color,
                transparent: true,
                opacity: 0.8
            });
            
            const mesh = new THREE.Mesh(geometry, material);
            mesh.position.y = (parseInt(layerNum) - 1) * 3;
            mesh.userData = { layerNum, data };
            
            this.scene.add(mesh);
            
            // 添加边框
            const edges = new THREE.EdgesGeometry(geometry);
            const lineMaterial = new THREE.LineBasicMaterial({ color: 0xffffff });
            const wireframe = new THREE.LineSegments(edges, lineMaterial);
            wireframe.position.copy(mesh.position);
            this.scene.add(wireframe);
        });
    }
    
    generateLayerLabels() {
        const labelsContainer = document.getElementById('layer-labels');
        if (!labelsContainer) return;
        
        // 清空现有标签
        labelsContainer.innerHTML = '';
        
        // 生成标签位置
        const positions = [
            { top: '10%', left: '20%' },
            { top: '20%', left: '70%' },
            { top: '30%', left: '25%' },
            { top: '40%', left: '65%' },
            { top: '50%', left: '30%' },
            { top: '60%', left: '60%' },
            { top: '70%', left: '35%' }
        ];
        
        Object.entries(this.layerData).forEach(([layerNum, data], index) => {
            const label = document.createElement('div');
            label.className = 'layer-label';
            label.dataset.layer = layerNum;
            label.style.top = positions[index].top;
            label.style.left = positions[index].left;
            
            label.innerHTML = `
                <div class="label-content">
                    <div class="label-header">
                        <span class="label-number">${layerNum}</span>
                    </div>
                    <div class="label-info">
                        <div class="label-name">${data.name}</div>
                        <div class="label-english">${data.english}</div>
                        <div class="label-description">${data.description}</div>
                    </div>
                </div>
            `;
            
            // 添加点击事件
            label.addEventListener('click', () => this.focusOnLayer(layerNum));
            
            labelsContainer.appendChild(label);
        });
    }
    
    setupControls() {
        const canvas = this.renderer.domElement;
        let isMouseDown = false;
        let mouseX = 0, mouseY = 0;
        let cameraAngle = 0;
        let cameraRadius = 50;
        
        canvas.addEventListener('mousedown', (e) => {
            isMouseDown = true;
            mouseX = e.clientX;
            mouseY = e.clientY;
            canvas.style.cursor = 'grabbing';
        });
        
        canvas.addEventListener('mousemove', (e) => {
            if (!isMouseDown) return;
            
            const deltaX = e.clientX - mouseX;
            cameraAngle += deltaX * 0.01;
            
            this.camera.position.x = Math.cos(cameraAngle) * cameraRadius;
            this.camera.position.z = Math.sin(cameraAngle) * cameraRadius;
            this.camera.lookAt(0, 10, 0);
            
            mouseX = e.clientX;
            mouseY = e.clientY;
        });
        
        canvas.addEventListener('mouseup', () => {
            isMouseDown = false;
            canvas.style.cursor = 'grab';
        });
        
        canvas.addEventListener('wheel', (e) => {
            e.preventDefault();
            cameraRadius += e.deltaY * 0.1;
            cameraRadius = Math.max(20, Math.min(100, cameraRadius));
            
            this.camera.position.x = Math.cos(cameraAngle) * cameraRadius;
            this.camera.position.z = Math.sin(cameraAngle) * cameraRadius;
            this.camera.lookAt(0, 10, 0);
        });
        
        canvas.style.cursor = 'grab';
    }
    
    setupEventListeners() {
        // 窗口大小调整
        window.addEventListener('resize', () => this.onWindowResize());
        
        // 视图控制按钮
        document.querySelectorAll('.view-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                document.querySelectorAll('.view-btn').forEach(b => b.classList.remove('active'));
                e.target.closest('.view-btn').classList.add('active');
                this.currentView = e.target.closest('.view-btn').dataset.view;
                this.updateCameraPosition();
            });
        });
        
        // 仿真控制按钮
        document.getElementById('start-simulation')?.addEventListener('click', () => {
            this.toggleSimulation();
        });
        
        document.getElementById('reset-simulation')?.addEventListener('click', () => {
            this.resetSimulation();
        });
        
        // 标签页切换
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const targetTab = e.target.closest('.tab-btn').dataset.tab;
                this.switchTab(targetTab);
            });
        });
        
        // 交互模式切换
        document.querySelectorAll('.interaction-mode').forEach(mode => {
            mode.addEventListener('click', (e) => {
                document.querySelectorAll('.interaction-mode').forEach(m => m.classList.remove('active'));
                e.target.closest('.interaction-mode').classList.add('active');
            });
        });
    }
    
    startRenderLoop() {
        const animate = () => {
            this.animationId = requestAnimationFrame(animate);
            
            // 旋转层级
            this.scene.children.forEach((child, index) => {
                if (child.userData.layerNum) {
                    child.rotation.y += 0.005 * (parseInt(child.userData.layerNum) / 7);
                }
            });
            
            this.renderer.render(this.scene, this.camera);
        };
        
        animate();
    }
    
    onWindowResize() {
        const container = document.getElementById('three-canvas');
        if (!container || !this.renderer || !this.camera) return;
        
        const rect = container.getBoundingClientRect();
        const width = rect.width || 800;
        const height = rect.height || 600;
        
        this.camera.aspect = width / height;
        this.camera.updateProjectionMatrix();
        this.renderer.setSize(width, height);
    }
    
    focusOnLayer(layerNum) {
        console.log(`聚焦到第${layerNum}层`);
        // 这里可以添加聚焦动画
    }
    
    updateCameraPosition() {
        // 根据当前视图更新摄像机位置
        switch (this.currentView) {
            case 'overview':
                this.camera.position.set(30, 30, 30);
                break;
            case 'layers':
                this.camera.position.set(0, 50, 30);
                break;
            case 'network':
                this.camera.position.set(50, 20, 0);
                break;
        }
        this.camera.lookAt(0, 10, 0);
    }
    
    toggleSimulation() {
        this.isSimulating = !this.isSimulating;
        const btn = document.getElementById('start-simulation');
        if (btn) {
            const icon = btn.querySelector('i');
            const text = btn.querySelector('span');
            if (this.isSimulating) {
                icon.className = 'fas fa-pause';
                text.textContent = '暂停生命流';
            } else {
                icon.className = 'fas fa-play';
                text.textContent = '启动生命流';
            }
        }
    }
    
    resetSimulation() {
        this.isSimulating = false;
        console.log('重置仿真');
    }
    
    switchTab(tabName) {
        // 切换标签页
        document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
        document.querySelectorAll('.tab-panel').forEach(panel => panel.classList.remove('active'));
        
        document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');
        document.querySelector(`.tab-panel[data-tab="${tabName}"]`).classList.add('active');
    }
    
    showError(message) {
        console.error(message);
        alert(message); // 简单的错误显示，可以后续改进
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    window.osiSimulator = new OSI3DSimulator();
    console.log('✅ OSI 3D模拟器已启动');
});
