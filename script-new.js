// OSI 3D 生物仿生艺术模拟器 - 完整功能版本
class OSI3DSimulator {
    constructor() {
        // 核心Three.js对象
        this.scene = null;
        this.camera = null;
        this.renderer = null;
        this.controls = null;

        // 状态管理
        this.isSimulating = false;
        this.currentView = 'overview';
        this.animationId = null;
        this.focusedLayer = null;

        // 3D对象集合
        this.layerMeshes = [];
        this.particleSystems = [];
        this.dataPackets = [];
        this.connections = [];
        this.bioEffects = [];

        // 功能系统
        this.dataFlowSystem = null;
        this.interactionSystem = null;
        this.infoPanelSystem = null;

        // 性能监控
        this.stats = {
            fps: 0,
            frameCount: 0,
            lastTime: performance.now(),
            memory: 0,
            drawCalls: 0
        };

        // OSI层级数据 - 增强版
        this.layerData = {
            7: {
                name: "应用层",
                english: "Application",
                color: 0xE6B3FF,
                description: "神经网络中枢",
                protocols: ["HTTP", "HTTPS", "FTP", "SMTP", "DNS"],
                devices: ["Web服务器", "邮件服务器", "DNS服务器"],
                bioAnalogy: "大脑皮层",
                position: { x: 0, y: 18, z: 0 },
                size: { width: 12, height: 2, depth: 12 }
            },
            6: {
                name: "表示层",
                english: "Presentation",
                color: 0xFF6B9D,
                description: "转译生物器官",
                protocols: ["SSL", "TLS", "JPEG", "MPEG", "ASCII"],
                devices: ["加密设备", "压缩器", "格式转换器"],
                bioAnalogy: "感觉器官",
                position: { x: 0, y: 15, z: 0 },
                size: { width: 11, height: 2, depth: 11 }
            },
            5: {
                name: "会话层",
                english: "Session",
                color: 0x00E5FF,
                description: "对话细胞集群",
                protocols: ["NetBIOS", "RPC", "SQL", "NFS"],
                devices: ["会话管理器", "认证服务器"],
                bioAnalogy: "神经突触",
                position: { x: 0, y: 12, z: 0 },
                size: { width: 10, height: 2, depth: 10 }
            },
            4: {
                name: "传输层",
                english: "Transport",
                color: 0x69F0AE,
                description: "循环系统节点",
                protocols: ["TCP", "UDP", "SPX", "SCTP"],
                devices: ["网关", "防火墙", "代理服务器"],
                bioAnalogy: "循环系统",
                position: { x: 0, y: 9, z: 0 },
                size: { width: 9, height: 2, depth: 9 }
            },
            3: {
                name: "网络层",
                english: "Network",
                color: 0xFFD54F,
                description: "导航蜂群",
                protocols: ["IP", "ICMP", "IGMP", "ARP"],
                devices: ["路由器", "三层交换机"],
                bioAnalogy: "神经网络",
                position: { x: 0, y: 6, z: 0 },
                size: { width: 8, height: 2, depth: 8 }
            },
            2: {
                name: "数据链路层",
                english: "Data Link",
                color: 0xFF5722,
                description: "微生物群落",
                protocols: ["Ethernet", "PPP", "Frame Relay"],
                devices: ["交换机", "网桥", "网卡"],
                bioAnalogy: "细胞膜",
                position: { x: 0, y: 3, z: 0 },
                size: { width: 7, height: 2, depth: 7 }
            },
            1: {
                name: "物理层",
                english: "Physical",
                color: 0x9C27B0,
                description: "菌丝网络基质",
                protocols: ["电信号", "光信号", "无线信号"],
                devices: ["集线器", "中继器", "网线", "光纤"],
                bioAnalogy: "DNA双螺旋",
                position: { x: 0, y: 0, z: 0 },
                size: { width: 6, height: 2, depth: 6 }
            }
        };

        // 动画配置
        this.animationConfig = {
            rotationSpeed: 0.005,
            pulseSpeed: 0.02,
            dataFlowSpeed: 0.1,
            particleCount: 1000,
            connectionOpacity: 0.3
        };

        this.init();
    }
    
    async init() {
        try {
            console.log('🚀 开始初始化OSI 3D模拟器...');

            // 显示启动画面
            this.showStartupScreen();

            // 检查WebGL支持
            if (!this.checkWebGLSupport()) {
                throw new Error('当前浏览器不支持WebGL');
            }

            // 初始化3D场景
            await this.setupScene();

            // 创建OSI层级和所有3D元素
            this.createOSILayers();

            // 生成层级标签
            this.generateLayerLabels();

            // 初始化信息面板
            this.initializeInfoPanels();

            // 初始化数据流系统
            this.initializeDataFlowSystem();

            // 初始化交互系统
            this.initializeInteractionSystem();

            // 初始化信息面板系统
            this.initializeInfoPanelSystem();

            // 设置事件监听
            this.setupEventListeners();

            // 开始渲染循环
            this.startRenderLoop();

            // 启动性能监控
            this.startPerformanceMonitoring();

            // 隐藏启动画面
            setTimeout(() => this.hideStartupScreen(), 2000);

            console.log('✅ OSI 3D模拟器初始化完成');

        } catch (error) {
            console.error('❌ 初始化失败:', error);
            this.showError('初始化失败: ' + error.message);
        }
    }

    checkWebGLSupport() {
        try {
            const canvas = document.createElement('canvas');
            const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
            if (!gl) return false;

            const renderer = gl.getParameter(gl.RENDERER);
            const vendor = gl.getParameter(gl.VENDOR);
            console.log(`✅ WebGL支持正常 - ${vendor}: ${renderer}`);
            return true;
        } catch (e) {
            console.error('WebGL检测失败:', e);
            return false;
        }
    }

    initializeInfoPanels() {
        // 初始化概览面板数据
        this.updateOverviewStats();

        // 初始化层级详情
        this.generateLayerDetailsList();

        // 初始化分析面板
        this.initializeAnalyticsPanel();

        // 初始化设置面板
        this.initializeSettingsPanel();

        console.log('📊 信息面板初始化完成');
    }

    initializeSettingsPanel() {
        // 设置面板将通过InfoPanelSystem处理
        console.log('⚙️ 设置面板准备就绪');
    }

    updateOverviewStats() {
        // 更新生态系统统计数据
        const stats = document.querySelectorAll('.tab-panel[data-tab="overview"] .stat-item');
        if (stats.length >= 3) {
            stats[0].querySelector('div:first-child').textContent = '7';
            stats[1].querySelector('div:first-child').textContent = '∞';
            stats[2].querySelector('div:first-child').textContent = Object.keys(this.layerData).length * 6;
        }
    }

    generateLayerDetailsList() {
        const layerDetailsContainer = document.getElementById('layer-details');
        if (!layerDetailsContainer) return;

        let detailsHTML = '';
        Object.entries(this.layerData).reverse().forEach(([layerNum, data]) => {
            detailsHTML += `
                <div class="layer-summary" data-layer="${layerNum}">
                    <div class="layer-summary-header">
                        <span class="layer-number" style="background-color: #${data.color.toString(16).padStart(6, '0')}">${layerNum}</span>
                        <div class="layer-summary-info">
                            <h4>${data.name}</h4>
                            <p>${data.english}</p>
                        </div>
                        <button class="focus-btn" onclick="window.osiSimulator.focusOnLayer(${layerNum})">
                            <i class="fas fa-search-plus"></i>
                        </button>
                    </div>
                    <p class="layer-summary-desc">${data.description}</p>
                </div>
            `;
        });

        layerDetailsContainer.innerHTML = detailsHTML;
    }

    initializeAnalyticsPanel() {
        const analyticsPanel = document.querySelector('.tab-panel[data-tab="analytics"]');
        if (!analyticsPanel) return;

        analyticsPanel.innerHTML = `
            <div class="analytics-content">
                <div class="analytics-grid">
                    <div class="analytics-card">
                        <h4><i class="fas fa-chart-line"></i> 数据流量</h4>
                        <div class="metric-value" id="data-throughput">1.2 GB/s</div>
                        <div class="metric-trend">↗ +15%</div>
                    </div>

                    <div class="analytics-card">
                        <h4><i class="fas fa-network-wired"></i> 网络延迟</h4>
                        <div class="metric-value" id="network-latency">12ms</div>
                        <div class="metric-trend">↘ -8%</div>
                    </div>

                    <div class="analytics-card">
                        <h4><i class="fas fa-microchip"></i> 处理效率</h4>
                        <div class="metric-value" id="processing-efficiency">94%</div>
                        <div class="metric-trend">→ 稳定</div>
                    </div>

                    <div class="analytics-card">
                        <h4><i class="fas fa-shield-alt"></i> 安全状态</h4>
                        <div class="metric-value" id="security-status">优秀</div>
                        <div class="metric-trend">✓ 正常</div>
                    </div>
                </div>

                <div class="analytics-chart">
                    <h4>实时性能监控</h4>
                    <div class="chart-placeholder">
                        <p>性能图表将在此显示</p>
                        <small>FPS: <span id="analytics-fps">60</span> | 内存: <span id="analytics-memory">128MB</span></small>
                    </div>
                </div>
            </div>
        `;
    }

    startPerformanceMonitoring() {
        // 每秒更新一次分析数据
        setInterval(() => {
            this.updateAnalyticsData();
        }, 1000);

        console.log('📈 性能监控已启动');
    }

    updateAnalyticsData() {
        // 更新分析面板的实时数据
        const throughput = document.getElementById('data-throughput');
        const latency = document.getElementById('network-latency');
        const efficiency = document.getElementById('processing-efficiency');
        const analyticsFps = document.getElementById('analytics-fps');
        const analyticsMemory = document.getElementById('analytics-memory');

        if (throughput) {
            const value = (1.0 + Math.sin(performance.now() * 0.001) * 0.5).toFixed(1);
            throughput.textContent = `${value} GB/s`;
        }

        if (latency) {
            const value = Math.round(10 + Math.random() * 10);
            latency.textContent = `${value}ms`;
        }

        if (efficiency) {
            const value = Math.round(90 + Math.random() * 10);
            efficiency.textContent = `${value}%`;
        }

        if (analyticsFps) {
            analyticsFps.textContent = this.stats.fps;
        }

        if (analyticsMemory) {
            analyticsMemory.textContent = `${this.stats.memory}MB`;
        }
    }
    
    showStartupScreen() {
        const progressFill = document.getElementById('progress-fill');
        const progressText = document.getElementById('progress-text');
        
        let progress = 0;
        const interval = setInterval(() => {
            progress += Math.random() * 15;
            if (progress > 100) progress = 100;
            
            if (progressFill) progressFill.style.width = progress + '%';
            
            if (progress >= 100) {
                clearInterval(interval);
                if (progressText) progressText.textContent = '初始化完成！';
            }
        }, 200);
    }
    
    hideStartupScreen() {
        const startupScreen = document.getElementById('startup-screen');
        if (startupScreen) {
            startupScreen.classList.add('hidden');
        }
    }
    
    async setupScene() {
        const container = document.getElementById('three-canvas');
        if (!container) {
            throw new Error('找不到3D场景容器');
        }

        // 获取容器尺寸
        const rect = container.getBoundingClientRect();
        const width = rect.width || 800;
        const height = rect.height || 600;

        console.log(`📐 场景容器尺寸: ${width}x${height}`);

        // 创建场景
        this.scene = new THREE.Scene();
        this.scene.background = new THREE.Color(0x0a0b1e);
        this.scene.fog = new THREE.Fog(0x0a0b1e, 50, 200);

        // 创建摄像机
        this.camera = new THREE.PerspectiveCamera(75, width / height, 0.1, 1000);
        this.camera.position.set(40, 35, 40);
        this.camera.lookAt(0, 10, 0);

        // 创建渲染器 - 增强配置
        this.renderer = new THREE.WebGLRenderer({
            antialias: true,
            alpha: true,
            powerPreference: "high-performance"
        });
        this.renderer.setSize(width, height);
        this.renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
        this.renderer.shadowMap.enabled = true;
        this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
        this.renderer.outputEncoding = THREE.sRGBEncoding;
        this.renderer.toneMapping = THREE.ACESFilmicToneMapping;
        this.renderer.toneMappingExposure = 1.2;

        // 添加到容器
        container.appendChild(this.renderer.domElement);

        // 设置后处理效果
        this.setupPostProcessing();

        // 添加光照系统
        this.setupAdvancedLighting();

        // 创建环境
        this.createEnvironment();

        // 设置控制
        this.setupControls();
    }
    
    setupPostProcessing() {
        // 这里可以添加后处理效果，如辉光、景深等
        // 需要引入THREE.js的后处理库
        console.log('🎨 后处理效果设置完成');
    }

    setupAdvancedLighting() {
        // 环境光 - 提供基础照明
        const ambientLight = new THREE.AmbientLight(0x404040, 0.4);
        this.scene.add(ambientLight);

        // 主方向光 - 模拟太阳光
        const mainLight = new THREE.DirectionalLight(0xffffff, 1.2);
        mainLight.position.set(50, 100, 50);
        mainLight.castShadow = true;
        mainLight.shadow.mapSize.width = 2048;
        mainLight.shadow.mapSize.height = 2048;
        mainLight.shadow.camera.near = 0.5;
        mainLight.shadow.camera.far = 500;
        mainLight.shadow.camera.left = -50;
        mainLight.shadow.camera.right = 50;
        mainLight.shadow.camera.top = 50;
        mainLight.shadow.camera.bottom = -50;
        this.scene.add(mainLight);

        // 辅助光源 - 填充阴影
        const fillLight = new THREE.DirectionalLight(0x4facfe, 0.3);
        fillLight.position.set(-30, 20, -30);
        this.scene.add(fillLight);

        // 动态点光源 - 为每个OSI层添加特色光源
        Object.entries(this.layerData).forEach(([layerNum, data]) => {
            const pointLight = new THREE.PointLight(data.color, 0.8, 20);
            pointLight.position.copy(data.position);
            pointLight.position.y += 3;

            // 添加光源动画
            pointLight.userData = {
                originalIntensity: 0.8,
                pulsePhase: Math.random() * Math.PI * 2,
                layerNum: layerNum
            };

            this.scene.add(pointLight);
        });

        // 环境光探针 - 增强反射效果
        const hemiLight = new THREE.HemisphereLight(0x4facfe, 0x9C27B0, 0.6);
        hemiLight.position.set(0, 50, 0);
        this.scene.add(hemiLight);

        console.log('💡 高级光照系统设置完成');
    }

    createEnvironment() {
        // 创建地面
        this.createGround();

        // 创建背景粒子
        this.createBackgroundParticles();

        // 创建能量场效果
        this.createEnergyField();

        console.log('🌍 环境创建完成');
    }

    createGround() {
        const groundGeometry = new THREE.PlaneGeometry(100, 100);
        const groundMaterial = new THREE.MeshLambertMaterial({
            color: 0x1a1b2e,
            transparent: true,
            opacity: 0.8
        });

        const ground = new THREE.Mesh(groundGeometry, groundMaterial);
        ground.rotation.x = -Math.PI / 2;
        ground.position.y = -2;
        ground.receiveShadow = true;

        // 添加网格线
        const gridHelper = new THREE.GridHelper(100, 50, 0x4facfe, 0x4facfe);
        gridHelper.material.opacity = 0.2;
        gridHelper.material.transparent = true;
        gridHelper.position.y = -1.9;

        this.scene.add(ground);
        this.scene.add(gridHelper);
    }

    createBackgroundParticles() {
        const particleCount = 500;
        const positions = new Float32Array(particleCount * 3);
        const colors = new Float32Array(particleCount * 3);

        for (let i = 0; i < particleCount; i++) {
            const i3 = i * 3;

            // 随机位置
            positions[i3] = (Math.random() - 0.5) * 200;
            positions[i3 + 1] = Math.random() * 100;
            positions[i3 + 2] = (Math.random() - 0.5) * 200;

            // 随机颜色
            const color = new THREE.Color();
            color.setHSL(Math.random() * 0.3 + 0.5, 0.7, 0.5);
            colors[i3] = color.r;
            colors[i3 + 1] = color.g;
            colors[i3 + 2] = color.b;
        }

        const particleGeometry = new THREE.BufferGeometry();
        particleGeometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
        particleGeometry.setAttribute('color', new THREE.BufferAttribute(colors, 3));

        const particleMaterial = new THREE.PointsMaterial({
            size: 0.5,
            vertexColors: true,
            transparent: true,
            opacity: 0.6,
            blending: THREE.AdditiveBlending
        });

        const particles = new THREE.Points(particleGeometry, particleMaterial);
        particles.userData = { type: 'backgroundParticles' };
        this.scene.add(particles);
        this.particleSystems.push(particles);
    }

    createEnergyField() {
        // 创建能量场环绕效果
        const ringCount = 5;
        for (let i = 0; i < ringCount; i++) {
            const radius = 25 + i * 8;
            const ringGeometry = new THREE.RingGeometry(radius, radius + 0.5, 32);
            const ringMaterial = new THREE.MeshBasicMaterial({
                color: 0x4facfe,
                transparent: true,
                opacity: 0.1 - i * 0.02,
                side: THREE.DoubleSide
            });

            const ring = new THREE.Mesh(ringGeometry, ringMaterial);
            ring.rotation.x = -Math.PI / 2;
            ring.position.y = 10 + i * 2;
            ring.userData = {
                type: 'energyRing',
                originalY: ring.position.y,
                phase: i * Math.PI / 3
            };

            this.scene.add(ring);
            this.bioEffects.push(ring);
        }
    }
    
    createOSILayers() {
        Object.entries(this.layerData).forEach(([layerNum, data]) => {
            this.createLayerMesh(parseInt(layerNum), data);
            this.createLayerConnections(parseInt(layerNum), data);
            this.createLayerParticles(parseInt(layerNum), data);
        });

        console.log('🏗️ OSI层级创建完成');
    }

    createLayerMesh(layerNum, data) {
        // 创建主体几何体 - 使用有机形状
        const geometry = this.createOrganicGeometry(data.size);

        // 创建材质 - 增强视觉效果
        const material = new THREE.MeshPhongMaterial({
            color: data.color,
            transparent: true,
            opacity: 0.7,
            shininess: 100,
            specular: 0x4facfe,
            emissive: data.color,
            emissiveIntensity: 0.1
        });

        const mesh = new THREE.Mesh(geometry, material);
        mesh.position.copy(data.position);
        mesh.castShadow = true;
        mesh.receiveShadow = true;
        mesh.userData = {
            layerNum,
            data,
            type: 'osiLayer',
            originalPosition: data.position.clone ? data.position.clone() : {...data.position},
            pulsePhase: Math.random() * Math.PI * 2
        };

        this.scene.add(mesh);
        this.layerMeshes.push(mesh);

        // 创建发光边框
        this.createGlowingEdges(mesh, data);

        // 创建层级标识
        this.createLayerLabel(mesh, layerNum, data);

        // 创建生物仿生装饰
        this.createBioDecorations(mesh, data);
    }

    createOrganicGeometry(size) {
        // 创建有机形状的几何体
        const geometry = new THREE.BoxGeometry(size.width, size.height, size.depth, 4, 2, 4);

        // 添加噪声变形，创造有机感
        const positions = geometry.attributes.position;
        for (let i = 0; i < positions.count; i++) {
            const x = positions.getX(i);
            const y = positions.getY(i);
            const z = positions.getZ(i);

            // 应用噪声变形
            const noise = (Math.sin(x * 0.5) + Math.cos(z * 0.5)) * 0.2;
            positions.setY(i, y + noise);
        }

        geometry.computeVertexNormals();
        return geometry;
    }

    createGlowingEdges(mesh, data) {
        const edges = new THREE.EdgesGeometry(mesh.geometry);
        const lineMaterial = new THREE.LineBasicMaterial({
            color: data.color,
            transparent: true,
            opacity: 0.8,
            linewidth: 2
        });

        const wireframe = new THREE.LineSegments(edges, lineMaterial);
        wireframe.position.copy(mesh.position);
        wireframe.userData = {
            type: 'layerEdges',
            parentLayer: mesh.userData.layerNum
        };

        this.scene.add(wireframe);
    }

    createLayerLabel(mesh, layerNum, data) {
        // 创建3D文字标签
        const canvas = document.createElement('canvas');
        const context = canvas.getContext('2d');
        canvas.width = 256;
        canvas.height = 128;

        // 绘制标签背景
        const gradient = context.createLinearGradient(0, 0, 0, canvas.height);
        gradient.addColorStop(0, `#${data.color.toString(16).padStart(6, '0')}80`);
        gradient.addColorStop(1, `#${data.color.toString(16).padStart(6, '0')}20`);
        context.fillStyle = gradient;
        context.fillRect(0, 0, canvas.width, canvas.height);

        // 绘制文字
        context.fillStyle = '#ffffff';
        context.font = 'bold 24px Arial';
        context.textAlign = 'center';
        context.shadowColor = 'rgba(0,0,0,0.5)';
        context.shadowBlur = 4;
        context.fillText(`第${layerNum}层`, canvas.width / 2, 40);

        context.font = 'bold 18px Arial';
        context.fillStyle = `#${data.color.toString(16).padStart(6, '0')}`;
        context.fillText(data.name, canvas.width / 2, 70);

        context.font = '14px Arial';
        context.fillStyle = '#cccccc';
        context.fillText(data.english, canvas.width / 2, 95);

        const texture = new THREE.CanvasTexture(canvas);
        const spriteMaterial = new THREE.SpriteMaterial({
            map: texture,
            transparent: true,
            opacity: 0.9
        });
        const sprite = new THREE.Sprite(spriteMaterial);

        sprite.position.copy(mesh.position);
        sprite.position.y += data.size.height + 2;
        sprite.scale.set(8, 4, 1);
        sprite.userData = {
            type: 'layerLabel',
            parentLayer: layerNum
        };

        this.scene.add(sprite);
    }

    createBioDecorations(mesh, data) {
        // 为每个层级添加生物仿生装饰元素
        const decorationCount = 5;
        for (let i = 0; i < decorationCount; i++) {
            const decorGeometry = new THREE.SphereGeometry(0.3, 8, 6);
            const decorMaterial = new THREE.MeshPhongMaterial({
                color: data.color,
                transparent: true,
                opacity: 0.6,
                emissive: data.color,
                emissiveIntensity: 0.2
            });

            const decoration = new THREE.Mesh(decorGeometry, decorMaterial);

            // 随机分布在层级周围
            const angle = (i / decorationCount) * Math.PI * 2;
            const radius = data.size.width * 0.7;
            decoration.position.x = mesh.position.x + Math.cos(angle) * radius;
            decoration.position.y = mesh.position.y + (Math.random() - 0.5) * data.size.height;
            decoration.position.z = mesh.position.z + Math.sin(angle) * radius;

            decoration.userData = {
                type: 'bioDecoration',
                parentLayer: mesh.userData.layerNum,
                orbitAngle: angle,
                orbitRadius: radius,
                orbitSpeed: 0.01 + Math.random() * 0.02
            };

            this.scene.add(decoration);
            this.bioEffects.push(decoration);
        }
    }

    createLayerConnections(layerNum, data) {
        // 创建层级之间的连接线
        if (layerNum > 1) {
            const lowerLayer = this.layerData[layerNum - 1];
            if (lowerLayer) {
                this.createConnection(data.position, lowerLayer.position, data.color);
            }
        }
    }

    createConnection(pos1, pos2, color) {
        const points = [];
        points.push(new THREE.Vector3(pos1.x, pos1.y, pos1.z));
        points.push(new THREE.Vector3(pos2.x, pos2.y, pos2.z));

        const geometry = new THREE.BufferGeometry().setFromPoints(points);
        const material = new THREE.LineBasicMaterial({
            color: color,
            transparent: true,
            opacity: 0.4,
            linewidth: 2
        });

        const line = new THREE.Line(geometry, material);
        line.userData = { type: 'layerConnection' };

        this.scene.add(line);
        this.connections.push(line);
    }

    createLayerParticles(layerNum, data) {
        // 为每个层级创建专属粒子系统
        const particleCount = 50;
        const positions = new Float32Array(particleCount * 3);
        const colors = new Float32Array(particleCount * 3);
        const sizes = new Float32Array(particleCount);

        const color = new THREE.Color(data.color);

        for (let i = 0; i < particleCount; i++) {
            const i3 = i * 3;

            // 在层级周围随机分布
            const radius = data.size.width * 0.8;
            const angle = Math.random() * Math.PI * 2;
            const height = (Math.random() - 0.5) * data.size.height * 2;

            positions[i3] = data.position.x + Math.cos(angle) * radius * Math.random();
            positions[i3 + 1] = data.position.y + height;
            positions[i3 + 2] = data.position.z + Math.sin(angle) * radius * Math.random();

            colors[i3] = color.r;
            colors[i3 + 1] = color.g;
            colors[i3 + 2] = color.b;

            sizes[i] = Math.random() * 2 + 1;
        }

        const particleGeometry = new THREE.BufferGeometry();
        particleGeometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
        particleGeometry.setAttribute('color', new THREE.BufferAttribute(colors, 3));
        particleGeometry.setAttribute('size', new THREE.BufferAttribute(sizes, 1));

        const particleMaterial = new THREE.PointsMaterial({
            size: 1,
            vertexColors: true,
            transparent: true,
            opacity: 0.8,
            blending: THREE.AdditiveBlending,
            sizeAttenuation: true
        });

        const particles = new THREE.Points(particleGeometry, particleMaterial);
        particles.userData = {
            type: 'layerParticles',
            layerNum: layerNum,
            originalPositions: positions.slice()
        };

        this.scene.add(particles);
        this.particleSystems.push(particles);
    }

    createDataPackets() {
        // 创建数据包动画系统
        const packetCount = 10;

        for (let i = 0; i < packetCount; i++) {
            const geometry = new THREE.SphereGeometry(0.2, 8, 6);
            const material = new THREE.MeshPhongMaterial({
                color: 0x4facfe,
                transparent: true,
                opacity: 0.8,
                emissive: 0x4facfe,
                emissiveIntensity: 0.3
            });

            const packet = new THREE.Mesh(geometry, material);
            packet.userData = {
                type: 'dataPacket',
                currentLayer: 1,
                targetLayer: 7,
                progress: 0,
                speed: 0.02 + Math.random() * 0.03,
                trail: []
            };

            // 初始位置设在物理层
            packet.position.copy(this.layerData[1].position);
            packet.position.x += (Math.random() - 0.5) * 4;
            packet.position.z += (Math.random() - 0.5) * 4;

            this.scene.add(packet);
            this.dataPackets.push(packet);
        }
    }
    
    generateLayerLabels(focusedLayer = null) {
        const labelsContainer = document.getElementById('layer-labels');
        if (!labelsContainer) return;

        // 清空现有标签
        labelsContainer.innerHTML = '';

        // 生成标签位置
        const positions = [
            { top: '10%', left: '20%' },
            { top: '20%', left: '70%' },
            { top: '30%', left: '25%' },
            { top: '40%', left: '65%' },
            { top: '50%', left: '30%' },
            { top: '60%', left: '60%' },
            { top: '70%', left: '35%' }
        ];

        Object.entries(this.layerData).forEach(([layerNum, data], index) => {
            const label = document.createElement('div');
            label.className = 'layer-label';
            if (focusedLayer && layerNum == focusedLayer) {
                label.className += ' focused';
            }
            label.dataset.layer = layerNum;
            label.style.top = positions[index].top;
            label.style.left = positions[index].left;

            label.innerHTML = `
                <div class="label-content">
                    <div class="label-header">
                        <span class="label-number">${layerNum}</span>
                    </div>
                    <div class="label-info">
                        <div class="label-name">${data.name}</div>
                        <div class="label-english">${data.english}</div>
                        <div class="label-description">${data.description}</div>
                    </div>
                </div>
            `;

            // 添加点击事件
            label.addEventListener('click', () => this.focusOnLayer(layerNum));

            labelsContainer.appendChild(label);
        });
    }
    
    setupControls() {
        const canvas = this.renderer.domElement;
        let isMouseDown = false;
        let mouseX = 0, mouseY = 0;
        let cameraAngle = 0;
        let cameraRadius = 50;
        
        canvas.addEventListener('mousedown', (e) => {
            isMouseDown = true;
            mouseX = e.clientX;
            mouseY = e.clientY;
            canvas.style.cursor = 'grabbing';
        });
        
        canvas.addEventListener('mousemove', (e) => {
            if (!isMouseDown) return;
            
            const deltaX = e.clientX - mouseX;
            cameraAngle += deltaX * 0.01;
            
            this.camera.position.x = Math.cos(cameraAngle) * cameraRadius;
            this.camera.position.z = Math.sin(cameraAngle) * cameraRadius;
            this.camera.lookAt(0, 10, 0);
            
            mouseX = e.clientX;
            mouseY = e.clientY;
        });
        
        canvas.addEventListener('mouseup', () => {
            isMouseDown = false;
            canvas.style.cursor = 'grab';
        });
        
        canvas.addEventListener('wheel', (e) => {
            e.preventDefault();
            cameraRadius += e.deltaY * 0.1;
            cameraRadius = Math.max(20, Math.min(100, cameraRadius));
            
            this.camera.position.x = Math.cos(cameraAngle) * cameraRadius;
            this.camera.position.z = Math.sin(cameraAngle) * cameraRadius;
            this.camera.lookAt(0, 10, 0);
        });
        
        canvas.style.cursor = 'grab';
    }
    
    setupEventListeners() {
        // 窗口大小调整
        window.addEventListener('resize', () => this.onWindowResize());
        
        // 视图控制按钮
        document.querySelectorAll('.view-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                document.querySelectorAll('.view-btn').forEach(b => b.classList.remove('active'));
                e.target.closest('.view-btn').classList.add('active');
                this.currentView = e.target.closest('.view-btn').dataset.view;
                this.updateCameraPosition();
            });
        });
        
        // 仿真控制按钮
        document.getElementById('start-simulation')?.addEventListener('click', () => {
            this.toggleSimulation();
        });
        
        document.getElementById('reset-simulation')?.addEventListener('click', () => {
            this.resetSimulation();
        });
        
        // 标签页切换
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const targetTab = e.target.closest('.tab-btn').dataset.tab;
                this.switchTab(targetTab);
            });
        });
        
        // 交互模式切换
        document.querySelectorAll('.interaction-mode').forEach(mode => {
            mode.addEventListener('click', (e) => {
                document.querySelectorAll('.interaction-mode').forEach(m => m.classList.remove('active'));
                const clickedMode = e.target.closest('.interaction-mode');
                if (clickedMode) {
                    clickedMode.classList.add('active');

                    // 切换交互模式
                    const modeName = clickedMode.dataset.mode;
                    if (this.interactionSystem) {
                        this.interactionSystem.switchMode(modeName);
                    }

                    // 更新状态显示
                    this.updateSimulationStatus();
                }
            });
        });
    }
    
    startRenderLoop() {
        const animate = () => {
            this.animationId = requestAnimationFrame(animate);

            // 更新性能统计
            this.updatePerformanceStats();

            // 更新所有动画
            this.updateAnimations();

            // 渲染场景
            this.renderer.render(this.scene, this.camera);
        };

        animate();
        console.log('🎬 渲染循环已启动');
    }

    updatePerformanceStats() {
        const now = performance.now();
        this.stats.frameCount++;

        if (now - this.stats.lastTime >= 1000) {
            this.stats.fps = Math.round((this.stats.frameCount * 1000) / (now - this.stats.lastTime));
            this.stats.frameCount = 0;
            this.stats.lastTime = now;

            // 更新内存使用情况
            if (performance.memory) {
                this.stats.memory = Math.round(performance.memory.usedJSHeapSize / 1048576);
            }

            // 更新UI显示
            this.updatePerformanceUI();
        }
    }

    updateAnimations() {
        const time = performance.now() * 0.001;

        // 更新OSI层级动画
        this.updateLayerAnimations(time);

        // 更新粒子系统
        this.updateParticleAnimations(time);

        // 更新数据包动画
        this.updateDataPacketAnimations(time);

        // 更新生物效果
        this.updateBioEffects(time);

        // 更新光照效果
        this.updateLightingEffects(time);

        // 更新数据流系统
        if (this.dataFlowSystem) {
            this.dataFlowSystem.update();
        }
    }

    updateLayerAnimations(time) {
        this.layerMeshes.forEach(mesh => {
            const userData = mesh.userData;

            // 脉冲效果
            const pulseIntensity = Math.sin(time * 2 + userData.pulsePhase) * 0.1 + 0.9;
            mesh.scale.setScalar(pulseIntensity);

            // 轻微旋转
            mesh.rotation.y += this.animationConfig.rotationSpeed * (userData.layerNum / 7);

            // 材质发光强度变化
            if (mesh.material.emissiveIntensity !== undefined) {
                mesh.material.emissiveIntensity = 0.1 + Math.sin(time + userData.pulsePhase) * 0.05;
            }
        });
    }

    updateParticleAnimations(time) {
        this.particleSystems.forEach(system => {
            if (system.userData.type === 'layerParticles') {
                const positions = system.geometry.attributes.position;
                const originalPositions = system.userData.originalPositions;

                for (let i = 0; i < positions.count; i++) {
                    const i3 = i * 3;

                    // 添加浮动效果
                    const floatOffset = Math.sin(time * 2 + i * 0.1) * 0.5;
                    positions.array[i3 + 1] = originalPositions[i3 + 1] + floatOffset;
                }

                positions.needsUpdate = true;
            } else if (system.userData.type === 'backgroundParticles') {
                // 背景粒子缓慢旋转
                system.rotation.y += 0.001;
            }
        });
    }

    updateDataPacketAnimations(time) {
        this.dataPackets.forEach(packet => {
            const userData = packet.userData;

            if (userData.currentLayer < userData.targetLayer) {
                userData.progress += userData.speed;

                if (userData.progress >= 1) {
                    userData.currentLayer++;
                    userData.progress = 0;

                    // 到达目标层后重置
                    if (userData.currentLayer > userData.targetLayer) {
                        userData.currentLayer = 1;
                        userData.targetLayer = 7;
                    }
                }

                // 插值计算位置
                const currentPos = this.layerData[userData.currentLayer].position;
                const nextPos = this.layerData[userData.currentLayer + 1]?.position || currentPos;

                packet.position.lerpVectors(
                    new THREE.Vector3(currentPos.x, currentPos.y, currentPos.z),
                    new THREE.Vector3(nextPos.x, nextPos.y, nextPos.z),
                    userData.progress
                );

                // 添加随机偏移
                packet.position.x += Math.sin(time * 5 + packet.id) * 0.5;
                packet.position.z += Math.cos(time * 5 + packet.id) * 0.5;
            }
        });
    }

    updateBioEffects(time) {
        this.bioEffects.forEach(effect => {
            if (effect.userData.type === 'bioDecoration') {
                const userData = effect.userData;

                // 轨道运动
                userData.orbitAngle += userData.orbitSpeed;
                const parentLayer = this.layerData[userData.parentLayer];

                effect.position.x = parentLayer.position.x + Math.cos(userData.orbitAngle) * userData.orbitRadius;
                effect.position.z = parentLayer.position.z + Math.sin(userData.orbitAngle) * userData.orbitRadius;

                // 上下浮动
                effect.position.y = parentLayer.position.y + Math.sin(time * 3 + userData.orbitAngle) * 0.5;

            } else if (effect.userData.type === 'energyRing') {
                const userData = effect.userData;

                // 能量环上下浮动
                effect.position.y = userData.originalY + Math.sin(time + userData.phase) * 2;

                // 旋转
                effect.rotation.z += 0.01;

                // 透明度变化
                effect.material.opacity = 0.1 + Math.sin(time * 2 + userData.phase) * 0.05;
            }
        });
    }

    updateLightingEffects(time) {
        // 更新动态点光源
        this.scene.children.forEach(child => {
            if (child.type === 'PointLight' && child.userData.layerNum) {
                const userData = child.userData;

                // 光源强度脉冲
                child.intensity = userData.originalIntensity +
                    Math.sin(time * 2 + userData.pulsePhase) * 0.3;

                // 轻微位置变化
                const layerData = this.layerData[userData.layerNum];
                child.position.x = layerData.position.x + Math.sin(time + userData.pulsePhase) * 0.5;
                child.position.z = layerData.position.z + Math.cos(time + userData.pulsePhase) * 0.5;
            }
        });
    }

    updatePerformanceUI() {
        // 更新FPS显示
        const fpsElement = document.getElementById('fps-counter');
        if (fpsElement) {
            fpsElement.textContent = `${this.stats.fps} FPS`;
        }

        // 更新内存使用显示
        const memoryElement = document.getElementById('memory-usage');
        if (memoryElement) {
            memoryElement.textContent = `${this.stats.memory}MB`;
        }

        // 更新性能指标
        const indicators = document.querySelectorAll('.indicator');
        indicators.forEach(indicator => {
            const value = indicator.querySelector('.indicator-value');
            if (indicator.id === 'bio-health') {
                const health = Math.max(90, 100 - (this.stats.fps < 30 ? 10 : 0));
                value.textContent = `${health}%`;
            } else if (indicator.id === 'data-flow') {
                const flow = (1.0 + Math.sin(performance.now() * 0.001) * 0.3).toFixed(1);
                value.textContent = `${flow}GB/s`;
            } else if (indicator.id === 'network-complexity') {
                const complexity = Math.min(95, 80 + this.layerMeshes.length * 2);
                value.textContent = `${complexity}%`;
            }
        });
    }

    // 重写原有的简单方法，使其调用新的创建方法
    createOSILayers() {
        Object.entries(this.layerData).forEach(([layerNum, data]) => {
            this.createLayerMesh(parseInt(layerNum), data);
            this.createLayerConnections(parseInt(layerNum), data);
            this.createLayerParticles(parseInt(layerNum), data);
        });

        // 创建数据包
        this.createDataPackets();

        console.log('🏗️ OSI层级创建完成');
    }

    initializeDataFlowSystem() {
        // 动态加载数据流系统
        if (typeof DataFlowSystem !== 'undefined') {
            this.dataFlowSystem = new DataFlowSystem(this.scene, this.layerData);
        } else {
            console.warn('⚠️ DataFlowSystem未加载，数据流功能不可用');
        }
    }

    initializeInteractionSystem() {
        // 动态加载交互系统
        if (typeof InteractionSystem !== 'undefined') {
            this.interactionSystem = new InteractionSystem(this);
        } else {
            console.warn('⚠️ InteractionSystem未加载，高级交互功能不可用');
        }
    }

    initializeInfoPanelSystem() {
        // 动态加载信息面板系统
        if (typeof InfoPanelSystem !== 'undefined') {
            this.infoPanelSystem = new InfoPanelSystem(this);
        } else {
            console.warn('⚠️ InfoPanelSystem未加载，信息面板功能不可用');
        }
    }

    focusOnLayer(layerNum) {
        console.log(`🎯 聚焦到第${layerNum}层`);

        this.focusedLayer = layerNum;
        const layerData = this.layerData[layerNum];

        if (layerData) {
            // 相机动画移动到目标层级
            const targetPosition = {
                x: layerData.position.x + 15,
                y: layerData.position.y + 10,
                z: layerData.position.z + 15
            };

            this.animateCamera(targetPosition, layerData.position);

            // 高亮目标层级
            this.highlightLayer(layerNum);

            // 更新信息面板
            this.updateLayerInfo(layerNum, layerData);
        }
    }

    animateCamera(targetPos, lookAtPos) {
        // 简单的相机动画（可以使用Tween.js来实现更平滑的动画）
        const duration = 1000; // 1秒
        const startPos = this.camera.position.clone();
        const startTime = performance.now();

        const animateStep = () => {
            const elapsed = performance.now() - startTime;
            const progress = Math.min(elapsed / duration, 1);

            // 使用缓动函数
            const easeProgress = 1 - Math.pow(1 - progress, 3);

            this.camera.position.lerpVectors(startPos, new THREE.Vector3(targetPos.x, targetPos.y, targetPos.z), easeProgress);
            this.camera.lookAt(lookAtPos.x, lookAtPos.y, lookAtPos.z);

            if (progress < 1) {
                requestAnimationFrame(animateStep);
            }
        };

        animateStep();
    }

    highlightLayer(layerNum) {
        // 重置所有层级的高亮状态
        this.layerMeshes.forEach(mesh => {
            if (mesh.userData.layerNum == layerNum) {
                // 高亮选中的层级
                mesh.material.emissiveIntensity = 0.3;
                mesh.scale.setScalar(1.1);
            } else {
                // 降低其他层级的亮度
                mesh.material.emissiveIntensity = 0.05;
                mesh.scale.setScalar(0.9);
            }
        });

        // 更新层级标签显示
        this.updateLayerLabels(layerNum);
    }

    updateLayerLabels(focusedLayer) {
        const labelsContainer = document.getElementById('layer-labels');
        if (!labelsContainer) return;

        // 清空现有标签
        labelsContainer.innerHTML = '';

        // 重新生成标签，突出显示聚焦的层级
        this.generateLayerLabels(focusedLayer);
    }

    updateLayerInfo(layerNum, layerData) {
        // 更新侧边栏的层级详细信息
        const layerDetailsContainer = document.getElementById('layer-details');
        if (!layerDetailsContainer) return;

        layerDetailsContainer.innerHTML = `
            <div class="layer-detail-card">
                <div class="layer-header">
                    <span class="layer-number" style="background-color: #${layerData.color.toString(16).padStart(6, '0')}">${layerNum}</span>
                    <div class="layer-title">
                        <h4>${layerData.name}</h4>
                        <p>${layerData.english}</p>
                    </div>
                </div>

                <div class="layer-description">
                    <p>${layerData.description}</p>
                    <p><strong>生物类比:</strong> ${layerData.bioAnalogy}</p>
                </div>

                <div class="layer-protocols">
                    <h5>主要协议:</h5>
                    <div class="protocol-tags">
                        ${layerData.protocols.map(protocol =>
                            `<span class="protocol-tag">${protocol}</span>`
                        ).join('')}
                    </div>
                </div>

                <div class="layer-devices">
                    <h5>相关设备:</h5>
                    <ul>
                        ${layerData.devices.map(device =>
                            `<li>${device}</li>`
                        ).join('')}
                    </ul>
                </div>
            </div>
        `;

        // 切换到层级详情标签页
        this.switchTab('layers');
    }

    switchTab(tabName) {
        // 切换标签页
        document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
        document.querySelectorAll('.tab-panel').forEach(panel => panel.classList.remove('active'));

        const targetBtn = document.querySelector(`[data-tab="${tabName}"]`);
        const targetPanel = document.querySelector(`.tab-panel[data-tab="${tabName}"]`);

        if (targetBtn && targetPanel) {
            targetBtn.classList.add('active');
            targetPanel.classList.add('active');
        }
    }

    updateCameraPosition() {
        // 根据当前视图更新摄像机位置
        switch (this.currentView) {
            case 'overview':
                this.camera.position.set(30, 30, 30);
                break;
            case 'layers':
                this.camera.position.set(0, 50, 30);
                break;
            case 'network':
                this.camera.position.set(50, 20, 0);
                break;
        }
        this.camera.lookAt(0, 10, 0);
    }
    
    onWindowResize() {
        const container = document.getElementById('three-canvas');
        if (!container || !this.renderer || !this.camera) return;
        
        const rect = container.getBoundingClientRect();
        const width = rect.width || 800;
        const height = rect.height || 600;
        
        this.camera.aspect = width / height;
        this.camera.updateProjectionMatrix();
        this.renderer.setSize(width, height);
    }
    
    focusOnLayer(layerNum) {
        console.log(`聚焦到第${layerNum}层`);
        // 这里可以添加聚焦动画
    }
    
    updateCameraPosition() {
        // 根据当前视图更新摄像机位置
        switch (this.currentView) {
            case 'overview':
                this.camera.position.set(30, 30, 30);
                break;
            case 'layers':
                this.camera.position.set(0, 50, 30);
                break;
            case 'network':
                this.camera.position.set(50, 20, 0);
                break;
        }
        this.camera.lookAt(0, 10, 0);
    }
    
    toggleSimulation() {
        this.isSimulating = !this.isSimulating;
        const btn = document.getElementById('start-simulation');
        if (btn) {
            const icon = btn.querySelector('i');
            const text = btn.querySelector('span');
            if (this.isSimulating) {
                icon.className = 'fas fa-pause';
                text.textContent = '暂停生命流';

                // 启动数据流
                if (this.dataFlowSystem) {
                    this.dataFlowSystem.startDataFlow();
                }
            } else {
                icon.className = 'fas fa-play';
                text.textContent = '启动生命流';

                // 停止数据流
                if (this.dataFlowSystem) {
                    this.dataFlowSystem.stopDataFlow();
                }
            }
        }

        // 更新状态显示
        this.updateSimulationStatus();
    }
    
    resetSimulation() {
        this.isSimulating = false;

        // 重置数据流系统
        if (this.dataFlowSystem) {
            this.dataFlowSystem.stopDataFlow();
        }

        // 重置所有动画状态
        this.layerMeshes.forEach(mesh => {
            mesh.scale.setScalar(1);
            mesh.rotation.set(0, 0, 0);
            if (mesh.material.emissiveIntensity !== undefined) {
                mesh.material.emissiveIntensity = 0.1;
            }
        });

        // 更新UI
        const btn = document.getElementById('start-simulation');
        if (btn) {
            const icon = btn.querySelector('i');
            const text = btn.querySelector('span');
            icon.className = 'fas fa-play';
            text.textContent = '启动生命流';
        }

        this.updateSimulationStatus();
        console.log('🔄 仿真已重置');
    }

    updateSimulationStatus() {
        const statusElement = document.getElementById('simulation-status');
        if (statusElement) {
            if (this.isSimulating) {
                statusElement.textContent = '生态系统运行中';
            } else {
                statusElement.textContent = '生态系统就绪';
            }
        }

        // 更新交互模式显示
        const modeElement = document.getElementById('interaction-mode');
        if (modeElement) {
            const activeMode = document.querySelector('.interaction-mode.active');
            if (activeMode) {
                const modeName = activeMode.querySelector('span').textContent;
                modeElement.textContent = `交互: ${modeName}模式`;
            }
        }
    }
    
    switchTab(tabName) {
        // 切换标签页
        document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
        document.querySelectorAll('.tab-panel').forEach(panel => panel.classList.remove('active'));
        
        document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');
        document.querySelector(`.tab-panel[data-tab="${tabName}"]`).classList.add('active');
    }
    
    showError(message) {
        console.error(message);
        alert(message); // 简单的错误显示，可以后续改进
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    window.osiSimulator = new OSI3DSimulator();
    console.log('✅ OSI 3D模拟器已启动');
});
