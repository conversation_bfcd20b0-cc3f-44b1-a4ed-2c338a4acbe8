#!/bin/bash

# Telegram 频道下载器安装脚本

echo "🚀 Telegram 频道下载器安装脚本"
echo "=================================="

# 检查Python版本
echo "📋 检查Python环境..."
if command -v python3 &> /dev/null; then
    PYTHON_VERSION=$(python3 --version 2>&1 | awk '{print $2}')
    echo "✅ Python版本: $PYTHON_VERSION"
else
    echo "❌ 未找到Python3，请先安装Python3"
    exit 1
fi

# 检查pip
if command -v pip3 &> /dev/null; then
    echo "✅ pip3 已安装"
else
    echo "❌ 未找到pip3，请先安装pip3"
    exit 1
fi

# 创建虚拟环境 (可选)
read -p "🤔 是否创建Python虚拟环境? (推荐) [y/N]: " create_venv
if [[ $create_venv =~ ^[Yy]$ ]]; then
    echo "📦 创建虚拟环境..."
    python3 -m venv telegram_downloader_env
    source telegram_downloader_env/bin/activate
    echo "✅ 虚拟环境已激活"
fi

# 升级pip
echo "⬆️  升级pip..."
pip3 install --upgrade pip

# 安装依赖
echo "📦 安装Python依赖包..."
pip3 install -r requirements.txt

if [ $? -eq 0 ]; then
    echo "✅ 依赖安装成功"
else
    echo "❌ 依赖安装失败"
    exit 1
fi

# 创建配置文件
if [ ! -f "config.py" ]; then
    echo "📝 创建配置文件..."
    cp config_example.py config.py
    echo "✅ 配置文件已创建: config.py"
    echo "⚠️  请编辑 config.py 文件，填入你的API密钥和配置"
else
    echo "✅ 配置文件已存在: config.py"
fi

# 创建下载目录
mkdir -p downloads
echo "✅ 下载目录已创建: downloads/"

# 设置执行权限
chmod +x telegram_channel_downloader.py
chmod +x start.py

echo ""
echo "🎉 安装完成！"
echo ""
echo "📋 下一步操作:"
echo "1. 编辑 config.py 文件，填入你的配置信息"
echo "2. 运行: python3 telegram_channel_downloader.py"
echo "   或者: python3 start.py"
echo ""
echo "📚 更多信息请查看 README.md"

# 检查gclone (用于Google Drive上传)
if command -v gclone &> /dev/null; then
    echo "✅ gclone 已安装 (支持Google Drive上传)"
else
    echo "⚠️  gclone 未安装 (如需Google Drive上传功能请安装gclone)"
fi

echo ""
echo "🔗 有用的链接:"
echo "- 获取API密钥: https://my.telegram.org/apps"
echo "- 创建Bot: https://t.me/BotFather"
echo "- 获取用户ID: https://t.me/userinfobot"
