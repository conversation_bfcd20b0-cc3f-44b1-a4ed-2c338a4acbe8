// 信息面板系统
class InfoPanelSystem {
    constructor(simulator) {
        this.simulator = simulator;
        this.currentTab = 'overview';
        this.updateInterval = null;
        this.chartData = {
            fps: [],
            memory: [],
            packets: [],
            timestamps: []
        };
        
        this.init();
    }
    
    init() {
        this.setupTabSwitching();
        this.startDataCollection();

        console.log('📊 信息面板系统初始化完成');
    }
    
    setupTabSwitching() {
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const targetTab = e.target.closest('.tab-btn').dataset.tab;
                this.switchTab(targetTab);
            });
        });
    }
    
    switchTab(tabName) {
        // 更新按钮状态
        document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
        document.querySelectorAll('.tab-panel').forEach(panel => panel.classList.remove('active'));
        
        const targetBtn = document.querySelector(`[data-tab="${tabName}"]`);
        const targetPanel = document.querySelector(`.tab-panel[data-tab="${tabName}"]`);
        
        if (targetBtn && targetPanel) {
            targetBtn.classList.add('active');
            targetPanel.classList.add('active');
            this.currentTab = tabName;
            
            // 根据标签页加载相应内容
            this.loadTabContent(tabName);
        }
    }
    
    loadTabContent(tabName) {
        switch (tabName) {
            case 'overview':
                this.updateOverviewContent();
                break;
            case 'layers':
                this.updateLayersContent();
                break;
            case 'analytics':
                this.updateAnalyticsContent();
                break;
            case 'settings':
                this.updateSettingsContent();
                break;
        }
    }
    
    updateOverviewContent() {
        // 更新概览统计
        const stats = {
            layers: Object.keys(this.simulator.layerData).length,
            dataFlow: this.simulator.dataFlowSystem ? this.simulator.dataFlowSystem.getActivePacketCount() : 0,
            devices: Object.values(this.simulator.layerData).reduce((total, layer) => total + layer.devices.length, 0)
        };
        
        // 更新统计卡片
        const statCards = document.querySelectorAll('.tab-panel[data-tab="overview"] .stat-item');
        if (statCards.length >= 3) {
            statCards[0].querySelector('div:first-child').textContent = stats.layers;
            statCards[1].querySelector('div:first-child').textContent = stats.dataFlow > 0 ? '∞' : '0';
            statCards[2].querySelector('div:first-child').textContent = stats.devices;
        }
        
        // 更新交互指南
        this.updateInteractionGuide();
    }
    
    updateInteractionGuide() {
        const guideContainer = document.querySelector('.interaction-guide');
        if (!guideContainer) return;
        
        const currentMode = this.simulator.interactionSystem ? this.simulator.interactionSystem.currentMode : 'touch';
        
        let guideHTML = '';
        switch (currentMode) {
            case 'voice':
                guideHTML = `
                    <h4>语音控制指南</h4>
                    <div class="guide-items">
                        <div class="guide-item">
                            <i class="fas fa-microphone"></i>
                            <div>
                                <div>"聚焦第X层"</div>
                                <div>聚焦到指定OSI层级</div>
                            </div>
                        </div>
                        <div class="guide-item">
                            <i class="fas fa-play"></i>
                            <div>
                                <div>"启动仿真"</div>
                                <div>开始数据流动画</div>
                            </div>
                        </div>
                        <div class="guide-item">
                            <i class="fas fa-expand"></i>
                            <div>
                                <div>"全屏模式"</div>
                                <div>切换全屏显示</div>
                            </div>
                        </div>
                    </div>
                `;
                break;
            case 'gesture':
                guideHTML = `
                    <h4>手势控制指南</h4>
                    <div class="guide-items">
                        <div class="guide-item">
                            <i class="fas fa-hand-point-right"></i>
                            <div>
                                <div>右滑手势</div>
                                <div>切换到下一个视角</div>
                            </div>
                        </div>
                        <div class="guide-item">
                            <i class="fas fa-hand-point-up"></i>
                            <div>
                                <div>上滑手势</div>
                                <div>进入聚焦模式</div>
                            </div>
                        </div>
                        <div class="guide-item">
                            <i class="fas fa-hand-point-down"></i>
                            <div>
                                <div>下滑手势</div>
                                <div>重置视角</div>
                            </div>
                        </div>
                    </div>
                `;
                break;
            default: // touch
                guideHTML = `
                    <h4>触控操作指南</h4>
                    <div class="guide-items">
                        <div class="guide-item">
                            <i class="fas fa-mouse"></i>
                            <div>
                                <div>鼠标拖拽</div>
                                <div>旋转3D生态系统</div>
                            </div>
                        </div>
                        <div class="guide-item">
                            <i class="fas fa-scroll"></i>
                            <div>
                                <div>滚轮缩放</div>
                                <div>深入生物层级</div>
                            </div>
                        </div>
                        <div class="guide-item">
                            <i class="fas fa-hand-point-up"></i>
                            <div>
                                <div>双击生物体</div>
                                <div>查看详细信息</div>
                            </div>
                        </div>
                    </div>
                `;
                break;
        }
        
        guideContainer.innerHTML = guideHTML;
    }
    
    updateLayersContent() {
        const layerDetailsContainer = document.getElementById('layer-details');
        if (!layerDetailsContainer) return;
        
        let detailsHTML = '';
        Object.entries(this.simulator.layerData).reverse().forEach(([layerNum, data]) => {
            const isActive = this.simulator.focusedLayer == layerNum;
            detailsHTML += `
                <div class="layer-summary ${isActive ? 'active' : ''}" data-layer="${layerNum}">
                    <div class="layer-summary-header">
                        <span class="layer-number" style="background-color: #${data.color.toString(16).padStart(6, '0')}">${layerNum}</span>
                        <div class="layer-summary-info">
                            <h4>${data.name}</h4>
                            <p>${data.english}</p>
                        </div>
                        <button class="focus-btn" onclick="window.osiSimulator.focusOnLayer(${layerNum})">
                            <i class="fas fa-search-plus"></i>
                        </button>
                    </div>
                    <p class="layer-summary-desc">${data.description}</p>
                    <div class="layer-protocols">
                        <small>协议: ${data.protocols.slice(0, 3).join(', ')}${data.protocols.length > 3 ? '...' : ''}</small>
                    </div>
                </div>
            `;
        });
        
        layerDetailsContainer.innerHTML = detailsHTML;
    }
    
    updateAnalyticsContent() {
        // 更新实时指标
        this.updateMetrics();
        
        // 更新图表数据
        this.updateChartData();
        
        // 绘制简单图表
        this.drawPerformanceChart();
    }
    
    updateMetrics() {
        const metrics = {
            throughput: (1.0 + Math.sin(performance.now() * 0.001) * 0.5).toFixed(1),
            latency: Math.round(10 + Math.random() * 10),
            efficiency: Math.round(90 + Math.random() * 10),
            security: ['优秀', '良好', '正常'][Math.floor(Math.random() * 3)]
        };
        
        // 更新UI
        const elements = {
            'data-throughput': `${metrics.throughput} GB/s`,
            'network-latency': `${metrics.latency}ms`,
            'processing-efficiency': `${metrics.efficiency}%`,
            'security-status': metrics.security
        };
        
        Object.entries(elements).forEach(([id, value]) => {
            const element = document.getElementById(id);
            if (element) element.textContent = value;
        });
    }
    
    updateChartData() {
        const now = Date.now();
        const fps = this.simulator.stats.fps;
        const memory = this.simulator.stats.memory;
        const packets = this.simulator.dataFlowSystem ? this.simulator.dataFlowSystem.getActivePacketCount() : 0;
        
        // 保持最近50个数据点
        const maxPoints = 50;
        
        this.chartData.timestamps.push(now);
        this.chartData.fps.push(fps);
        this.chartData.memory.push(memory);
        this.chartData.packets.push(packets);
        
        if (this.chartData.timestamps.length > maxPoints) {
            this.chartData.timestamps.shift();
            this.chartData.fps.shift();
            this.chartData.memory.shift();
            this.chartData.packets.shift();
        }
    }
    
    drawPerformanceChart() {
        const chartContainer = document.querySelector('.chart-placeholder');
        if (!chartContainer) return;
        
        // 简单的ASCII风格图表
        const fpsData = this.chartData.fps.slice(-20); // 最近20个点
        if (fpsData.length < 2) return;
        
        const maxFps = Math.max(...fpsData);
        const minFps = Math.min(...fpsData);
        const range = maxFps - minFps || 1;
        
        let chartHTML = '<div class="ascii-chart">';
        chartHTML += '<div class="chart-title">FPS 趋势图</div>';
        chartHTML += '<div class="chart-bars">';
        
        fpsData.forEach((fps) => {
            const height = ((fps - minFps) / range) * 100;
            const color = fps > 50 ? '#10b981' : fps > 30 ? '#f59e0b' : '#ef4444';
            chartHTML += `
                <div class="chart-bar" style="height: ${height}%; background-color: ${color};" 
                     title="FPS: ${fps}"></div>
            `;
        });
        
        chartHTML += '</div>';
        chartHTML += `<div class="chart-info">范围: ${minFps}-${maxFps} FPS</div>`;
        chartHTML += '</div>';
        
        chartContainer.innerHTML = chartHTML;
    }
    
    updateSettingsContent() {
        const settingsPanel = document.querySelector('.tab-panel[data-tab="settings"]');
        if (!settingsPanel) return;
        
        if (!settingsPanel.querySelector('.settings-content')) {
            this.createSettingsInterface(settingsPanel);
        }
    }
    
    createSettingsInterface(container) {
        const settingsHTML = `
            <div class="settings-content">
                <div class="settings-section">
                    <h4><i class="fas fa-eye"></i> 视觉设置</h4>
                    <div class="setting-item">
                        <label>渲染质量</label>
                        <select id="render-quality">
                            <option value="high">高质量</option>
                            <option value="medium" selected>中等质量</option>
                            <option value="low">低质量</option>
                        </select>
                    </div>
                    <div class="setting-item">
                        <label>粒子效果</label>
                        <input type="checkbox" id="particles-enabled" checked>
                    </div>
                    <div class="setting-item">
                        <label>阴影效果</label>
                        <input type="checkbox" id="shadows-enabled" checked>
                    </div>
                </div>
                
                <div class="settings-section">
                    <h4><i class="fas fa-cog"></i> 性能设置</h4>
                    <div class="setting-item">
                        <label>目标帧率</label>
                        <select id="target-fps">
                            <option value="30">30 FPS</option>
                            <option value="60" selected>60 FPS</option>
                            <option value="120">120 FPS</option>
                        </select>
                    </div>
                    <div class="setting-item">
                        <label>自动优化</label>
                        <input type="checkbox" id="auto-optimize" checked>
                    </div>
                </div>
                
                <div class="settings-section">
                    <h4><i class="fas fa-volume-up"></i> 音频设置</h4>
                    <div class="setting-item">
                        <label>主音量</label>
                        <input type="range" id="master-volume" min="0" max="100" value="70">
                    </div>
                    <div class="setting-item">
                        <label>音效开关</label>
                        <input type="checkbox" id="sound-enabled" checked>
                    </div>
                </div>
                
                <div class="settings-section">
                    <h4><i class="fas fa-universal-access"></i> 可访问性</h4>
                    <div class="setting-item">
                        <label>减少动画</label>
                        <input type="checkbox" id="reduce-motion">
                    </div>
                    <div class="setting-item">
                        <label>高对比度</label>
                        <input type="checkbox" id="high-contrast">
                    </div>
                    <div class="setting-item">
                        <label>大字体</label>
                        <input type="checkbox" id="large-font">
                    </div>
                </div>
                
                <div class="settings-actions">
                    <button class="btn secondary" onclick="window.osiSimulator.infoPanelSystem.resetSettings()">
                        <i class="fas fa-undo"></i> 重置设置
                    </button>
                    <button class="btn primary" onclick="window.osiSimulator.infoPanelSystem.saveSettings()">
                        <i class="fas fa-save"></i> 保存设置
                    </button>
                </div>
            </div>
        `;
        
        container.innerHTML = settingsHTML;
        this.bindSettingsEvents();
    }
    
    bindSettingsEvents() {
        // 绑定设置项事件
        document.getElementById('render-quality')?.addEventListener('change', (e) => {
            this.updateRenderQuality(e.target.value);
        });
        
        document.getElementById('particles-enabled')?.addEventListener('change', (e) => {
            this.toggleParticles(e.target.checked);
        });
        
        document.getElementById('shadows-enabled')?.addEventListener('change', (e) => {
            this.toggleShadows(e.target.checked);
        });
        
        // 其他设置项...
    }
    
    updateRenderQuality(quality) {
        const renderer = this.simulator.renderer;
        switch (quality) {
            case 'high':
                renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
                break;
            case 'medium':
                renderer.setPixelRatio(1);
                break;
            case 'low':
                renderer.setPixelRatio(0.5);
                break;
        }
        console.log(`🎨 渲染质量设置为: ${quality}`);
    }
    
    toggleParticles(enabled) {
        this.simulator.particleSystems.forEach(system => {
            system.visible = enabled;
        });
        console.log(`✨ 粒子效果: ${enabled ? '开启' : '关闭'}`);
    }
    
    toggleShadows(enabled) {
        this.simulator.renderer.shadowMap.enabled = enabled;
        console.log(`🌑 阴影效果: ${enabled ? '开启' : '关闭'}`);
    }
    
    saveSettings() {
        const settings = {
            renderQuality: document.getElementById('render-quality')?.value,
            particlesEnabled: document.getElementById('particles-enabled')?.checked,
            shadowsEnabled: document.getElementById('shadows-enabled')?.checked,
            targetFps: document.getElementById('target-fps')?.value,
            autoOptimize: document.getElementById('auto-optimize')?.checked,
            masterVolume: document.getElementById('master-volume')?.value,
            soundEnabled: document.getElementById('sound-enabled')?.checked,
            reduceMotion: document.getElementById('reduce-motion')?.checked,
            highContrast: document.getElementById('high-contrast')?.checked,
            largeFont: document.getElementById('large-font')?.checked
        };
        
        localStorage.setItem('osi-simulator-settings', JSON.stringify(settings));
        console.log('💾 设置已保存');
        
        // 显示保存成功提示
        this.showNotification('设置已保存', 'success');
    }
    
    resetSettings() {
        localStorage.removeItem('osi-simulator-settings');
        this.loadDefaultSettings();
        console.log('🔄 设置已重置');
        
        this.showNotification('设置已重置', 'info');
    }
    
    loadDefaultSettings() {
        // 加载默认设置
        const defaults = {
            renderQuality: 'medium',
            particlesEnabled: true,
            shadowsEnabled: true,
            targetFps: '60',
            autoOptimize: true,
            masterVolume: '70',
            soundEnabled: true,
            reduceMotion: false,
            highContrast: false,
            largeFont: false
        };
        
        Object.entries(defaults).forEach(([key, value]) => {
            const element = document.getElementById(key.replace(/([A-Z])/g, '-$1').toLowerCase());
            if (element) {
                if (element.type === 'checkbox') {
                    element.checked = value;
                } else {
                    element.value = value;
                }
            }
        });
    }
    
    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.textContent = message;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 12px 20px;
            border-radius: 6px;
            color: white;
            font-size: 14px;
            z-index: 10000;
            animation: slideInRight 0.3s ease-out;
            background: ${type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : '#3b82f6'};
        `;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.style.animation = 'slideOutRight 0.3s ease-in';
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, 3000);
    }
    
    startDataCollection() {
        // 每秒更新一次数据
        this.updateInterval = setInterval(() => {
            if (this.currentTab === 'analytics') {
                this.updateAnalyticsContent();
            }
        }, 1000);
    }
    
    destroy() {
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
        }
    }
}
