<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>布局修复测试 - OSI 3D 模拟器</title>
    <style>
        /* 基础重置 */
        * { margin: 0; padding: 0; box-sizing: border-box; }
        
        /* 简化版变量 */
        :root {
            --primary-color: #667eea;
            --bg-dark: #0a0b1e;
            --glass-bg: rgba(255, 255, 255, 0.08);
            --border-color: rgba(255, 255, 255, 0.15);
            --text-primary: #f8fafc;
            --text-secondary: #e2e8f0;
            --spacing-sm: 0.5rem;
            --spacing-md: 1rem;
            --spacing-lg: 1.5rem;
            --spacing-xl: 2rem;
        }
        
        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, var(--bg-dark) 0%, #1e1b2e 100%);
            color: var(--text-primary);
            height: 100vh;
            overflow: hidden;
        }
        
        .app-container {
            display: flex;
            flex-direction: column;
            height: 100vh;
        }
        
        /* 顶部导航 */
        .top-nav {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid var(--border-color);
            padding: var(--spacing-md) var(--spacing-xl);
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-shrink: 0;
            height: 70px;
        }
        
        .brand {
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
        }
        
        .brand-icon {
            font-size: 1.5rem;
            color: var(--primary-color);
        }
        
        .nav-actions {
            display: flex;
            gap: var(--spacing-sm);
        }
        
        .nav-btn {
            width: 40px;
            height: 40px;
            background: var(--glass-bg);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            color: var(--text-secondary);
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .nav-btn:hover {
            background: rgba(255, 255, 255, 0.12);
            border-color: var(--primary-color);
        }
        
        /* 控制面板 */
        .control-panel {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid var(--border-color);
            padding: var(--spacing-lg) var(--spacing-xl);
            display: flex;
            gap: var(--spacing-lg);
            flex-wrap: wrap;
            align-items: flex-start;
            flex-shrink: 0;
            min-height: 120px;
        }
        
        .panel-section {
            flex: 1;
            min-width: 200px;
        }
        
        .section-title {
            font-size: 0.875rem;
            font-weight: 600;
            color: var(--text-secondary);
            margin-bottom: var(--spacing-md);
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }
        
        .control-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
            gap: var(--spacing-sm);
        }
        
        .control-btn {
            padding: var(--spacing-sm) var(--spacing-md);
            background: var(--glass-bg);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            color: var(--text-secondary);
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            font-size: 0.875rem;
        }
        
        .control-btn:hover {
            background: rgba(255, 255, 255, 0.12);
            color: var(--text-primary);
        }
        
        .control-btn.active {
            background: var(--primary-color);
            border-color: transparent;
            color: white;
        }
        
        /* 主工作区 */
        .main-workspace {
            display: flex;
            flex: 1;
            overflow: hidden;
            height: calc(100vh - 190px);
        }
        
        .scene-container {
            flex: 1;
            background: linear-gradient(45deg, #1e1b2e, #2d1b69);
            position: relative;
            min-width: 0;
            overflow: hidden;
        }
        
        .scene-content {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            color: var(--text-secondary);
        }
        
        /* 信息面板 */
        .info-panel {
            width: 360px;
            min-width: 300px;
            max-width: 400px;
            background: var(--glass-bg);
            backdrop-filter: blur(10px);
            border-left: 1px solid var(--border-color);
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }
        
        .panel-header {
            padding: var(--spacing-lg);
            border-bottom: 1px solid var(--border-color);
            flex-shrink: 0;
        }
        
        .panel-content {
            flex: 1;
            padding: var(--spacing-lg);
            overflow-y: auto;
        }
        
        /* 底部状态栏 */
        .status-bar {
            background: var(--glass-bg);
            backdrop-filter: blur(10px);
            border-top: 1px solid var(--border-color);
            padding: var(--spacing-md) var(--spacing-xl);
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-shrink: 0;
            font-size: 0.875rem;
            height: 50px;
        }
        
        .status-group {
            display: flex;
            gap: var(--spacing-lg);
            align-items: center;
        }
        
        .status-item {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            color: var(--text-secondary);
        }
        
        /* 响应式设计 */
        @media (max-width: 1024px) {
            .info-panel {
                width: 320px;
                min-width: 280px;
            }
            
            .control-panel {
                flex-direction: column;
                gap: var(--spacing-md);
                padding: var(--spacing-md) var(--spacing-lg);
            }
            
            .panel-section {
                min-width: auto;
                text-align: center;
            }
        }
        
        @media (max-width: 768px) {
            .main-workspace {
                flex-direction: column;
                height: auto;
            }
            
            .info-panel {
                width: 100%;
                height: 300px;
                max-height: 40vh;
                order: -1;
                border-left: none;
                border-bottom: 1px solid var(--border-color);
            }
            
            .scene-container {
                flex: 1;
                min-height: 400px;
            }
            
            .control-panel {
                padding: var(--spacing-md);
                min-height: auto;
            }
            
            .control-grid {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .status-bar {
                flex-direction: column;
                gap: var(--spacing-sm);
                height: auto;
                padding: var(--spacing-sm);
            }
        }
        
        @media (max-width: 480px) {
            .control-grid {
                grid-template-columns: 1fr;
            }
            
            .top-nav {
                padding: var(--spacing-md);
                flex-wrap: wrap;
                height: auto;
                min-height: 60px;
            }
            
            .nav-actions .nav-btn {
                width: 36px;
                height: 36px;
            }
        }
        
        /* 显示辅助信息 */
        .layout-info {
            position: absolute;
            top: 20px;
            left: 20px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 10px;
            border-radius: 8px;
            font-size: 0.75rem;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <div class="app-container">
        <!-- 顶部导航 -->
        <nav class="top-nav">
            <div class="brand">
                <div class="brand-icon">🧬</div>
                <div>
                    <div style="font-weight: 700;">Network Poetry</div>
                    <div style="font-size: 0.75rem; opacity: 0.8;">生物网络诗学</div>
                </div>
            </div>
            <div class="nav-actions">
                <button class="nav-btn">⚙️</button>
                <button class="nav-btn">❓</button>
                <button class="nav-btn">⛶</button>
            </div>
        </nav>

        <!-- 控制面板 -->
        <header class="control-panel">
            <div class="panel-section">
                <h3 class="section-title">
                    👁️ 视角控制
                </h3>
                <div class="control-grid">
                    <button class="control-btn active">🌍 总览</button>
                    <button class="control-btn">📚 层级</button>
                    <button class="control-btn">🖥️ 数据中心</button>
                    <button class="control-btn">🔗 网络</button>
                </div>
            </div>
            
            <div class="panel-section">
                <h3 class="section-title">
                    ▶️ 仿生模拟
                </h3>
                <div class="control-grid">
                    <button class="control-btn">🚀 启动</button>
                    <button class="control-btn">🔄 重置</button>
                    <button class="control-btn">🏷️ 标签</button>
                </div>
            </div>
            
            <div class="panel-section">
                <h3 class="section-title">
                    ✨ 交互模式
                </h3>
                <div class="control-grid">
                    <button class="control-btn active">👆 触控</button>
                    <button class="control-btn">🎤 语音</button>
                    <button class="control-btn">✋ 手势</button>
                    <button class="control-btn">👀 凝视</button>
                </div>
            </div>
        </header>

        <!-- 主要工作区 -->
        <main class="main-workspace">
            <!-- 3D场景容器 -->
            <div class="scene-container">
                <div class="scene-content">
                    🧬 OSI 3D 生物仿生可视化区域
                </div>
                
                <!-- 布局信息显示 -->
                <div class="layout-info">
                    <div>视窗: <span id="viewport-size">计算中...</span></div>
                    <div>布局: <span id="layout-mode">桌面版</span></div>
                    <div>状态: <span style="color: #10b981;">✅ 布局正常</span></div>
                </div>
            </div>

            <!-- 信息面板 -->
            <aside class="info-panel">
                <div class="panel-header">
                    <h3>系统概览</h3>
                </div>
                <div class="panel-content">
                    <div style="margin-bottom: 1rem;">
                        <h4 style="margin-bottom: 0.5rem;">🌱 生态系统状态</h4>
                        <div style="font-size: 0.875rem; color: var(--text-secondary);">
                            <div>• 生物层级: 7层</div>
                            <div>• 数据流: 活跃</div>
                            <div>• 网络健康: 98%</div>
                        </div>
                    </div>
                    
                    <div style="margin-bottom: 1rem;">
                        <h4 style="margin-bottom: 0.5rem;">🎮 交互指南</h4>
                        <div style="font-size: 0.875rem; color: var(--text-secondary);">
                            <div>• 鼠标拖拽: 旋转视角</div>
                            <div>• 滚轮: 缩放场景</div>
                            <div>• 点击: 查看详情</div>
                        </div>
                    </div>
                    
                    <div>
                        <h4 style="margin-bottom: 0.5rem;">📊 性能指标</h4>
                        <div style="font-size: 0.875rem; color: var(--text-secondary);">
                            <div>• FPS: 60</div>
                            <div>• 内存: 128MB</div>
                            <div>• 延迟: <5ms</div>
                        </div>
                    </div>
                </div>
            </aside>
        </main>

        <!-- 底部状态栏 -->
        <footer class="status-bar">
            <div class="status-group">
                <div class="status-item">
                    <span>👁️ 视角: 生态总览</span>
                </div>
                <div class="status-item">
                    <span>👆 交互: 触控模式</span>
                </div>
            </div>
            
            <div class="status-item">
                <span>🟢 系统就绪</span>
            </div>
            
            <div class="status-group">
                <div class="status-item">
                    <span>⏱️ 00:00</span>
                </div>
                <div class="status-item">
                    <span>📊 60 FPS</span>
                </div>
            </div>
        </footer>
    </div>

    <script>
        // 实时显示视窗信息
        function updateViewportInfo() {
            const viewportSize = document.getElementById('viewport-size');
            const layoutMode = document.getElementById('layout-mode');
            
            if (viewportSize) {
                viewportSize.textContent = `${window.innerWidth}×${window.innerHeight}`;
            }
            
            if (layoutMode) {
                if (window.innerWidth <= 480) {
                    layoutMode.textContent = '手机版';
                } else if (window.innerWidth <= 768) {
                    layoutMode.textContent = '平板版';
                } else if (window.innerWidth <= 1024) {
                    layoutMode.textContent = '小屏版';
                } else {
                    layoutMode.textContent = '桌面版';
                }
            }
        }
        
        // 初始化和监听窗口变化
        updateViewportInfo();
        window.addEventListener('resize', updateViewportInfo);
        
        // 模拟按钮交互
        document.querySelectorAll('.control-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                // 移除同组其他按钮的active状态
                const section = this.closest('.panel-section');
                section.querySelectorAll('.control-btn').forEach(b => b.classList.remove('active'));
                
                // 添加当前按钮的active状态
                this.classList.add('active');
            });
        });
        
        console.log('✅ 布局修复测试页面已加载');
        console.log('📱 请尝试调整浏览器窗口大小来测试响应式布局');
    </script>
</body>
</html>
