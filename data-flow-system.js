// OSI数据流动画系统
class DataFlowSystem {
    constructor(scene, layerData) {
        this.scene = scene;
        this.layerData = layerData;
        this.dataPackets = [];
        this.flowPaths = [];
        this.encapsulationEffects = [];
        this.isActive = false;
        
        // 数据包类型配置
        this.packetTypes = {
            HTTP: { color: 0x4facfe, size: 0.3, speed: 0.02 },
            TCP: { color: 0x69F0AE, size: 0.25, speed: 0.025 },
            IP: { color: 0xFFD54F, size: 0.2, speed: 0.03 },
            ETHERNET: { color: 0xFF5722, size: 0.15, speed: 0.035 }
        };
        
        this.init();
    }
    
    init() {
        this.createFlowPaths();
        this.setupDataPacketPool();
        console.log('📦 数据流系统初始化完成');
    }
    
    createFlowPaths() {
        // 创建数据流路径
        for (let i = 1; i < 7; i++) {
            const startLayer = this.layerData[i];
            const endLayer = this.layerData[i + 1];
            
            if (startLayer && endLayer) {
                this.createFlowPath(startLayer, endLayer, i);
            }
        }
    }
    
    createFlowPath(startLayer, endLayer, layerIndex) {
        // 创建上行和下行路径
        const upPath = this.createPath(startLayer.position, endLayer.position, 'up');
        const downPath = this.createPath(endLayer.position, startLayer.position, 'down');
        
        this.flowPaths.push({
            up: upPath,
            down: downPath,
            startLayer: layerIndex,
            endLayer: layerIndex + 1
        });
    }
    
    createPath(start, end, direction) {
        // 创建曲线路径
        const curve = new THREE.CatmullRomCurve3([
            new THREE.Vector3(start.x, start.y, start.z),
            new THREE.Vector3(
                start.x + (end.x - start.x) * 0.3,
                start.y + (end.y - start.y) * 0.5,
                start.z + (direction === 'up' ? 2 : -2)
            ),
            new THREE.Vector3(
                start.x + (end.x - start.x) * 0.7,
                start.y + (end.y - start.y) * 0.8,
                start.z + (direction === 'up' ? -2 : 2)
            ),
            new THREE.Vector3(end.x, end.y, end.z)
        ]);
        
        // 可视化路径（调试用）
        if (false) { // 设为true可显示路径线
            const points = curve.getPoints(50);
            const geometry = new THREE.BufferGeometry().setFromPoints(points);
            const material = new THREE.LineBasicMaterial({ 
                color: 0x4facfe, 
                transparent: true, 
                opacity: 0.3 
            });
            const line = new THREE.Line(geometry, material);
            this.scene.add(line);
        }
        
        return curve;
    }
    
    setupDataPacketPool() {
        // 创建数据包对象池
        const poolSize = 20;
        
        for (let i = 0; i < poolSize; i++) {
            const packet = this.createDataPacket();
            packet.visible = false;
            this.dataPackets.push(packet);
        }
    }
    
    createDataPacket() {
        const geometry = new THREE.SphereGeometry(0.2, 12, 8);
        const material = new THREE.MeshPhongMaterial({
            color: 0x4facfe,
            transparent: true,
            opacity: 0.8,
            emissive: 0x4facfe,
            emissiveIntensity: 0.2
        });
        
        const packet = new THREE.Mesh(geometry, material);
        packet.userData = {
            type: 'dataPacket',
            isActive: false,
            currentPath: null,
            progress: 0,
            speed: 0.02,
            packetType: 'HTTP',
            encapsulationLayers: [],
            trail: []
        };
        
        this.scene.add(packet);
        return packet;
    }
    
    startDataFlow() {
        this.isActive = true;
        this.schedulePacketTransmission();
        console.log('🚀 数据流动画已启动');
    }
    
    stopDataFlow() {
        this.isActive = false;
        console.log('⏹️ 数据流动画已停止');
    }
    
    schedulePacketTransmission() {
        if (!this.isActive) return;
        
        // 随机发送数据包
        const delay = 500 + Math.random() * 1500; // 0.5-2秒间隔
        
        setTimeout(() => {
            this.sendDataPacket();
            this.schedulePacketTransmission();
        }, delay);
    }
    
    sendDataPacket() {
        const availablePacket = this.dataPackets.find(p => !p.userData.isActive);
        if (!availablePacket) return;
        
        // 随机选择传输方向和路径
        const direction = Math.random() > 0.5 ? 'up' : 'down';
        const pathIndex = Math.floor(Math.random() * this.flowPaths.length);
        const flowPath = this.flowPaths[pathIndex];
        
        if (!flowPath) return;
        
        const path = direction === 'up' ? flowPath.up : flowPath.down;
        const packetType = Object.keys(this.packetTypes)[Math.floor(Math.random() * Object.keys(this.packetTypes).length)];
        const config = this.packetTypes[packetType];
        
        // 配置数据包
        availablePacket.userData.isActive = true;
        availablePacket.userData.currentPath = path;
        availablePacket.userData.progress = 0;
        availablePacket.userData.speed = config.speed;
        availablePacket.userData.packetType = packetType;
        availablePacket.userData.direction = direction;
        availablePacket.userData.startLayer = direction === 'up' ? flowPath.startLayer : flowPath.endLayer;
        availablePacket.userData.endLayer = direction === 'up' ? flowPath.endLayer : flowPath.startLayer;
        
        // 设置外观
        availablePacket.material.color.setHex(config.color);
        availablePacket.material.emissive.setHex(config.color);
        availablePacket.scale.setScalar(config.size / 0.2);
        availablePacket.visible = true;
        
        // 设置初始位置
        const startPoint = path.getPoint(0);
        availablePacket.position.copy(startPoint);
        
        // 开始封装动画
        if (direction === 'up') {
            this.startEncapsulation(availablePacket);
        }
    }
    
    startEncapsulation(packet) {
        const userData = packet.userData;
        const startLayer = userData.startLayer;
        
        // 模拟OSI层级封装过程
        for (let layer = startLayer; layer <= userData.endLayer; layer++) {
            setTimeout(() => {
                this.addEncapsulationLayer(packet, layer);
            }, (layer - startLayer) * 200);
        }
    }
    
    addEncapsulationLayer(packet, layer) {
        const layerData = this.layerData[layer];
        if (!layerData) return;
        
        // 创建封装效果
        const encapGeometry = new THREE.RingGeometry(0.3, 0.35, 16);
        const encapMaterial = new THREE.MeshBasicMaterial({
            color: layerData.color,
            transparent: true,
            opacity: 0.6,
            side: THREE.DoubleSide
        });
        
        const encapRing = new THREE.Mesh(encapGeometry, encapMaterial);
        encapRing.position.copy(packet.position);
        encapRing.userData = {
            type: 'encapsulation',
            parentPacket: packet,
            layer: layer,
            creationTime: performance.now()
        };
        
        this.scene.add(encapRing);
        this.encapsulationEffects.push(encapRing);
        
        // 添加到数据包的封装层列表
        packet.userData.encapsulationLayers.push(encapRing);
        
        // 动画效果
        this.animateEncapsulation(encapRing);
    }
    
    animateEncapsulation(ring) {
        const startScale = 0.5;
        const endScale = 1.2;
        const duration = 500;
        const startTime = performance.now();
        
        const animate = () => {
            const elapsed = performance.now() - startTime;
            const progress = Math.min(elapsed / duration, 1);
            
            const scale = startScale + (endScale - startScale) * progress;
            ring.scale.setScalar(scale);
            ring.material.opacity = 0.6 * (1 - progress);
            
            if (progress < 1) {
                requestAnimationFrame(animate);
            } else {
                // 动画完成，移除环形效果
                this.scene.remove(ring);
                const index = this.encapsulationEffects.indexOf(ring);
                if (index > -1) {
                    this.encapsulationEffects.splice(index, 1);
                }
            }
        };
        
        animate();
    }
    
    update() {
        // 更新所有活跃的数据包
        this.dataPackets.forEach(packet => {
            if (packet.userData.isActive) {
                this.updatePacket(packet);
            }
        });
        
        // 清理过期的封装效果
        this.cleanupEncapsulationEffects();
    }
    
    updatePacket(packet) {
        const userData = packet.userData;
        const path = userData.currentPath;
        
        if (!path) return;
        
        // 更新进度
        userData.progress += userData.speed;
        
        if (userData.progress >= 1) {
            // 到达终点
            this.onPacketReachDestination(packet);
            return;
        }
        
        // 更新位置
        const position = path.getPoint(userData.progress);
        packet.position.copy(position);
        
        // 添加轨迹点
        if (userData.trail.length > 10) {
            userData.trail.shift();
        }
        userData.trail.push(position.clone());
        
        // 旋转效果
        packet.rotation.x += 0.1;
        packet.rotation.y += 0.05;
        
        // 脉冲效果
        const pulse = 1 + Math.sin(performance.now() * 0.01) * 0.1;
        packet.scale.setScalar(pulse * (userData.packetType === 'HTTP' ? 1.5 : 1));
    }
    
    onPacketReachDestination(packet) {
        const userData = packet.userData;
        
        // 开始解封装动画（如果是下行）
        if (userData.direction === 'down') {
            this.startDecapsulation(packet);
        }
        
        // 重置数据包
        setTimeout(() => {
            this.resetPacket(packet);
        }, userData.direction === 'down' ? 1000 : 0);
    }
    
    startDecapsulation(packet) {
        // 模拟解封装过程
        const userData = packet.userData;
        
        for (let i = 0; i < 3; i++) {
            setTimeout(() => {
                this.createDecapsulationEffect(packet);
            }, i * 150);
        }
    }
    
    createDecapsulationEffect(packet) {
        // 创建解封装爆炸效果
        const particleCount = 8;
        
        for (let i = 0; i < particleCount; i++) {
            const geometry = new THREE.SphereGeometry(0.05, 6, 4);
            const material = new THREE.MeshBasicMaterial({
                color: packet.material.color,
                transparent: true,
                opacity: 0.8
            });
            
            const particle = new THREE.Mesh(geometry, material);
            particle.position.copy(packet.position);
            
            const angle = (i / particleCount) * Math.PI * 2;
            const velocity = new THREE.Vector3(
                Math.cos(angle) * 2,
                Math.random() * 2,
                Math.sin(angle) * 2
            );
            
            particle.userData = {
                velocity: velocity,
                life: 1.0,
                decay: 0.02
            };
            
            this.scene.add(particle);
            this.encapsulationEffects.push(particle);
        }
    }
    
    resetPacket(packet) {
        packet.userData.isActive = false;
        packet.userData.progress = 0;
        packet.userData.currentPath = null;
        packet.userData.encapsulationLayers = [];
        packet.userData.trail = [];
        packet.visible = false;
        packet.scale.setScalar(1);
    }
    
    cleanupEncapsulationEffects() {
        this.encapsulationEffects = this.encapsulationEffects.filter(effect => {
            if (effect.userData.life !== undefined) {
                effect.userData.life -= effect.userData.decay;
                effect.material.opacity = effect.userData.life;
                effect.position.add(effect.userData.velocity.multiplyScalar(0.1));
                
                if (effect.userData.life <= 0) {
                    this.scene.remove(effect);
                    return false;
                }
            }
            return true;
        });
    }
    
    // 公共接口
    toggleDataFlow() {
        if (this.isActive) {
            this.stopDataFlow();
        } else {
            this.startDataFlow();
        }
    }
    
    setFlowSpeed(speed) {
        Object.keys(this.packetTypes).forEach(type => {
            this.packetTypes[type].speed *= speed;
        });
    }
    
    getActivePacketCount() {
        return this.dataPackets.filter(p => p.userData.isActive).length;
    }
}
