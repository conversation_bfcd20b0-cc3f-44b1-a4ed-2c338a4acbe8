<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebGL Test</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #0a0b1e;
            color: white;
            font-family: Arial, sans-serif;
        }
        #test-canvas {
            border: 2px solid #4facfe;
            background: #1a1a2e;
            display: block;
            margin: 20px auto;
        }
        .status {
            text-align: center;
            padding: 20px;
            font-size: 18px;
        }
        .success { color: #10b981; }
        .error { color: #ef4444; }
    </style>
</head>
<body>
    <div class="status" id="status">正在检测WebGL支持...</div>
    <canvas id="test-canvas" width="400" height="300"></canvas>
    
    <script>
        function testWebGL() {
            const canvas = document.getElementById('test-canvas');
            const status = document.getElementById('status');
            
            try {
                // 测试WebGL上下文
                const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
                
                if (!gl) {
                    throw new Error('WebGL not supported');
                }
                
                // 清除画布为蓝色
                gl.clearColor(0.2, 0.4, 0.8, 1.0);
                gl.clear(gl.COLOR_BUFFER_BIT);
                
                // 获取WebGL信息
                const vendor = gl.getParameter(gl.VENDOR);
                const renderer = gl.getParameter(gl.RENDERER);
                const version = gl.getParameter(gl.VERSION);
                
                status.innerHTML = `
                    <div class="success">✅ WebGL支持正常!</div>
                    <div style="font-size: 14px; margin-top: 10px;">
                        <div>供应商: ${vendor}</div>
                        <div>渲染器: ${renderer}</div>
                        <div>版本: ${version}</div>
                    </div>
                `;
                
                return true;
                
            } catch (error) {
                status.innerHTML = `
                    <div class="error">❌ WebGL不支持</div>
                    <div style="font-size: 14px; margin-top: 10px;">
                        错误: ${error.message}
                    </div>
                `;
                return false;
            }
        }
        
        // 运行测试
        testWebGL();
    </script>
</body>
</html>