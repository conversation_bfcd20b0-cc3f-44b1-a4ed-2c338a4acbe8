// 布局验证脚本
class LayoutValidator {
    constructor() {
        this.issues = [];
        this.warnings = [];
        this.init();
    }

    init() {
        console.log('🔍 开始布局验证...');
        this.validateViewport();
        this.validateContainers();
        this.validateResponsive();
        this.validateZIndex();
        this.validateOverflow();
        this.generateReport();
    }

    validateViewport() {
        console.log('📱 检查视窗设置...');
        
        const viewport = document.querySelector('meta[name="viewport"]');
        if (!viewport) {
            this.issues.push('缺少viewport meta标签');
        } else {
            const content = viewport.getAttribute('content');
            if (!content.includes('width=device-width')) {
                this.issues.push('viewport未设置width=device-width');
            }
        }

        // 检查body溢出
        const body = document.body;
        const bodyStyles = getComputedStyle(body);
        if (bodyStyles.overflowX !== 'hidden') {
            this.warnings.push('body未设置overflow-x: hidden');
        }
    }

    validateContainers() {
        console.log('📦 检查容器布局...');
        
        const containers = [
            '.app-container',
            '.main-workspace', 
            '.scene-container',
            '.info-panel',
            '.control-panel'
        ];

        containers.forEach(selector => {
            const element = document.querySelector(selector);
            if (!element) {
                this.issues.push(`找不到容器: ${selector}`);
                return;
            }

            const styles = getComputedStyle(element);
            const rect = element.getBoundingClientRect();

            // 检查是否超出视窗
            if (rect.width > window.innerWidth) {
                this.issues.push(`${selector} 宽度超出视窗: ${rect.width}px > ${window.innerWidth}px`);
            }

            // 检查flex容器
            if (styles.display === 'flex') {
                if (styles.minWidth !== '0px' && selector !== '.info-panel') {
                    this.warnings.push(`${selector} flex容器应设置min-width: 0`);
                }
            }
        });
    }

    validateResponsive() {
        console.log('📱 检查响应式布局...');
        
        const breakpoints = [480, 768, 1024, 1200];
        const currentWidth = window.innerWidth;
        
        console.log(`当前屏幕宽度: ${currentWidth}px`);
        
        // 检查信息面板在移动端的行为
        const infoPanel = document.querySelector('.info-panel');
        if (infoPanel && currentWidth <= 768) {
            const styles = getComputedStyle(infoPanel);
            if (styles.width !== '100%') {
                this.warnings.push('移动端信息面板应为100%宽度');
            }
        }

        // 检查控制面板在小屏幕的布局
        const controlPanel = document.querySelector('.control-panel');
        if (controlPanel && currentWidth <= 1024) {
            const styles = getComputedStyle(controlPanel);
            if (styles.flexDirection !== 'column') {
                this.warnings.push('小屏幕下控制面板应为垂直布局');
            }
        }
    }

    validateZIndex() {
        console.log('🔢 检查层级顺序...');
        
        const zIndexElements = [
            { selector: '.startup-screen', expectedMin: 9999 },
            { selector: '.modal-overlay', expectedMin: 10000 },
            { selector: '.loading-overlay', expectedMin: 1000 },
            { selector: '.performance-indicators', expectedMin: 15 },
            { selector: '.layer-labels', expectedMin: 10 }
        ];

        zIndexElements.forEach(({ selector, expectedMin }) => {
            const element = document.querySelector(selector);
            if (element) {
                const zIndex = parseInt(getComputedStyle(element).zIndex) || 0;
                if (zIndex < expectedMin) {
                    this.warnings.push(`${selector} z-index过低: ${zIndex} < ${expectedMin}`);
                }
            }
        });
    }

    validateOverflow() {
        console.log('🌊 检查溢出处理...');
        
        const elements = document.querySelectorAll('*');
        elements.forEach(element => {
            const rect = element.getBoundingClientRect();
            
            // 检查水平溢出
            if (rect.right > window.innerWidth + 10) { // 10px容差
                const selector = this.getElementSelector(element);
                this.warnings.push(`元素水平溢出: ${selector}`);
            }
        });
    }

    getElementSelector(element) {
        if (element.id) return `#${element.id}`;
        if (element.className) {
            const classes = element.className.split(' ').filter(c => c.trim());
            if (classes.length > 0) return `.${classes[0]}`;
        }
        return element.tagName.toLowerCase();
    }

    generateReport() {
        console.log('\n📊 布局验证报告');
        console.log('='.repeat(50));
        
        if (this.issues.length === 0 && this.warnings.length === 0) {
            console.log('✅ 布局验证通过，未发现问题！');
        } else {
            if (this.issues.length > 0) {
                console.log('\n❌ 严重问题:');
                this.issues.forEach((issue, index) => {
                    console.log(`${index + 1}. ${issue}`);
                });
            }
            
            if (this.warnings.length > 0) {
                console.log('\n⚠️ 警告:');
                this.warnings.forEach((warning, index) => {
                    console.log(`${index + 1}. ${warning}`);
                });
            }
        }
        
        console.log('\n📱 设备信息:');
        console.log(`屏幕尺寸: ${screen.width}x${screen.height}`);
        console.log(`视窗尺寸: ${window.innerWidth}x${window.innerHeight}`);
        console.log(`设备像素比: ${window.devicePixelRatio}`);
        console.log(`用户代理: ${navigator.userAgent.includes('Mobile') ? '移动设备' : '桌面设备'}`);
        
        console.log('='.repeat(50));
    }

    // 实时监控布局变化
    startMonitoring() {
        let resizeTimer;
        window.addEventListener('resize', () => {
            clearTimeout(resizeTimer);
            resizeTimer = setTimeout(() => {
                console.log('🔄 窗口大小改变，重新验证布局...');
                this.issues = [];
                this.warnings = [];
                this.validateContainers();
                this.validateResponsive();
                this.validateOverflow();
                this.generateReport();
            }, 300);
        });
        
        console.log('👁️ 布局监控已启动');
    }
}

// 页面加载完成后自动运行验证
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        setTimeout(() => {
            const validator = new LayoutValidator();
            validator.startMonitoring();
        }, 1000);
    });
} else {
    setTimeout(() => {
        const validator = new LayoutValidator();
        validator.startMonitoring();
    }, 1000);
}

// 导出到全局作用域，方便手动调用
window.LayoutValidator = LayoutValidator;
