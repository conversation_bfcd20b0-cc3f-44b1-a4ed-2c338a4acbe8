<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Network Poetry - OSI 3D 生物仿生艺术模拟器</title>
    <meta name="description" content="探索OSI七层模型的3D生物仿生艺术体验">

    <!-- Favicon -->
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🧬</text></svg>">

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap" rel="stylesheet">

    <!-- Icons - 使用最新版本 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">

    <!-- Three.js - 使用最新版本和ES模块 -->
    <script type="importmap">
    {
        "imports": {
            "three": "https://unpkg.com/three@0.160.0/build/three.module.js",
            "three/addons/": "https://unpkg.com/three@0.160.0/examples/jsm/"
        }
    }
    </script>

    <!-- Styles -->
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <!-- 启动画面 -->
    <div class="startup-screen" id="startup-screen">
        <div class="startup-content">
            <div class="startup-logo">
                <div class="logo-symbol">🧬</div>
                <h1 class="startup-title">Network Poetry</h1>
                <p class="startup-subtitle">OSI 3D 生物仿生艺术模拟器</p>
            </div>
            <div class="startup-progress">
                <div class="progress-bar">
                    <div class="progress-fill" id="progress-fill"></div>
                </div>
                <p class="progress-text" id="progress-text">正在初始化生物网络生态系统...</p>
            </div>
        </div>
    </div>

    <!-- 主应用容器 -->
    <div class="app-container">
        <!-- 顶部导航栏 -->
        <nav class="top-navigation">
            <div class="nav-brand">
                <div class="brand-icon">
                    <i class="fas fa-dna"></i>
                </div>
                <div class="brand-text">
                    <span class="brand-name">Network Poetry</span>
                    <span class="brand-tagline">生物网络诗学</span>
                </div>
            </div>

            <div class="nav-actions">
                <button class="nav-btn" id="fullscreen-btn" title="全屏模式">
                    <i class="fas fa-expand"></i>
                </button>
                <button class="nav-btn" id="settings-btn" title="设置">
                    <i class="fas fa-cog"></i>
                </button>
                <button class="nav-btn" id="info-btn" title="关于">
                    <i class="fas fa-question-circle"></i>
                </button>
            </div>
        </nav>

        <!-- 控制面板 -->
        <header class="control-panel">
            <div class="panel-section">
                <h3 class="section-title">
                    <i class="fas fa-eye"></i>
                    视角控制
                </h3>
                <div class="view-controls">
                    <button class="view-btn active" data-view="overview">
                        <i class="fas fa-globe-americas"></i>
                        <span>生态总览</span>
                    </button>
                    <button class="view-btn" data-view="layers">
                        <i class="fas fa-layer-group"></i>
                        <span>层级聚焦</span>
                    </button>
                    <button class="view-btn" data-view="network">
                        <i class="fas fa-project-diagram"></i>
                        <span>网络拓扑</span>
                    </button>
                </div>
            </div>

            <div class="panel-section">
                <h3 class="section-title">
                    <i class="fas fa-play-circle"></i>
                    仿生模拟
                </h3>
                <div class="simulation-controls">
                    <button class="sim-btn primary" id="start-simulation">
                        <i class="fas fa-play"></i>
                        <span>启动生命流</span>
                    </button>
                    <button class="sim-btn secondary" id="reset-simulation">
                        <i class="fas fa-redo"></i>
                        <span>重置生态</span>
                    </button>
                </div>
            </div>

            <div class="panel-section">
                <h3 class="section-title">
                    <i class="fas fa-hand-sparkles"></i>
                    交互模式
                </h3>
                <div class="interaction-modes">
                    <div class="interaction-mode active" data-mode="touch">
                        <div class="mode-icon">
                            <i class="fas fa-hand-pointer"></i>
                        </div>
                        <span>触控</span>
                    </div>
                    <div class="interaction-mode" data-mode="voice">
                        <div class="mode-icon">
                            <i class="fas fa-microphone"></i>
                        </div>
                        <span>语音</span>
                    </div>
                    <div class="interaction-mode" data-mode="gesture">
                        <div class="mode-icon">
                            <i class="fas fa-hand-peace"></i>
                        </div>
                        <span>手势</span>
                    </div>
                </div>
            </div>
        </header>

        <!-- 主工作区 -->
        <main class="main-workspace">
            <div class="scene-container">
                <div id="three-canvas"></div>

                <!-- 场景覆盖层 -->
                <div class="scene-overlay">
                    <!-- OSI层级标签 -->
                    <div class="layer-labels" id="layer-labels">
                        <!-- 标签将通过JavaScript动态生成 -->
                    </div>

                    <!-- 性能指标 -->
                    <div class="performance-indicators">
                        <div class="indicator" id="bio-health">
                            <i class="fas fa-heartbeat"></i>
                            <span class="indicator-label">生态健康</span>
                            <span class="indicator-value">98%</span>
                        </div>
                        <div class="indicator" id="data-flow">
                            <i class="fas fa-exchange-alt"></i>
                            <span class="indicator-label">数据流速</span>
                            <span class="indicator-value">1.2GB/s</span>
                        </div>
                        <div class="indicator" id="network-complexity">
                            <i class="fas fa-project-diagram"></i>
                            <span class="indicator-label">网络复杂度</span>
                            <span class="indicator-value">87%</span>
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <!-- 侧边栏信息面板 -->
        <aside class="info-panel">
            <div class="panel-header">
                <div class="panel-tabs">
                    <button class="tab-btn active" data-tab="overview">
                        <i class="fas fa-home"></i>
                        概览
                    </button>
                    <button class="tab-btn" data-tab="layers">
                        <i class="fas fa-layer-group"></i>
                        层级
                    </button>
                    <button class="tab-btn" data-tab="analytics">
                        <i class="fas fa-chart-line"></i>
                        分析
                    </button>
                    <button class="tab-btn" data-tab="settings">
                        <i class="fas fa-cog"></i>
                        设置
                    </button>
                </div>
            </div>

            <div class="panel-content" id="panel-content">
                <div class="tab-panel active" data-tab="overview">
                    <h3>网络诗学体验</h3>
                    <p>探索OSI七层模型的生物仿生艺术世界</p>

                    <div style="margin-top: 1rem;">
                        <h4>生态系统概览</h4>
                        <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 1rem; margin-top: 0.5rem;">
                            <div style="text-align: center; padding: 1rem; background: rgba(255,255,255,0.05); border-radius: 8px;">
                                <div style="font-size: 1.5rem; font-weight: 600; color: #4facfe;">7</div>
                                <div style="font-size: 0.75rem; color: #94a3b8;">生物层级</div>
                            </div>
                            <div style="text-align: center; padding: 1rem; background: rgba(255,255,255,0.05); border-radius: 8px;">
                                <div style="font-size: 1.5rem; font-weight: 600; color: #4facfe;">∞</div>
                                <div style="font-size: 0.75rem; color: #94a3b8;">数据流</div>
                            </div>
                            <div style="text-align: center; padding: 1rem; background: rgba(255,255,255,0.05); border-radius: 8px;">
                                <div style="font-size: 1.5rem; font-weight: 600; color: #4facfe;">42</div>
                                <div style="font-size: 0.75rem; color: #94a3b8;">有机设备</div>
                            </div>
                        </div>
                    </div>

                    <div style="margin-top: 1.5rem;">
                        <h4>交互指南</h4>
                        <div style="margin-top: 0.5rem; display: flex; flex-direction: column; gap: 0.5rem;">
                            <div style="display: flex; align-items: center; gap: 0.5rem; padding: 0.5rem; background: rgba(255,255,255,0.03); border-radius: 6px;">
                                <i class="fas fa-mouse" style="color: #4facfe;"></i>
                                <div>
                                    <div style="font-size: 0.875rem;">鼠标拖拽</div>
                                    <div style="font-size: 0.75rem; color: #94a3b8;">旋转3D生态系统</div>
                                </div>
                            </div>
                            <div style="display: flex; align-items: center; gap: 0.5rem; padding: 0.5rem; background: rgba(255,255,255,0.03); border-radius: 6px;">
                                <i class="fas fa-scroll" style="color: #4facfe;"></i>
                                <div>
                                    <div style="font-size: 0.875rem;">滚轮缩放</div>
                                    <div style="font-size: 0.75rem; color: #94a3b8;">深入生物层级</div>
                                </div>
                            </div>
                            <div style="display: flex; align-items: center; gap: 0.5rem; padding: 0.5rem; background: rgba(255,255,255,0.03); border-radius: 6px;">
                                <i class="fas fa-hand-point-up" style="color: #4facfe;"></i>
                                <div>
                                    <div style="font-size: 0.875rem;">点击生物体</div>
                                    <div style="font-size: 0.75rem; color: #94a3b8;">查看详细信息</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="tab-panel" data-tab="layers">
                    <h3>生物层级详情</h3>
                    <div id="layer-details">
                        <!-- 层级详情将通过JavaScript动态生成 -->
                    </div>
                </div>

                <div class="tab-panel" data-tab="analytics">
                    <h3>生态分析</h3>
                    <p>实时监控生物网络生态系统的各项指标</p>
                    <div style="margin-top: 1rem;">
                        <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 1rem;">
                            <div style="text-align: center; padding: 1rem; background: rgba(255,255,255,0.05); border-radius: 8px;">
                                <div style="font-size: 1.25rem; font-weight: 600; color: #4facfe;" id="fps-counter">60 FPS</div>
                                <div style="font-size: 0.75rem; color: #94a3b8;">渲染性能</div>
                            </div>
                            <div style="text-align: center; padding: 1rem; background: rgba(255,255,255,0.05); border-radius: 8px;">
                                <div style="font-size: 1.25rem; font-weight: 600; color: #4facfe;">128MB</div>
                                <div style="font-size: 0.75rem; color: #94a3b8;">内存使用</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="tab-panel" data-tab="settings">
                    <h3>系统设置</h3>
                    <p>自定义您的生物网络体验</p>
                </div>
            </div>
        </aside>

        <!-- 底部状态栏 -->
        <footer class="status-bar">
            <div class="status-group">
                <div class="status-item">
                    <i class="fas fa-eye"></i>
                    <span id="camera-position">视角: 生态总览</span>
                </div>
                <div class="status-item">
                    <i class="fas fa-hand-pointer"></i>
                    <span id="interaction-mode">交互: 触控模式</span>
                </div>
            </div>

            <div class="status-item">
                <i class="fas fa-heartbeat"></i>
                <span id="simulation-status">生态系统就绪</span>
            </div>

            <div class="status-group">
                <div class="status-item">
                    <i class="fas fa-clock"></i>
                    <span id="simulation-time">00:00</span>
                </div>
                <div class="status-item">
                    <i class="fas fa-tachometer-alt"></i>
                    <span id="fps-display">60 FPS</span>
                </div>
                <div class="status-item">
                    <i class="fas fa-memory"></i>
                    <span id="memory-usage">128MB</span>
                </div>
            </div>
        </footer>
    </div>

    <!-- 现代JavaScript ES模块 -->
    <script type="module">
        import ModernOSI3DSimulator from './script.js';

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', () => {
            window.osiSimulator = new ModernOSI3DSimulator();
            console.log('✅ 现代OSI 3D模拟器已启动');
        });
    </script>
</body>
</html>
