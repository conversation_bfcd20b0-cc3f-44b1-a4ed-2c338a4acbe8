<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Network Poetry - OSI 3D 生物仿生艺术模拟器</title>
    <meta name="description" content="探索OSI七层模型的3D生物仿生艺术体验">
    
    <!-- Favicon -->
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🧬</text></svg>">
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Three.js -->
    <script src="https://cdn.jsdelivr.net/npm/three@0.128.0/build/three.min.js"></script>
    
    <!-- Styles -->
    <style>
        /* OSI 3D 生物仿生艺术模拟器 - 内联样式 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary-bg: #0a0b1e;
            --secondary-bg: #1a1b2e;
            --accent-color: #4facfe;
            --text-primary: #ffffff;
            --text-secondary: #cbd5e1;
            --text-muted: #94a3b8;
            --border-color: rgba(255, 255, 255, 0.1);
            --glass-bg: rgba(255, 255, 255, 0.05);
            --space-xs: 0.25rem;
            --space-sm: 0.5rem;
            --space-md: 1rem;
            --space-lg: 1.5rem;
            --space-xl: 2rem;
            --font-xs: 0.75rem;
            --font-sm: 0.875rem;
            --font-base: 1rem;
            --font-lg: 1.125rem;
            --font-xl: 1.25rem;
            --header-height: 60px;
            --control-height: 80px;
            --sidebar-width: 320px;
            --footer-height: 50px;
            --transition-fast: 0.15s ease;
            --transition-normal: 0.3s ease;
            --transition-slow: 0.5s ease;
        }

        html {
            height: 100%;
            font-size: 16px;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, var(--primary-bg) 0%, var(--secondary-bg) 100%);
            color: var(--text-primary);
            height: 100vh;
            overflow: hidden;
            line-height: 1.5;
        }

        .app-container {
            display: grid;
            grid-template-areas: 
                "header header"
                "controls controls"
                "main sidebar"
                "footer footer";
            grid-template-rows: var(--header-height) var(--control-height) 1fr var(--footer-height);
            grid-template-columns: 1fr var(--sidebar-width);
            height: 100vh;
            gap: 0;
        }

        .top-navigation {
            grid-area: header;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 var(--space-lg);
            background: var(--glass-bg);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid var(--border-color);
            z-index: 100;
        }

        .nav-brand {
            display: flex;
            align-items: center;
            gap: var(--space-md);
        }

        .brand-icon {
            font-size: var(--font-xl);
            color: var(--accent-color);
        }

        .brand-text {
            display: flex;
            flex-direction: column;
        }

        .brand-name {
            font-weight: 600;
            font-size: var(--font-lg);
        }

        .brand-tagline {
            font-size: var(--font-xs);
            color: var(--text-muted);
        }

        .nav-actions {
            display: flex;
            gap: var(--space-sm);
        }

        .nav-btn {
            width: 40px;
            height: 40px;
            border: none;
            background: transparent;
            color: var(--text-secondary);
            border-radius: 8px;
            cursor: pointer;
            transition: all var(--transition-fast);
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .nav-btn:hover {
            background: var(--glass-bg);
            color: var(--text-primary);
            transform: scale(1.05);
        }

        .control-panel {
            grid-area: controls;
            display: flex;
            align-items: center;
            gap: var(--space-lg);
            padding: 0 var(--space-lg);
            background: var(--glass-bg);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid var(--border-color);
            overflow-x: auto;
        }

        .panel-section {
            display: flex;
            align-items: center;
            gap: var(--space-md);
            flex-shrink: 0;
        }

        .section-title {
            font-size: var(--font-sm);
            color: var(--text-muted);
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: var(--space-xs);
            white-space: nowrap;
        }

        .view-controls,
        .simulation-controls {
            display: flex;
            gap: var(--space-sm);
        }

        .view-btn,
        .sim-btn {
            padding: var(--space-sm) var(--space-md);
            border: 1px solid var(--border-color);
            background: transparent;
            color: var(--text-secondary);
            border-radius: 6px;
            cursor: pointer;
            transition: all var(--transition-fast);
            font-size: var(--font-sm);
            white-space: nowrap;
            display: flex;
            align-items: center;
            gap: var(--space-xs);
        }

        .view-btn:hover,
        .sim-btn:hover {
            background: var(--glass-bg);
            color: var(--text-primary);
            border-color: var(--accent-color);
        }

        .view-btn.active {
            background: var(--accent-color);
            color: white;
            border-color: var(--accent-color);
        }

        .sim-btn.primary {
            background: var(--accent-color);
            color: white;
            border-color: var(--accent-color);
        }

        .main-workspace {
            grid-area: main;
            position: relative;
            overflow: hidden;
            background: var(--primary-bg);
        }

        .scene-container {
            width: 100%;
            height: 100%;
            position: relative;
        }

        #three-canvas {
            width: 100%;
            height: 100%;
            display: block;
        }

        .info-panel {
            grid-area: sidebar;
            background: var(--glass-bg);
            backdrop-filter: blur(10px);
            border-left: 1px solid var(--border-color);
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .panel-header {
            padding: var(--space-md);
            border-bottom: 1px solid var(--border-color);
            flex-shrink: 0;
        }

        .panel-tabs {
            display: flex;
            gap: var(--space-xs);
        }

        .tab-btn {
            padding: var(--space-sm) var(--space-md);
            border: none;
            background: transparent;
            color: var(--text-muted);
            border-radius: 6px;
            cursor: pointer;
            transition: all var(--transition-fast);
            font-size: var(--font-sm);
            display: flex;
            align-items: center;
            gap: var(--space-xs);
        }

        .tab-btn:hover {
            background: var(--glass-bg);
            color: var(--text-secondary);
        }

        .tab-btn.active {
            background: var(--accent-color);
            color: white;
        }

        .panel-content {
            flex: 1;
            padding: var(--space-md);
            overflow-y: auto;
        }

        .tab-panel {
            display: none;
        }

        .tab-panel.active {
            display: block;
        }

        .status-bar {
            grid-area: footer;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 var(--space-lg);
            background: var(--glass-bg);
            backdrop-filter: blur(10px);
            border-top: 1px solid var(--border-color);
            font-size: var(--font-sm);
        }

        .status-group {
            display: flex;
            gap: var(--space-lg);
        }

        .status-item {
            display: flex;
            align-items: center;
            gap: var(--space-xs);
            color: var(--text-muted);
        }

        .scene-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 10;
        }

        .layer-labels {
            position: relative;
            width: 100%;
            height: 100%;
        }

        .layer-label {
            position: absolute;
            pointer-events: auto;
            background: var(--glass-bg);
            backdrop-filter: blur(10px);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: var(--space-sm);
            min-width: 120px;
            transition: all var(--transition-normal);
            cursor: pointer;
        }

        .layer-label:hover {
            background: rgba(255, 255, 255, 0.1);
            border-color: var(--accent-color);
            transform: scale(1.05);
        }

        .label-content {
            display: flex;
            flex-direction: column;
            gap: var(--space-xs);
        }

        .label-header {
            display: flex;
            align-items: center;
            gap: var(--space-xs);
        }

        .label-number {
            width: 24px;
            height: 24px;
            background: var(--accent-color);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: var(--font-sm);
            font-weight: 600;
        }

        .label-info {
            display: flex;
            flex-direction: column;
        }

        .label-name {
            font-size: var(--font-sm);
            font-weight: 500;
            color: var(--text-primary);
        }

        .label-english {
            font-size: var(--font-xs);
            color: var(--text-muted);
        }

        .label-description {
            font-size: var(--font-xs);
            color: var(--text-secondary);
            margin-top: var(--space-xs);
        }

        .performance-indicators {
            position: absolute;
            top: var(--space-md);
            right: var(--space-md);
            display: flex;
            flex-direction: column;
            gap: var(--space-sm);
            z-index: 20;
            pointer-events: none;
        }

        .indicator {
            background: var(--glass-bg);
            backdrop-filter: blur(10px);
            border: 1px solid var(--border-color);
            border-radius: 6px;
            padding: var(--space-sm);
            display: flex;
            align-items: center;
            gap: var(--space-xs);
            font-size: var(--font-xs);
            color: var(--text-secondary);
        }

        .indicator-value {
            color: var(--accent-color);
            font-weight: 600;
        }

        .interaction-modes {
            display: flex;
            gap: var(--space-sm);
        }

        .interaction-mode {
            padding: var(--space-sm);
            border: 1px solid var(--border-color);
            background: transparent;
            border-radius: 6px;
            cursor: pointer;
            transition: all var(--transition-fast);
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: var(--space-xs);
            min-width: 60px;
        }

        .interaction-mode:hover {
            background: var(--glass-bg);
            border-color: var(--accent-color);
        }

        .interaction-mode.active {
            background: var(--accent-color);
            color: white;
            border-color: var(--accent-color);
        }

        .mode-icon {
            font-size: var(--font-lg);
        }

        .interaction-mode span {
            font-size: var(--font-xs);
        }

        .startup-screen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: var(--primary-bg);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
            transition: opacity var(--transition-slow);
        }

        .startup-screen.hidden {
            opacity: 0;
            pointer-events: none;
        }

        .startup-content {
            text-align: center;
            max-width: 400px;
        }

        .startup-logo {
            margin-bottom: var(--space-xl);
        }

        .logo-symbol {
            font-size: 4rem;
            margin-bottom: var(--space-md);
            color: var(--accent-color);
        }

        .startup-title {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: var(--space-sm);
            background: linear-gradient(45deg, var(--accent-color), #ff6b9d);
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .startup-subtitle {
            color: var(--text-muted);
            margin-bottom: var(--space-xl);
        }

        .progress-bar {
            width: 100%;
            height: 4px;
            background: var(--secondary-bg);
            border-radius: 2px;
            overflow: hidden;
            margin-bottom: var(--space-md);
        }

        .progress-fill {
            height: 100%;
            background: var(--accent-color);
            width: 0%;
            transition: width var(--transition-normal);
        }

        .progress-text {
            font-size: var(--font-sm);
            color: var(--text-muted);
        }

        .hidden {
            display: none !important;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .app-container {
                grid-template-areas: 
                    "header"
                    "controls"
                    "main"
                    "sidebar"
                    "footer";
                grid-template-columns: 1fr;
                grid-template-rows: var(--header-height) auto 1fr 180px var(--footer-height);
            }
            
            .info-panel {
                border-left: none;
                border-top: 1px solid var(--border-color);
            }
            
            .control-panel {
                flex-wrap: wrap;
                height: auto;
                min-height: var(--control-height);
            }
            
            .nav-brand .brand-tagline {
                display: none;
            }
        }
    </style>
</head>
<body>
    <!-- 启动画面 -->
    <div class="startup-screen" id="startup-screen">
        <div class="startup-content">
            <div class="startup-logo">
                <div class="logo-symbol">🧬</div>
                <h1 class="startup-title">Network Poetry</h1>
                <p class="startup-subtitle">OSI 3D 生物仿生艺术模拟器</p>
            </div>
            <div class="startup-progress">
                <div class="progress-bar">
                    <div class="progress-fill" id="progress-fill"></div>
                </div>
                <p class="progress-text" id="progress-text">正在初始化生物网络生态系统...</p>
            </div>
        </div>
    </div>

    <!-- 主应用容器 -->
    <div class="app-container">
        <!-- 顶部导航栏 -->
        <nav class="top-navigation">
            <div class="nav-brand">
                <div class="brand-icon">
                    <i class="fas fa-dna"></i>
                </div>
                <div class="brand-text">
                    <span class="brand-name">Network Poetry</span>
                    <span class="brand-tagline">生物网络诗学</span>
                </div>
            </div>

            <div class="nav-actions">
                <button class="nav-btn" id="fullscreen-btn" title="全屏模式">
                    <i class="fas fa-expand"></i>
                </button>
                <button class="nav-btn" id="settings-btn" title="设置">
                    <i class="fas fa-cog"></i>
                </button>
                <button class="nav-btn" id="info-btn" title="关于">
                    <i class="fas fa-question-circle"></i>
                </button>
            </div>
        </nav>

        <!-- 控制面板 -->
        <header class="control-panel">
            <div class="panel-section">
                <h3 class="section-title">
                    <i class="fas fa-eye"></i>
                    视角控制
                </h3>
                <div class="view-controls">
                    <button class="view-btn active" data-view="overview">
                        <i class="fas fa-globe-americas"></i>
                        <span>生态总览</span>
                    </button>
                    <button class="view-btn" data-view="layers">
                        <i class="fas fa-layer-group"></i>
                        <span>层级聚焦</span>
                    </button>
                    <button class="view-btn" data-view="network">
                        <i class="fas fa-project-diagram"></i>
                        <span>网络拓扑</span>
                    </button>
                </div>
            </div>

            <div class="panel-section">
                <h3 class="section-title">
                    <i class="fas fa-play-circle"></i>
                    仿生模拟
                </h3>
                <div class="simulation-controls">
                    <button class="sim-btn primary" id="start-simulation">
                        <i class="fas fa-play"></i>
                        <span>启动生命流</span>
                    </button>
                    <button class="sim-btn secondary" id="reset-simulation">
                        <i class="fas fa-redo"></i>
                        <span>重置生态</span>
                    </button>
                </div>
            </div>

            <div class="panel-section">
                <h3 class="section-title">
                    <i class="fas fa-hand-sparkles"></i>
                    交互模式
                </h3>
                <div class="interaction-modes">
                    <div class="interaction-mode active" data-mode="touch">
                        <div class="mode-icon">
                            <i class="fas fa-hand-pointer"></i>
                        </div>
                        <span>触控</span>
                    </div>
                    <div class="interaction-mode" data-mode="voice">
                        <div class="mode-icon">
                            <i class="fas fa-microphone"></i>
                        </div>
                        <span>语音</span>
                    </div>
                    <div class="interaction-mode" data-mode="gesture">
                        <div class="mode-icon">
                            <i class="fas fa-hand-peace"></i>
                        </div>
                        <span>手势</span>
                    </div>
                </div>
            </div>
        </header>

        <!-- 主工作区 -->
        <main class="main-workspace">
            <div class="scene-container">
                <div id="three-canvas"></div>

                <!-- 场景覆盖层 -->
                <div class="scene-overlay">
                    <!-- OSI层级标签 -->
                    <div class="layer-labels" id="layer-labels">
                        <!-- 标签将通过JavaScript动态生成 -->
                    </div>

                    <!-- 性能指标 -->
                    <div class="performance-indicators">
                        <div class="indicator" id="bio-health">
                            <i class="fas fa-heartbeat"></i>
                            <span class="indicator-label">生态健康</span>
                            <span class="indicator-value">98%</span>
                        </div>
                        <div class="indicator" id="data-flow">
                            <i class="fas fa-exchange-alt"></i>
                            <span class="indicator-label">数据流速</span>
                            <span class="indicator-value">1.2GB/s</span>
                        </div>
                        <div class="indicator" id="network-complexity">
                            <i class="fas fa-project-diagram"></i>
                            <span class="indicator-label">网络复杂度</span>
                            <span class="indicator-value">87%</span>
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <!-- 侧边栏信息面板 -->
        <aside class="info-panel">
            <div class="panel-header">
                <div class="panel-tabs">
                    <button class="tab-btn active" data-tab="overview">
                        <i class="fas fa-home"></i>
                        概览
                    </button>
                    <button class="tab-btn" data-tab="layers">
                        <i class="fas fa-layer-group"></i>
                        层级
                    </button>
                    <button class="tab-btn" data-tab="analytics">
                        <i class="fas fa-chart-line"></i>
                        分析
                    </button>
                    <button class="tab-btn" data-tab="settings">
                        <i class="fas fa-cog"></i>
                        设置
                    </button>
                </div>
            </div>

            <div class="panel-content" id="panel-content">
                <div class="tab-panel active" data-tab="overview">
                    <h3>网络诗学体验</h3>
                    <p>探索OSI七层模型的生物仿生艺术世界</p>

                    <div style="margin-top: 1rem;">
                        <h4>生态系统概览</h4>
                        <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 1rem; margin-top: 0.5rem;">
                            <div style="text-align: center; padding: 1rem; background: rgba(255,255,255,0.05); border-radius: 8px;">
                                <div style="font-size: 1.5rem; font-weight: 600; color: #4facfe;">7</div>
                                <div style="font-size: 0.75rem; color: #94a3b8;">生物层级</div>
                            </div>
                            <div style="text-align: center; padding: 1rem; background: rgba(255,255,255,0.05); border-radius: 8px;">
                                <div style="font-size: 1.5rem; font-weight: 600; color: #4facfe;">∞</div>
                                <div style="font-size: 0.75rem; color: #94a3b8;">数据流</div>
                            </div>
                            <div style="text-align: center; padding: 1rem; background: rgba(255,255,255,0.05); border-radius: 8px;">
                                <div style="font-size: 1.5rem; font-weight: 600; color: #4facfe;">42</div>
                                <div style="font-size: 0.75rem; color: #94a3b8;">有机设备</div>
                            </div>
                        </div>
                    </div>

                    <div style="margin-top: 1.5rem;">
                        <h4>交互指南</h4>
                        <div style="margin-top: 0.5rem; display: flex; flex-direction: column; gap: 0.5rem;">
                            <div style="display: flex; align-items: center; gap: 0.5rem; padding: 0.5rem; background: rgba(255,255,255,0.03); border-radius: 6px;">
                                <i class="fas fa-mouse" style="color: #4facfe;"></i>
                                <div>
                                    <div style="font-size: 0.875rem;">鼠标拖拽</div>
                                    <div style="font-size: 0.75rem; color: #94a3b8;">旋转3D生态系统</div>
                                </div>
                            </div>
                            <div style="display: flex; align-items: center; gap: 0.5rem; padding: 0.5rem; background: rgba(255,255,255,0.03); border-radius: 6px;">
                                <i class="fas fa-scroll" style="color: #4facfe;"></i>
                                <div>
                                    <div style="font-size: 0.875rem;">滚轮缩放</div>
                                    <div style="font-size: 0.75rem; color: #94a3b8;">深入生物层级</div>
                                </div>
                            </div>
                            <div style="display: flex; align-items: center; gap: 0.5rem; padding: 0.5rem; background: rgba(255,255,255,0.03); border-radius: 6px;">
                                <i class="fas fa-hand-point-up" style="color: #4facfe;"></i>
                                <div>
                                    <div style="font-size: 0.875rem;">点击生物体</div>
                                    <div style="font-size: 0.75rem; color: #94a3b8;">查看详细信息</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="tab-panel" data-tab="layers">
                    <h3>生物层级详情</h3>
                    <div id="layer-details">
                        <!-- 层级详情将通过JavaScript动态生成 -->
                    </div>
                </div>

                <div class="tab-panel" data-tab="analytics">
                    <h3>生态分析</h3>
                    <p>实时监控生物网络生态系统的各项指标</p>
                    <div style="margin-top: 1rem;">
                        <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 1rem;">
                            <div style="text-align: center; padding: 1rem; background: rgba(255,255,255,0.05); border-radius: 8px;">
                                <div style="font-size: 1.25rem; font-weight: 600; color: #4facfe;" id="fps-counter">60 FPS</div>
                                <div style="font-size: 0.75rem; color: #94a3b8;">渲染性能</div>
                            </div>
                            <div style="text-align: center; padding: 1rem; background: rgba(255,255,255,0.05); border-radius: 8px;">
                                <div style="font-size: 1.25rem; font-weight: 600; color: #4facfe;">128MB</div>
                                <div style="font-size: 0.75rem; color: #94a3b8;">内存使用</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="tab-panel" data-tab="settings">
                    <h3>系统设置</h3>
                    <p>自定义您的生物网络体验</p>
                </div>
            </div>
        </aside>

        <!-- 底部状态栏 -->
        <footer class="status-bar">
            <div class="status-group">
                <div class="status-item">
                    <i class="fas fa-eye"></i>
                    <span id="camera-position">视角: 生态总览</span>
                </div>
                <div class="status-item">
                    <i class="fas fa-hand-pointer"></i>
                    <span id="interaction-mode">交互: 触控模式</span>
                </div>
            </div>

            <div class="status-item">
                <i class="fas fa-heartbeat"></i>
                <span id="simulation-status">生态系统就绪</span>
            </div>

            <div class="status-group">
                <div class="status-item">
                    <i class="fas fa-clock"></i>
                    <span id="simulation-time">00:00</span>
                </div>
                <div class="status-item">
                    <i class="fas fa-tachometer-alt"></i>
                    <span id="fps-display">60 FPS</span>
                </div>
                <div class="status-item">
                    <i class="fas fa-memory"></i>
                    <span id="memory-usage">128MB</span>
                </div>
            </div>
        </footer>
    </div>

    <!-- JavaScript -->
    <script src="script.js"></script>
</body>
</html>
