<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Network Poetry - OSI 3D 生物仿生艺术模拟器</title>
    <meta name="description" content="探索OSI七层模型的3D生物仿生艺术体验，融合网络技术与生命美学的交互式学习平台">
    <meta name="keywords" content="OSI模型, 3D可视化, 生物仿生, 网络诗学, 交互艺术">
    
    <!-- Favicon -->
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🧬</text></svg>">
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=JetBrains+Mono:wght@400;500;600&display=swap" rel="stylesheet">
    
    <!-- Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Three.js - 使用可靠的CDN -->
    <script src="https://cdn.jsdelivr.net/npm/three@0.128.0/build/three.min.js"></script>
    
    <!-- Styles -->
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="app-container">
        <!-- 启动画面 -->
        <div class="startup-screen" id="startup-screen">
            <div class="startup-content">
                <div class="startup-logo">
                    <div class="logo-symbol">
                        <i class="fas fa-dna"></i>
                    </div>
                    <h1 class="startup-title">Network Poetry</h1>
                    <p class="startup-subtitle">OSI 3D 生物仿生艺术模拟器</p>
                </div>
                <div class="startup-progress">
                    <div class="progress-bar">
                        <div class="progress-fill"></div>
                    </div>
                    <p class="progress-text">正在初始化生物网络生态系统...</p>
                </div>
            </div>
        </div>

        <!-- 顶部导航栏 -->
        <nav class="top-navigation">
            <div class="nav-brand">
                <div class="brand-icon">
                    <i class="fas fa-dna"></i>
                </div>
                <div class="brand-text">
                    <span class="brand-name">Network Poetry</span>
                    <span class="brand-tagline">生物网络诗学</span>
                </div>
            </div>
            
            <div class="nav-actions">
                <button class="nav-btn" id="fullscreen-btn" title="全屏模式">
                    <i class="fas fa-expand"></i>
                </button>
                <button class="nav-btn" id="settings-btn" title="设置">
                    <i class="fas fa-cog"></i>
                </button>
                <button class="nav-btn" id="info-btn" title="关于">
                    <i class="fas fa-question-circle"></i>
                </button>
            </div>
        </nav>

        <!-- 主控制面板 -->
        <header class="control-panel">
            <div class="panel-section view-section">
                <h3 class="section-title">
                    <i class="fas fa-eye"></i>
                    视角控制
                </h3>
                <div class="view-controls">
                    <button class="view-btn active" data-view="overview" title="生态总览">
                        <i class="fas fa-globe-americas"></i>
                        <span>生态总览</span>
                        <div class="btn-glow"></div>
                    </button>
                    <button class="view-btn" data-view="building" title="层级聚焦">
                        <i class="fas fa-layer-group"></i>
                        <span>层级聚焦</span>
                        <div class="btn-glow"></div>
                    </button>
                    <button class="view-btn" data-view="datacenter" title="数据中心">
                        <i class="fas fa-server"></i>
                        <span>数据中心</span>
                        <div class="btn-glow"></div>
                    </button>
                    <button class="view-btn" data-view="network" title="网络拓扑">
                        <i class="fas fa-project-diagram"></i>
                        <span>网络拓扑</span>
                        <div class="btn-glow"></div>
                    </button>
                </div>
            </div>
            
            <div class="panel-section simulation-section">
                <h3 class="section-title">
                    <i class="fas fa-play-circle"></i>
                    仿生模拟
                </h3>
                <div class="simulation-controls">
                    <button class="sim-btn primary" id="start-simulation">
                        <i class="fas fa-play"></i>
                        <span>启动生命流</span>
                        <div class="btn-glow"></div>
                    </button>
                    <button class="sim-btn secondary" id="reset-simulation">
                        <i class="fas fa-redo"></i>
                        <span>重置生态</span>
                        <div class="btn-glow"></div>
                    </button>
                    <button class="sim-btn tertiary" id="toggle-labels">
                        <i class="fas fa-tags"></i>
                        <span>标签显示</span>
                        <div class="btn-glow"></div>
                    </button>
                </div>
            </div>
            
            <div class="panel-section interaction-section">
                <h3 class="section-title">
                    <i class="fas fa-hand-sparkles"></i>
                    交互模式
                </h3>
                <div class="interaction-modes">
                    <div class="interaction-mode active" data-mode="touch">
                        <div class="mode-icon">
                            <i class="fas fa-hand-pointer"></i>
                        </div>
                        <span>触控</span>
                    </div>
                    <div class="interaction-mode" data-mode="voice">
                        <div class="mode-icon">
                            <i class="fas fa-microphone"></i>
                        </div>
                        <span>语音</span>
                    </div>
                    <div class="interaction-mode" data-mode="gesture">
                        <div class="mode-icon">
                            <i class="fas fa-hand-peace"></i>
                        </div>
                        <span>手势</span>
                    </div>
                    <div class="interaction-mode" data-mode="gaze">
                        <div class="mode-icon">
                            <i class="fas fa-eye"></i>
                        </div>
                        <span>凝视</span>
                    </div>
                </div>
            </div>
        </header>

        <!-- 主要内容区域 -->
        <main class="main-workspace">
            <!-- 3D场景容器 -->
            <div class="scene-container">
                <canvas id="three-canvas"></canvas>
                
                <!-- 场景覆盖层 -->
                <div class="scene-overlay">
                    <!-- OSI层级标签 -->
                    <div class="layer-labels">
                        <div class="layer-label enhanced" data-layer="7" style="top: 15%; left: 25%;">
                            <div class="label-content">
                                <div class="label-header">
                                    <span class="label-number">7</span>
                                    <div class="label-pulse"></div>
                                </div>
                                <div class="label-info">
                                    <span class="label-name">应用层</span>
                                    <span class="label-english">Application</span>
                                    <span class="label-description">神经网络中枢</span>
                                </div>
                                <div class="label-bio-indicator">
                                    <i class="fas fa-brain"></i>
                                </div>
                            </div>
                        </div>
                        
                        <div class="layer-label enhanced" data-layer="6" style="top: 25%; left: 75%;">
                            <div class="label-content">
                                <div class="label-header">
                                    <span class="label-number">6</span>
                                    <div class="label-pulse"></div>
                                </div>
                                <div class="label-info">
                                    <span class="label-name">表示层</span>
                                    <span class="label-english">Presentation</span>
                                    <span class="label-description">转译生物器官</span>
                                </div>
                                <div class="label-bio-indicator">
                                    <i class="fas fa-dna"></i>
                                </div>
                            </div>
                        </div>
                        
                        <div class="layer-label enhanced" data-layer="5" style="top: 35%; left: 30%;">
                            <div class="label-content">
                                <div class="label-header">
                                    <span class="label-number">5</span>
                                    <div class="label-pulse"></div>
                                </div>
                                <div class="label-info">
                                    <span class="label-name">会话层</span>
                                    <span class="label-english">Session</span>
                                    <span class="label-description">对话细胞集群</span>
                                </div>
                                <div class="label-bio-indicator">
                                    <i class="fas fa-comments"></i>
                                </div>
                            </div>
                        </div>
                        
                        <div class="layer-label enhanced" data-layer="4" style="top: 45%; left: 70%;">
                            <div class="label-content">
                                <div class="label-header">
                                    <span class="label-number">4</span>
                                    <div class="label-pulse"></div>
                                </div>
                                <div class="label-info">
                                    <span class="label-name">传输层</span>
                                    <span class="label-english">Transport</span>
                                    <span class="label-description">循环系统节点</span>
                                </div>
                                <div class="label-bio-indicator">
                                    <i class="fas fa-heartbeat"></i>
                                </div>
                            </div>
                        </div>
                        
                        <div class="layer-label enhanced" data-layer="3" style="top: 55%; left: 20%;">
                            <div class="label-content">
                                <div class="label-header">
                                    <span class="label-number">3</span>
                                    <div class="label-pulse"></div>
                                </div>
                                <div class="label-info">
                                    <span class="label-name">网络层</span>
                                    <span class="label-english">Network</span>
                                    <span class="label-description">导航蜂群</span>
                                </div>
                                <div class="label-bio-indicator">
                                    <i class="fas fa-route"></i>
                                </div>
                            </div>
                        </div>
                        
                        <div class="layer-label enhanced" data-layer="2" style="top: 65%; left: 65%;">
                            <div class="label-content">
                                <div class="label-header">
                                    <span class="label-number">2</span>
                                    <div class="label-pulse"></div>
                                </div>
                                <div class="label-info">
                                    <span class="label-name">数据链路层</span>
                                    <span class="label-english">Data Link</span>
                                    <span class="label-description">微生物群落</span>
                                </div>
                                <div class="label-bio-indicator">
                                    <i class="fas fa-link"></i>
                                </div>
                            </div>
                        </div>
                        
                        <div class="layer-label enhanced" data-layer="1" style="top: 75%; left: 35%;">
                            <div class="label-content">
                                <div class="label-header">
                                    <span class="label-number">1</span>
                                    <div class="label-pulse"></div>
                                </div>
                                <div class="label-info">
                                    <span class="label-name">物理层</span>
                                    <span class="label-english">Physical</span>
                                    <span class="label-description">菌丝网络基质</span>
                                </div>
                                <div class="label-bio-indicator">
                                    <i class="fas fa-sitemap"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 数据流轨迹 -->
                    <div class="data-trail enhanced" id="data-trail">
                        <div class="data-packet-3d">
                            <div class="packet-core">
                                <i class="fas fa-dna"></i>
                            </div>
                            <div class="packet-aura"></div>
                        </div>
                    </div>
                    
                    <!-- 性能指标显示 -->
                    <div class="performance-indicators">
                        <div class="indicator" id="bio-health">
                            <i class="fas fa-heartbeat"></i>
                            <span class="indicator-label">生态健康</span>
                            <span class="indicator-value">98%</span>
                        </div>
                        <div class="indicator" id="data-flow">
                            <i class="fas fa-exchange-alt"></i>
                            <span class="indicator-label">数据流速</span>
                            <span class="indicator-value">1.2GB/s</span>
                        </div>
                        <div class="indicator" id="network-complexity">
                            <i class="fas fa-project-diagram"></i>
                            <span class="indicator-label">网络复杂度</span>
                            <span class="indicator-value">87%</span>
                        </div>
                    </div>
                </div>
                
                <!-- 加载覆盖层 -->
                <div class="loading-overlay enhanced" id="loading-overlay">
                    <div class="loading-content">
                        <div class="loading-animation">
                            <div class="dna-helix">
                                <div class="helix-strand strand-1"></div>
                                <div class="helix-strand strand-2"></div>
                            </div>
                        </div>
                        <h3>正在构建生物网络生态系统</h3>
                        <p class="loading-details">初始化有机几何体生成器...</p>
                        <div class="loading-progress">
                            <div class="progress-track">
                                <div class="progress-indicator"></div>
                            </div>
                            <span class="progress-percentage">0%</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 智能信息面板 -->
            <aside class="info-panel">
                <div class="panel-header">
                    <div class="panel-tabs">
                        <button class="tab-btn active" data-tab="overview">
                            <i class="fas fa-home"></i>
                            概览
                        </button>
                        <button class="tab-btn" data-tab="layers">
                            <i class="fas fa-layer-group"></i>
                            层级
                        </button>
                        <button class="tab-btn" data-tab="analytics">
                            <i class="fas fa-chart-line"></i>
                            分析
                        </button>
                        <button class="tab-btn" data-tab="settings">
                            <i class="fas fa-cog"></i>
                            设置
                        </button>
                    </div>
                    <button class="panel-toggle" id="panel-toggle">
                        <i class="fas fa-angle-right"></i>
                    </button>
                </div>
                
                <div class="panel-content" id="panel-content">
                    <div class="tab-panel active" data-tab="overview">
                        <div class="welcome-section">
                            <div class="welcome-header">
                                <div class="welcome-icon">
                                    <i class="fas fa-dna"></i>
                                </div>
                                <h3>网络诗学体验</h3>
                                <p>探索OSI七层模型的生物仿生艺术世界</p>
                            </div>
                            
                            <div class="ecosystem-overview">
                                <h4>
                                    <i class="fas fa-seedling"></i>
                                    生态系统概览
                                </h4>
                                <div class="ecosystem-stats">
                                    <div class="stat-item">
                                        <span class="stat-number">7</span>
                                        <span class="stat-label">生物层级</span>
                                    </div>
                                    <div class="stat-item">
                                        <span class="stat-number">∞</span>
                                        <span class="stat-label">数据流</span>
                                    </div>
                                    <div class="stat-item">
                                        <span class="stat-number">42</span>
                                        <span class="stat-label">有机设备</span>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="interaction-guide">
                                <h4>
                                    <i class="fas fa-hand-sparkles"></i>
                                    交互指南
                                </h4>
                                <div class="guide-items">
                                    <div class="guide-item">
                                        <div class="guide-icon">
                                            <i class="fas fa-mouse"></i>
                                        </div>
                                        <div class="guide-text">
                                            <span class="guide-action">鼠标拖拽</span>
                                            <span class="guide-desc">旋转3D生态系统</span>
                                        </div>
                                    </div>
                                    <div class="guide-item">
                                        <div class="guide-icon">
                                            <i class="fas fa-scroll"></i>
                                        </div>
                                        <div class="guide-text">
                                            <span class="guide-action">滚轮缩放</span>
                                            <span class="guide-desc">深入生物层级</span>
                                        </div>
                                    </div>
                                    <div class="guide-item">
                                        <div class="guide-icon">
                                            <i class="fas fa-hand-point-up"></i>
                                        </div>
                                        <div class="guide-text">
                                            <span class="guide-action">点击生物体</span>
                                            <span class="guide-desc">查看详细信息</span>
                                        </div>
                                    </div>
                                    <div class="guide-item">
                                        <div class="guide-icon">
                                            <i class="fas fa-magic"></i>
                                        </div>
                                        <div class="guide-text">
                                            <span class="guide-action">Shift+拖拽</span>
                                            <span class="guide-desc">绘制手势控制</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="tab-panel" data-tab="layers">
                        <div class="layers-section">
                            <h3>
                                <i class="fas fa-layer-group"></i>
                                生物层级详情
                            </h3>
                            <div class="layer-list" id="layer-details">
                                <!-- 动态生成层级详情 -->
                            </div>
                        </div>
                    </div>
                    
                    <div class="tab-panel" data-tab="analytics">
                        <div class="analytics-section">
                            <h3>
                                <i class="fas fa-chart-line"></i>
                                生态分析
                            </h3>
                            <div class="analytics-charts">
                                <!-- 图表内容 -->
                            </div>
                        </div>
                    </div>
                    
                    <div class="tab-panel" data-tab="settings">
                        <div class="settings-section">
                            <h3>
                                <i class="fas fa-cog"></i>
                                系统设置
                            </h3>
                            <div class="settings-groups">
                                <!-- 设置选项 -->
                            </div>
                        </div>
                    </div>
                </div>
            </aside>
        </main>

        <!-- 底部状态栏 -->
        <footer class="status-bar enhanced">
            <div class="status-left">
                <div class="status-group">
                    <div class="status-item">
                        <i class="fas fa-eye"></i>
                        <span id="camera-position">视角: 生态总览</span>
                    </div>
                    <div class="status-item">
                        <i class="fas fa-hand-pointer"></i>
                        <span id="interaction-mode">交互: 触控模式</span>
                    </div>
                </div>
            </div>
            
            <div class="status-center">
                <div class="simulation-status enhanced" id="simulation-status">
                    <div class="status-indicator">
                        <div class="status-dot"></div>
                        <div class="status-ring"></div>
                    </div>
                    <span class="status-text">生态系统就绪</span>
                </div>
            </div>
            
            <div class="status-right">
                <div class="status-group">
                    <div class="status-item">
                        <i class="fas fa-clock"></i>
                        <span id="simulation-time">00:00</span>
                    </div>
                    <div class="status-item">
                        <i class="fas fa-tachometer-alt"></i>
                        <span id="fps-counter">60 FPS</span>
                    </div>
                    <div class="status-item">
                        <i class="fas fa-memory"></i>
                        <span id="memory-usage">128MB</span>
                    </div>
                </div>
            </div>
        </footer>

        <!-- 设备详情模态框 -->
        <div class="modal-overlay" id="device-modal">
            <div class="modal-container">
                <div class="modal-header">
                    <div class="modal-title-section">
                        <i class="fas fa-dna modal-icon"></i>
                        <h3 id="device-title">生物设备详情</h3>
                    </div>
                    <button class="modal-close" id="close-modal">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body" id="device-details">
                    <!-- 设备详情内容将动态加载 -->
                </div>
                <div class="modal-footer">
                    <button class="modal-btn secondary" id="modal-cancel">关闭</button>
                    <button class="modal-btn primary" id="modal-action">深入探索</button>
                </div>
            </div>
        </div>

        <!-- 高级控制提示 -->
        <div class="advanced-hints" id="advanced-hints">
            <div class="hints-header">
                <h4>
                    <i class="fas fa-magic"></i>
                    高级交互
                </h4>
                <button class="hints-toggle">
                    <i class="fas fa-angle-up"></i>
                </button>
            </div>
            <div class="hints-content">
                <div class="hint-category">
                    <h5>手势控制</h5>
                    <div class="hint-items">
                        <div class="hint-item">
                            <kbd>Shift</kbd> + <i class="fas fa-mouse"></i>
                            <span>绘制手势</span>
                        </div>
                        <div class="hint-item">
                            <i class="fas fa-circle"></i>
                            <span>圆形激活层级</span>
                        </div>
                    </div>
                </div>
                <div class="hint-category">
                    <h5>语音控制</h5>
                    <div class="hint-items">
                        <div class="hint-item">
                            <kbd>Alt</kbd> + <kbd>1-7</kbd>
                            <span>快速导航</span>
                        </div>
                        <div class="hint-item">
                            <i class="fas fa-microphone"></i>
                            <span>"显示应用层"</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
    <!-- 布局优化脚本 -->
    <script>
        // 布局优化和响应式处理
        class LayoutManager {
            constructor() {
                this.initLayoutOptimizations();
                this.setupResponsiveHandlers();
                this.optimizePerformance();
            }

            initLayoutOptimizations() {
                // 防止内容溢出
                document.documentElement.style.overflow = 'hidden';
                document.body.style.overflow = 'hidden';
                
                // 优化触摸滚动
                if ('ontouchstart' in window) {
                    document.body.style.webkitOverflowScrolling = 'touch';
                    document.body.style.overscrollBehavior = 'contain';
                }
                
                // 设置正确的视窗高度（处理移动端地址栏）
                this.updateViewportHeight();
            }

            updateViewportHeight() {
                const vh = window.innerHeight * 0.01;
                document.documentElement.style.setProperty('--vh', `${vh}px`);
                
                // 更新应用容器高度
                const appContainer = document.querySelector('.app-container');
                if (appContainer) {
                    appContainer.style.height = `${window.innerHeight}px`;
                }
            }

            setupResponsiveHandlers() {
                let resizeTimer;
                window.addEventListener('resize', () => {
                    clearTimeout(resizeTimer);
                    resizeTimer = setTimeout(() => {
                        this.updateViewportHeight();
                        this.adjustLayoutForScreenSize();
                        this.optimizeForDevice();
                    }, 100);
                });

                // 监听方向改变
                window.addEventListener('orientationchange', () => {
                    setTimeout(() => {
                        this.updateViewportHeight();
                        this.adjustLayoutForScreenSize();
                    }, 300);
                });
            }

            adjustLayoutForScreenSize() {
                const width = window.innerWidth;
                const height = window.innerHeight;
                
                // 添加屏幕尺寸类名
                document.body.classList.remove('screen-xs', 'screen-sm', 'screen-md', 'screen-lg', 'screen-xl');
                
                if (width <= 480) {
                    document.body.classList.add('screen-xs');
                } else if (width <= 768) {
                    document.body.classList.add('screen-sm');
                } else if (width <= 1024) {
                    document.body.classList.add('screen-md');
                } else if (width <= 1440) {
                    document.body.classList.add('screen-lg');
                } else {
                    document.body.classList.add('screen-xl');
                }

                // 调整信息面板在小屏幕上的行为
                const infoPanel = document.querySelector('.info-panel');
                if (infoPanel && width <= 768) {
                    infoPanel.style.order = '-1';
                    infoPanel.style.borderLeft = 'none';
                    infoPanel.style.borderBottom = '1px solid var(--border-color)';
                } else if (infoPanel) {
                    infoPanel.style.order = '';
                    infoPanel.style.borderLeft = '1px solid var(--border-color)';
                    infoPanel.style.borderBottom = 'none';
                }
            }

            optimizeForDevice() {
                const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
                const isTouchDevice = 'ontouchstart' in window;
                
                if (isMobile || isTouchDevice) {
                    document.body.classList.add('touch-device');
                    this.optimizeForTouch();
                } else {
                    document.body.classList.add('pointer-device');
                }
            }

            optimizeForTouch() {
                // 增加触摸目标大小
                const touchTargets = document.querySelectorAll('.view-btn, .sim-btn, .nav-btn, .tab-btn, .interaction-mode');
                touchTargets.forEach(target => {
                    const currentMinHeight = parseInt(getComputedStyle(target).minHeight) || 0;
                    if (currentMinHeight < 44) {
                        target.style.minHeight = '44px';
                        target.style.minWidth = '44px';
                    }
                });

                // 优化滚动区域
                const scrollAreas = document.querySelectorAll('.panel-content, .modal-body');
                scrollAreas.forEach(area => {
                    area.style.webkitOverflowScrolling = 'touch';
                    area.style.overscrollBehavior = 'contain';
                });
            }

            optimizePerformance() {
                // 减少不必要的重绘
                const optimizeElements = document.querySelectorAll('.layer-label, .view-btn, .sim-btn');
                optimizeElements.forEach(el => {
                    el.style.willChange = 'transform, opacity';
                    el.style.backfaceVisibility = 'hidden';
                });

                // 优化动画性能
                if (window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
                    document.body.classList.add('reduced-motion');
                }
            }

            // 公共方法：切换信息面板
            toggleInfoPanel() {
                const infoPanel = document.querySelector('.info-panel');
                if (infoPanel) {
                    infoPanel.classList.toggle('collapsed');
                }
            }

            // 公共方法：显示通知
            showNotification(message, type = 'info') {
                const notification = document.createElement('div');
                notification.className = `toast-notification ${type}`;
                notification.innerHTML = `
                    <div class="toast-header">
                        <i class="fas fa-info-circle"></i>
                        <span>系统通知</span>
                    </div>
                    <div class="toast-body">${message}</div>
                    <button class="toast-close">&times;</button>
                `;
                
                document.body.appendChild(notification);
                
                // 显示动画
                setTimeout(() => notification.classList.add('show'), 100);
                
                // 自动隐藏
                setTimeout(() => {
                    notification.classList.remove('show');
                    setTimeout(() => notification.remove(), 400);
                }, 3000);
                
                // 点击关闭
                notification.querySelector('.toast-close').addEventListener('click', () => {
                    notification.classList.remove('show');
                    setTimeout(() => notification.remove(), 400);
                });
            }
        }

        // 初始化布局管理器
        document.addEventListener('DOMContentLoaded', () => {
            window.layoutManager = new LayoutManager();
            console.log('✅ 布局优化管理器已启动');
        });
    </script>
</body>
</html>