@echo off
chcp 65001 >nul
title Telegram 频道下载器安装脚本

echo 🚀 Telegram 频道下载器安装脚本
echo ==================================

REM 检查Python
echo 📋 检查Python环境...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 未找到Python，请先安装Python3
    pause
    exit /b 1
)

for /f "tokens=2" %%i in ('python --version 2^>^&1') do set PYTHON_VERSION=%%i
echo ✅ Python版本: %PYTHON_VERSION%

REM 检查pip
pip --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 未找到pip，请先安装pip
    pause
    exit /b 1
)
echo ✅ pip 已安装

REM 询问是否创建虚拟环境
set /p create_venv="🤔 是否创建Python虚拟环境? (推荐) [y/N]: "
if /i "%create_venv%"=="y" (
    echo 📦 创建虚拟环境...
    python -m venv telegram_downloader_env
    call telegram_downloader_env\Scripts\activate.bat
    echo ✅ 虚拟环境已激活
)

REM 升级pip
echo ⬆️  升级pip...
python -m pip install --upgrade pip

REM 安装依赖
echo 📦 安装Python依赖包...
pip install -r requirements.txt

if %errorlevel% neq 0 (
    echo ❌ 依赖安装失败
    pause
    exit /b 1
)
echo ✅ 依赖安装成功

REM 创建配置文件
if not exist "config.py" (
    echo 📝 创建配置文件...
    copy config_example.py config.py >nul
    echo ✅ 配置文件已创建: config.py
    echo ⚠️  请编辑 config.py 文件，填入你的API密钥和配置
) else (
    echo ✅ 配置文件已存在: config.py
)

REM 创建下载目录
if not exist "downloads" mkdir downloads
echo ✅ 下载目录已创建: downloads\

echo.
echo 🎉 安装完成！
echo.
echo 📋 下一步操作:
echo 1. 编辑 config.py 文件，填入你的配置信息
echo 2. 运行: python telegram_channel_downloader.py
echo    或者: python start.py
echo.
echo 📚 更多信息请查看 README.md
echo.
echo 🔗 有用的链接:
echo - 获取API密钥: https://my.telegram.org/apps
echo - 创建Bot: https://t.me/BotFather
echo - 获取用户ID: https://t.me/userinfobot

pause
