#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Telegram 频道/群组 文件下载脚本
支持异步并发下载，自动重试，远程控制等功能
"""

import os
import sys
import asyncio
import logging
from datetime import datetime, timedelta
from pathlib import Path
import subprocess
import time
import json
import hashlib
import mimetypes
import signal
import sqlite3
from typing import Optional, Dict, List, Set
import re

from telethon import TelegramClient, events
from telethon.tl.types import MessageMediaDocument, MessageMediaPhoto, MessageMediaWebPage
from telethon.errors import FloodWaitError, TimeoutError, SessionPasswordNeededError, PhoneCodeInvalidError
from telethon.tl.functions.messages import GetHistoryRequest
import aiofiles
import aiofiles.os

# ==================== 配置区域 ====================
# 尝试从配置文件导入
try:
    from config import *
    logger_config_loaded = True
except ImportError:
    logger_config_loaded = False
    # 必填配置
    API_ID = 'your_api_id'  # 从 https://my.telegram.org/apps 获取
    API_HASH = 'your_api_hash'  # 从 https://my.telegram.org/apps 获取
    BOT_TOKEN = 'your_bot_token'  # 从 @BotFather 获取
    ADMIN_ID = 123456789  # 你的Telegram用户ID
    CHAT_ID = 'your_channel_username'  # 频道用户名或ID

    # 下载配置
    DOWNLOAD_PATH = './downloads'  # 下载保存路径
    MAX_CONCURRENT = 10  # 最大并发下载数
    RETRY_TIMES = 3  # 重试次数
    RETRY_DELAY = 5  # 重试延迟(秒)
    DOWNLOAD_TIMEOUT = 300  # 下载超时时间(秒)

    # 文件过滤配置
    SKIP_STICKERS = True  # 跳过贴纸
    SKIP_GIFS = True  # 跳过GIF
    SKIP_ANIMATIONS = True  # 跳过动态贴纸
    MAX_FILE_SIZE = 2 * 1024 * 1024 * 1024  # 最大文件大小 2GB
    MIN_FILE_SIZE = 1024  # 最小文件大小 1KB
    ALLOWED_EXTENSIONS = []  # 允许的文件扩展名
    BLOCKED_EXTENSIONS = ['.exe', '.bat', '.cmd', '.scr']  # 禁止的扩展名

    # Google Drive 上传配置 (可选)
    ENABLE_GDRIVE_UPLOAD = False  # 是否启用GD上传
    GCLONE_CONFIG_PATH = './gclone_config.json'  # gclone配置文件路径
    GDRIVE_FOLDER_ID = 'your_gdrive_folder_id'  # GD文件夹ID
    DELETE_AFTER_UPLOAD = False  # 上传后删除本地文件

    # 监控配置
    ENABLE_MONITORING = False  # 是否启用新消息监控
    MONITORED_CHATS = []  # 监控的频道列表
    MONITORING_INTERVAL = 60  # 监控间隔(秒)

    # 数据库配置
    USE_DATABASE = True  # 是否使用数据库记录下载历史
    DATABASE_PATH = './download_history.db'  # 数据库文件路径

    # 会话配置
    SESSION_NAME = 'downloader_session'  # 会话文件名
    BOT_SESSION_NAME = 'bot_session'  # Bot会话文件名

    # 通知配置
    ENABLE_NOTIFICATIONS = True  # 是否启用通知
    NOTIFICATION_INTERVAL = 10  # 通知间隔
    NOTIFY_ON_ERROR = True  # 错误时通知

    # 自定义函数占位符
    def custom_file_filter(message, filename, file_size):
        return True

    def custom_filename_format(message, original_filename):
        return original_filename

    def custom_download_path(message, filename):
        return os.path.join(DOWNLOAD_PATH, filename)

# ==================== 全局变量 ====================
client: Optional[TelegramClient] = None
bot_client: Optional[TelegramClient] = None
download_tasks: Dict[int, asyncio.Task] = {}
failed_messages: List[int] = []
downloaded_files: Set[str] = set()
active_downloads: Set[int] = set()
shutdown_event = asyncio.Event()

stats = {
    'total_downloaded': 0,
    'total_failed': 0,
    'total_skipped': 0,
    'total_size': 0,
    'start_time': None,
    'last_activity': None,
    'current_chat': None,
    'active_tasks': 0
}

# ==================== 数据库管理类 ====================
class DatabaseManager:
    def __init__(self, db_path: str):
        self.db_path = db_path
        self.init_database()

    def init_database(self):
        """初始化数据库"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute('''
                    CREATE TABLE IF NOT EXISTS downloads (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        message_id INTEGER NOT NULL,
                        chat_id TEXT NOT NULL,
                        filename TEXT NOT NULL,
                        file_size INTEGER,
                        file_hash TEXT,
                        download_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        file_path TEXT,
                        status TEXT DEFAULT 'completed',
                        UNIQUE(message_id, chat_id)
                    )
                ''')

                conn.execute('''
                    CREATE TABLE IF NOT EXISTS failed_downloads (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        message_id INTEGER NOT NULL,
                        chat_id TEXT NOT NULL,
                        filename TEXT,
                        error_message TEXT,
                        retry_count INTEGER DEFAULT 0,
                        last_attempt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        UNIQUE(message_id, chat_id)
                    )
                ''')

                conn.execute('''
                    CREATE INDEX IF NOT EXISTS idx_downloads_chat_message
                    ON downloads(chat_id, message_id)
                ''')

                conn.commit()
                logger.info("✅ 数据库初始化完成")
        except Exception as e:
            logger.error(f"❌ 数据库初始化失败: {e}")

    def is_downloaded(self, message_id: int, chat_id: str) -> bool:
        """检查文件是否已下载"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute(
                    'SELECT 1 FROM downloads WHERE message_id = ? AND chat_id = ?',
                    (message_id, chat_id)
                )
                return cursor.fetchone() is not None
        except Exception as e:
            logger.error(f"❌ 检查下载记录失败: {e}")
            return False

    def record_download(self, message_id: int, chat_id: str, filename: str,
                       file_size: int, file_hash: str, file_path: str):
        """记录下载成功"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute('''
                    INSERT OR REPLACE INTO downloads
                    (message_id, chat_id, filename, file_size, file_hash, file_path)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (message_id, chat_id, filename, file_size, file_hash, file_path))
                conn.commit()
        except Exception as e:
            logger.error(f"❌ 记录下载失败: {e}")

    def record_failure(self, message_id: int, chat_id: str, filename: str, error: str):
        """记录下载失败"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute('''
                    INSERT OR REPLACE INTO failed_downloads
                    (message_id, chat_id, filename, error_message, retry_count, last_attempt)
                    VALUES (?, ?, ?, ?,
                           COALESCE((SELECT retry_count FROM failed_downloads
                                   WHERE message_id = ? AND chat_id = ?), 0) + 1,
                           CURRENT_TIMESTAMP)
                ''', (message_id, chat_id, filename, error, message_id, chat_id))
                conn.commit()
        except Exception as e:
            logger.error(f"❌ 记录失败信息失败: {e}")

    def get_stats(self) -> Dict:
        """获取统计信息"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute('''
                    SELECT
                        COUNT(*) as total_downloads,
                        SUM(file_size) as total_size,
                        COUNT(DISTINCT chat_id) as total_chats
                    FROM downloads
                ''')
                result = cursor.fetchone()

                cursor = conn.execute('SELECT COUNT(*) FROM failed_downloads')
                failed_count = cursor.fetchone()[0]

                return {
                    'total_downloads': result[0] or 0,
                    'total_size': result[1] or 0,
                    'total_chats': result[2] or 0,
                    'total_failed': failed_count
                }
        except Exception as e:
            logger.error(f"❌ 获取统计信息失败: {e}")
            return {'total_downloads': 0, 'total_size': 0, 'total_chats': 0, 'total_failed': 0}

# 初始化数据库管理器
db_manager = DatabaseManager(DATABASE_PATH) if USE_DATABASE else None

# ==================== 日志配置 ====================
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('telegram_downloader.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# ==================== 工具函数 ====================
def ensure_dir(path: str) -> None:
    """确保目录存在"""
    Path(path).mkdir(parents=True, exist_ok=True)

def get_file_extension(filename: str) -> str:
    """获取文件扩展名"""
    return Path(filename).suffix.lower()

def format_file_size(size: int) -> str:
    """格式化文件大小"""
    if size == 0:
        return "0 B"
    for unit in ['B', 'KB', 'MB', 'GB']:
        if size < 1024.0:
            return f"{size:.1f} {unit}"
        size /= 1024.0
    return f"{size:.1f} TB"

def sanitize_filename(filename: str) -> str:
    """清理文件名中的非法字符"""
    # 移除或替换非法字符
    invalid_chars = '<>:"/\\|?*'
    for char in invalid_chars:
        filename = filename.replace(char, '_')

    # 移除控制字符
    filename = re.sub(r'[\x00-\x1f\x7f-\x9f]', '', filename)

    # 处理连续的点和空格
    filename = re.sub(r'\.{2,}', '.', filename)
    filename = filename.strip('. ')

    # 限制文件名长度
    if len(filename) > 255:
        name, ext = os.path.splitext(filename)
        filename = name[:255-len(ext)] + ext

    return filename or 'unnamed_file'

def calculate_file_hash(file_path: str) -> str:
    """计算文件MD5哈希值"""
    hash_md5 = hashlib.md5()
    try:
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_md5.update(chunk)
        return hash_md5.hexdigest()
    except Exception as e:
        logger.error(f"❌ 计算文件哈希失败: {e}")
        return ""

def get_mime_type(filename: str) -> str:
    """获取文件MIME类型"""
    mime_type, _ = mimetypes.guess_type(filename)
    return mime_type or 'application/octet-stream'

def is_media_file(filename: str) -> bool:
    """判断是否为媒体文件"""
    media_extensions = {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp',
                       '.mp4', '.avi', '.mkv', '.mov', '.wmv', '.flv',
                       '.mp3', '.wav', '.flac', '.aac', '.ogg'}
    return get_file_extension(filename) in media_extensions

def format_duration(seconds: int) -> str:
    """格式化时间长度"""
    if seconds < 60:
        return f"{seconds}秒"
    elif seconds < 3600:
        return f"{seconds//60}分{seconds%60}秒"
    else:
        hours = seconds // 3600
        minutes = (seconds % 3600) // 60
        return f"{hours}小时{minutes}分"

def get_chat_info(entity) -> Dict[str, str]:
    """获取聊天信息"""
    if hasattr(entity, 'title'):
        return {
            'name': entity.title,
            'type': 'channel' if hasattr(entity, 'broadcast') and entity.broadcast else 'group',
            'id': str(entity.id)
        }
    elif hasattr(entity, 'first_name'):
        return {
            'name': f"{entity.first_name} {entity.last_name or ''}".strip(),
            'type': 'user',
            'id': str(entity.id)
        }
    else:
        return {
            'name': 'Unknown',
            'type': 'unknown',
            'id': str(getattr(entity, 'id', 'unknown'))
        }

async def upload_to_gdrive(file_path: str, folder_id: Optional[str] = None) -> bool:
    """上传文件到Google Drive"""
    if not ENABLE_GDRIVE_UPLOAD:
        return True

    try:
        # 检查gclone是否可用
        try:
            process = await asyncio.create_subprocess_exec(
                'gclone', 'version',
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            await process.communicate()
            if process.returncode != 0:
                logger.error("❌ gclone 不可用，请先安装 gclone")
                return False
        except FileNotFoundError:
            logger.error("❌ 未找到 gclone 命令，请先安装 gclone")
            return False

        # 构建上传命令
        cmd_args = ['gclone', 'copy', file_path, 'gdrive:']
        if folder_id:
            cmd_args.extend(['--drive-shared-with-me', '--drive-folder-id', folder_id])

        logger.info(f"📤 开始上传到GD: {os.path.basename(file_path)}")

        process = await asyncio.create_subprocess_exec(
            *cmd_args,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )

        stdout, stderr = await process.communicate()

        if process.returncode == 0:
            logger.info(f"✅ 文件已上传到GD: {os.path.basename(file_path)}")

            # 如果配置了上传后删除本地文件
            if DELETE_AFTER_UPLOAD:
                try:
                    await aiofiles.os.remove(file_path)
                    logger.info(f"🗑️ 已删除本地文件: {os.path.basename(file_path)}")
                except Exception as e:
                    logger.error(f"❌ 删除本地文件失败: {e}")

            return True
        else:
            error_msg = stderr.decode('utf-8', errors='ignore')
            logger.error(f"❌ GD上传失败: {error_msg}")
            return False

    except asyncio.TimeoutError:
        logger.error(f"❌ GD上传超时: {os.path.basename(file_path)}")
        return False
    except Exception as e:
        logger.error(f"❌ GD上传异常: {e}")
        return False

def should_skip_file(filename: str, file_size: int, message) -> tuple[bool, str]:
    """判断是否应该跳过文件"""
    # 检查文件大小
    if file_size > MAX_FILE_SIZE:
        return True, f"文件过大 ({format_file_size(file_size)})"

    if file_size < MIN_FILE_SIZE:
        return True, f"文件过小 ({format_file_size(file_size)})"

    # 检查文件扩展名
    ext = get_file_extension(filename)

    if BLOCKED_EXTENSIONS and ext in BLOCKED_EXTENSIONS:
        return True, f"禁止的文件类型 ({ext})"

    if ALLOWED_EXTENSIONS and ext not in ALLOWED_EXTENSIONS:
        return True, f"不允许的文件类型 ({ext})"

    # 检查特定文件类型
    if SKIP_STICKERS and ext in ['.webp', '.tgs']:
        return True, "跳过贴纸文件"

    if SKIP_GIFS and ext == '.gif':
        return True, "跳过GIF文件"

    if SKIP_ANIMATIONS and ext in ['.tgs', '.webm']:
        return True, "跳过动画文件"

    # 调用自定义过滤器
    try:
        if not custom_file_filter(message, filename, file_size):
            return True, "自定义过滤器拒绝"
    except Exception as e:
        logger.error(f"❌ 自定义过滤器错误: {e}")

    return False, ""

# ==================== 下载函数 ====================
async def download_file(message, semaphore: asyncio.Semaphore, chat_id: str) -> bool:
    """下载单个文件"""
    message_id = message.id

    # 检查是否已在下载队列中
    if message_id in active_downloads:
        return False

    active_downloads.add(message_id)

    try:
        async with semaphore:
            # 检查数据库记录
            if db_manager and db_manager.is_downloaded(message_id, chat_id):
                logger.info(f"⏭️ 文件已下载过: 消息ID {message_id}")
                stats['total_skipped'] += 1
                return True

            media = message.media
            if not media:
                return False

            # 获取文件信息
            filename, file_size, file_type = await get_file_info(message, media)
            if not filename:
                logger.warning(f"⚠️ 无法获取文件信息: 消息ID {message_id}")
                return False

            # 文件过滤检查
            should_skip, skip_reason = should_skip_file(filename, file_size, message)
            if should_skip:
                logger.info(f"⏭️ 跳过文件: {filename} - {skip_reason}")
                stats['total_skipped'] += 1
                return False

            # 应用自定义文件名格式
            try:
                filename = custom_filename_format(message, filename)
            except Exception as e:
                logger.error(f"❌ 自定义文件名格式化失败: {e}")

            filename = sanitize_filename(filename)

            # 获取下载路径
            try:
                file_path = custom_download_path(message, filename)
            except Exception as e:
                logger.error(f"❌ 自定义路径生成失败: {e}")
                file_path = os.path.join(DOWNLOAD_PATH, filename)

            # 确保目录存在
            ensure_dir(os.path.dirname(file_path))

            # 检查文件是否已存在
            if os.path.exists(file_path):
                # 验证文件完整性
                if await verify_file_integrity(file_path, file_size):
                    logger.info(f"⏭️ 文件已存在且完整: {filename}")

                    # 记录到数据库
                    if db_manager:
                        file_hash = calculate_file_hash(file_path)
                        db_manager.record_download(message_id, chat_id, filename,
                                                 file_size, file_hash, file_path)

                    stats['total_skipped'] += 1
                    return True
                else:
                    logger.warning(f"⚠️ 文件不完整，重新下载: {filename}")
                    try:
                        await aiofiles.os.remove(file_path)
                    except:
                        pass

            # 开始下载
            logger.info(f"📥 开始下载: {filename} ({format_file_size(file_size)})")
            stats['active_tasks'] += 1
            stats['last_activity'] = datetime.now()

            # 执行下载
            success = await perform_download(message, file_path, file_size)

            if success:
                # 验证下载完成的文件
                if await verify_file_integrity(file_path, file_size):
                    logger.info(f"✅ 下载完成: {filename}")
                    stats['total_downloaded'] += 1
                    stats['total_size'] += file_size

                    # 记录到数据库
                    if db_manager:
                        file_hash = calculate_file_hash(file_path)
                        db_manager.record_download(message_id, chat_id, filename,
                                                 file_size, file_hash, file_path)

                    # 上传到Google Drive
                    if ENABLE_GDRIVE_UPLOAD:
                        await upload_to_gdrive(file_path, GDRIVE_FOLDER_ID)

                    # 发送通知
                    if ENABLE_NOTIFICATIONS and stats['total_downloaded'] % NOTIFICATION_INTERVAL == 0:
                        await send_notification(f"📊 已下载 {stats['total_downloaded']} 个文件")

                    return True
                else:
                    logger.error(f"❌ 文件下载不完整: {filename}")
                    try:
                        await aiofiles.os.remove(file_path)
                    except:
                        pass
                    raise Exception("文件下载不完整")
            else:
                raise Exception("下载失败")

    except FloodWaitError as e:
        logger.warning(f"⏳ 触发限流，等待 {e.seconds} 秒")
        await asyncio.sleep(e.seconds)
        return False

    except Exception as e:
        logger.error(f"❌ 下载失败 {filename}: {e}")
        stats['total_failed'] += 1
        failed_messages.append(message_id)

        # 记录失败信息
        if db_manager:
            db_manager.record_failure(message_id, chat_id, filename, str(e))

        # 错误通知
        if NOTIFY_ON_ERROR:
            await send_notification(f"❌ 下载失败: {filename}\n错误: {str(e)}")

        return False

    finally:
        active_downloads.discard(message_id)
        stats['active_tasks'] = max(0, stats['active_tasks'] - 1)

async def get_file_info(message, media) -> tuple[Optional[str], int, str]:
    """获取文件信息"""
    try:
        if hasattr(media, 'document') and media.document:
            document = media.document
            file_size = document.size
            mime_type = document.mime_type or 'application/octet-stream'

            # 获取文件名
            filename = None
            for attr in document.attributes:
                if hasattr(attr, 'file_name') and attr.file_name:
                    filename = attr.file_name
                    break

            if not filename:
                # 根据MIME类型生成文件名
                ext = mimetypes.guess_extension(mime_type) or '.bin'
                filename = f"document_{message.id}{ext}"

            return filename, file_size, 'document'

        elif hasattr(media, 'photo') and media.photo:
            # 获取最大尺寸的照片
            largest_size = max(media.photo.sizes, key=lambda x: getattr(x, 'size', 0))
            file_size = getattr(largest_size, 'size', 0)
            filename = f"photo_{message.id}_{message.date.strftime('%Y%m%d_%H%M%S')}.jpg"

            return filename, file_size, 'photo'

        elif hasattr(media, 'webpage') and media.webpage:
            # 网页预览，通常不需要下载
            return None, 0, 'webpage'

        else:
            logger.warning(f"⚠️ 不支持的媒体类型: {type(media)}")
            return None, 0, 'unknown'

    except Exception as e:
        logger.error(f"❌ 获取文件信息失败: {e}")
        return None, 0, 'error'

async def perform_download(message, file_path: str, expected_size: int) -> bool:
    """执行文件下载"""
    max_retries = RETRY_TIMES
    retry_count = 0

    while retry_count < max_retries:
        try:
            # 使用超时控制
            await asyncio.wait_for(
                client.download_media(message, file_path),
                timeout=DOWNLOAD_TIMEOUT
            )
            return True

        except asyncio.TimeoutError:
            retry_count += 1
            logger.warning(f"⏰ 下载超时，重试 {retry_count}/{max_retries}")
            if retry_count < max_retries:
                await asyncio.sleep(RETRY_DELAY * retry_count)

        except FloodWaitError as e:
            logger.warning(f"⏳ 触发限流，等待 {e.seconds} 秒")
            await asyncio.sleep(e.seconds)
            # 限流不计入重试次数

        except Exception as e:
            retry_count += 1
            logger.error(f"❌ 下载异常 {retry_count}/{max_retries}: {e}")
            if retry_count < max_retries:
                await asyncio.sleep(RETRY_DELAY * retry_count)

    return False

async def verify_file_integrity(file_path: str, expected_size: int) -> bool:
    """验证文件完整性"""
    try:
        if not os.path.exists(file_path):
            return False

        actual_size = os.path.getsize(file_path)
        if expected_size > 0 and actual_size != expected_size:
            logger.warning(f"⚠️ 文件大小不匹配: 期望 {expected_size}, 实际 {actual_size}")
            return False

        return True
    except Exception as e:
        logger.error(f"❌ 验证文件完整性失败: {e}")
        return False

async def send_notification(message: str) -> None:
    """发送通知消息"""
    try:
        if bot_client and ADMIN_ID:
            await bot_client.send_message(ADMIN_ID, message)
    except Exception as e:
        logger.error(f"❌ 发送通知失败: {e}")

async def download_channel_files(chat_id: str, start_id: int = 0, limit: Optional[int] = None) -> Dict:
    """下载频道文件"""
    try:
        entity = await client.get_entity(chat_id)
        chat_info = get_chat_info(entity)

        logger.info(f"🎯 开始扫描{chat_info['type']}: {chat_info['name']}")
        stats['current_chat'] = chat_info['name']

        # 发送开始通知
        await send_notification(f"🚀 开始下载任务\n📺 {chat_info['type']}: {chat_info['name']}\n🆔 起始ID: {start_id}")

        semaphore = asyncio.Semaphore(MAX_CONCURRENT)
        tasks = []
        processed_count = 0

        # 获取消息历史
        async for message in client.iter_messages(
            entity,
            offset_id=start_id,
            reverse=True,
            limit=limit
        ):
            # 检查停止信号
            if shutdown_event.is_set():
                logger.info("🛑 收到停止信号，中断下载")
                break

            processed_count += 1

            if message.media:
                # 创建下载任务
                task = asyncio.create_task(
                    download_file(message, semaphore, chat_info['id'])
                )
                tasks.append(task)
                download_tasks[message.id] = task

                # 批量处理任务，避免内存占用过多
                if len(tasks) >= MAX_CONCURRENT * 3:
                    completed_tasks = await asyncio.gather(*tasks, return_exceptions=True)

                    # 清理完成的任务
                    for i, task in enumerate(tasks):
                        if hasattr(task, 'get_name'):
                            download_tasks.pop(task.get_name(), None)

                    tasks.clear()

                    # 记录进度
                    if processed_count % 100 == 0:
                        logger.info(f"📊 已处理 {processed_count} 条消息")

            # 定期检查内存使用
            if processed_count % 1000 == 0:
                await asyncio.sleep(0.1)  # 让出控制权

        # 处理剩余任务
        if tasks:
            await asyncio.gather(*tasks, return_exceptions=True)
            tasks.clear()

        # 生成下载报告
        report = generate_download_report(chat_info['name'])
        logger.info(f"🎉 频道扫描完成: {chat_info['name']}")

        # 发送完成通知
        await send_notification(f"✅ 下载任务完成\n{report}")

        return {
            'success': True,
            'chat_name': chat_info['name'],
            'processed_messages': processed_count,
            'downloaded_files': stats['total_downloaded'],
            'failed_files': stats['total_failed'],
            'skipped_files': stats['total_skipped']
        }

    except Exception as e:
        error_msg = f"❌ 频道扫描失败: {e}"
        logger.error(error_msg)
        await send_notification(error_msg)
        return {
            'success': False,
            'error': str(e),
            'processed_messages': processed_count if 'processed_count' in locals() else 0
        }

def generate_download_report(chat_name: str) -> str:
    """生成下载报告"""
    runtime = datetime.now() - stats['start_time'] if stats['start_time'] else timedelta(0)

    report = f"""
📊 下载报告 - {chat_name}
⏰ 运行时间: {format_duration(int(runtime.total_seconds()))}
✅ 成功下载: {stats['total_downloaded']} 个文件
❌ 下载失败: {stats['total_failed']} 个文件
⏭️ 跳过文件: {stats['total_skipped']} 个文件
📦 总大小: {format_file_size(stats['total_size'])}
🔄 活跃任务: {stats['active_tasks']}
    """.strip()

    return report

async def retry_failed_downloads(chat_id: str) -> None:
    """重试失败的下载"""
    if not db_manager:
        logger.warning("⚠️ 数据库未启用，无法重试失败下载")
        return

    try:
        with sqlite3.connect(db_manager.db_path) as conn:
            cursor = conn.execute('''
                SELECT message_id, filename, retry_count
                FROM failed_downloads
                WHERE chat_id = ? AND retry_count < ?
                ORDER BY last_attempt ASC
            ''', (chat_id, RETRY_TIMES))

            failed_records = cursor.fetchall()

        if not failed_records:
            logger.info("✅ 没有需要重试的失败下载")
            return

        logger.info(f"🔄 开始重试 {len(failed_records)} 个失败下载")

        entity = await client.get_entity(chat_id)
        semaphore = asyncio.Semaphore(MAX_CONCURRENT)

        for message_id, filename, retry_count in failed_records:
            try:
                message = await client.get_messages(entity, ids=message_id)
                if message and message.media:
                    await download_file(message, semaphore, chat_id)
            except Exception as e:
                logger.error(f"❌ 重试下载失败 {filename}: {e}")

        logger.info("🎉 失败下载重试完成")

    except Exception as e:
        logger.error(f"❌ 重试失败下载异常: {e}")

# ==================== 信号处理 ====================
def signal_handler(signum, frame):
    """处理系统信号"""
    logger.info(f"📡 收到信号 {signum}，准备优雅关闭...")
    shutdown_event.set()

# 注册信号处理器
signal.signal(signal.SIGINT, signal_handler)
signal.signal(signal.SIGTERM, signal_handler)

# ==================== Bot 控制函数 ====================
def setup_bot_handlers():
    """设置Bot事件处理器"""

    @bot_client.on(events.NewMessage(pattern=r'/start(?:\s+(.+))?'))
    async def start_handler(event):
        """处理 /start 命令"""
        if event.sender_id != ADMIN_ID:
            await event.reply("❌ 权限不足")
            return

        try:
            match = event.pattern_match
            args = match.group(1).split() if match.group(1) else []

            if len(args) < 2:
                help_msg = """
🤖 Telegram 频道下载器

📋 命令格式:
/start <频道链接> <起始消息ID> [限制数量]

📝 示例:
/start @channel_name 0
/start https://t.me/channel_name 100
/start @channel_name 0 1000

📊 其他命令:
/status - 查看下载状态
/stop - 停止当前下载
/retry <频道链接> - 重试失败下载
/stats - 查看详细统计
/help - 显示帮助信息
                """.strip()
                await event.reply(help_msg)
                return

            chat_link = args[0]
            start_id = int(args[1])
            limit = int(args[2]) if len(args) > 2 else None

            # 检查是否有正在运行的任务
            if stats['start_time'] and stats['active_tasks'] > 0:
                await event.reply("⚠️ 已有下载任务正在运行，请先停止或等待完成")
                return

            await event.reply(f"🚀 开始下载任务\n📺 频道: {chat_link}\n🆔 起始ID: {start_id}" +
                            (f"\n📊 限制数量: {limit}" if limit else ""))

            # 重置统计信息
            stats.update({
                'total_downloaded': 0,
                'total_failed': 0,
                'total_skipped': 0,
                'total_size': 0,
                'start_time': datetime.now(),
                'active_tasks': 0
            })

            # 启动下载任务
            asyncio.create_task(download_channel_files(chat_link, start_id, limit))

        except ValueError:
            await event.reply("❌ 消息ID必须是数字")
        except Exception as e:
            await event.reply(f"❌ 启动失败: {e}")

    @bot_client.on(events.NewMessage(pattern='/status'))
    async def status_handler(event):
        """查看下载状态"""
        if event.sender_id != ADMIN_ID:
            await event.reply("❌ 权限不足")
            return

        try:
            if stats['start_time']:
                runtime = datetime.now() - stats['start_time']

                # 获取数据库统计
                db_stats = db_manager.get_stats() if db_manager else {}

                status_msg = f"""
📊 下载状态报告
⏰ 运行时间: {format_duration(int(runtime.total_seconds()))}
📺 当前频道: {stats.get('current_chat', '无')}
✅ 成功下载: {stats['total_downloaded']} 个文件
❌ 下载失败: {stats['total_failed']} 个文件
⏭️ 跳过文件: {stats['total_skipped']} 个文件
� 下载大小: {format_file_size(stats['total_size'])}
�🔄 活跃任务: {stats['active_tasks']}
⏱️ 最后活动: {stats.get('last_activity', '无').strftime('%H:%M:%S') if stats.get('last_activity') else '无'}

📈 历史统计:
📁 总下载数: {db_stats.get('total_downloads', 0)}
💾 总大小: {format_file_size(db_stats.get('total_size', 0))}
📺 频道数: {db_stats.get('total_chats', 0)}
                """.strip()
            else:
                status_msg = "📊 暂无下载任务运行"

            await event.reply(status_msg)

        except Exception as e:
            await event.reply(f"❌ 获取状态失败: {e}")

    @bot_client.on(events.NewMessage(pattern='/stop'))
    async def stop_handler(event):
        """停止下载任务"""
        if event.sender_id != ADMIN_ID:
            await event.reply("❌ 权限不足")
            return

        try:
            shutdown_event.set()

            # 取消所有下载任务
            cancelled_count = 0
            for task in download_tasks.values():
                if not task.done():
                    task.cancel()
                    cancelled_count += 1

            download_tasks.clear()
            active_downloads.clear()

            await event.reply(f"🛑 已停止下载任务\n📊 取消了 {cancelled_count} 个活跃任务")

            # 重置停止事件，准备下次使用
            shutdown_event.clear()

        except Exception as e:
            await event.reply(f"❌ 停止任务失败: {e}")

    @bot_client.on(events.NewMessage(pattern=r'/retry(?:\s+(.+))?'))
    async def retry_handler(event):
        """重试失败的下载"""
        if event.sender_id != ADMIN_ID:
            await event.reply("❌ 权限不足")
            return

        try:
            match = event.pattern_match
            chat_link = match.group(1) if match.group(1) else None

            if not chat_link:
                await event.reply("❌ 请指定频道链接\n格式: /retry <频道链接>")
                return

            await event.reply(f"🔄 开始重试失败下载: {chat_link}")

            entity = await client.get_entity(chat_link)
            chat_info = get_chat_info(entity)

            await retry_failed_downloads(chat_info['id'])
            await event.reply("✅ 失败下载重试完成")

        except Exception as e:
            await event.reply(f"❌ 重试失败: {e}")

    @bot_client.on(events.NewMessage(pattern='/stats'))
    async def stats_handler(event):
        """查看详细统计信息"""
        if event.sender_id != ADMIN_ID:
            await event.reply("❌ 权限不足")
            return

        try:
            if not db_manager:
                await event.reply("❌ 数据库未启用")
                return

            db_stats = db_manager.get_stats()

            stats_msg = f"""
📈 详细统计信息

📊 总体数据:
📁 总下载文件: {db_stats['total_downloads']}
💾 总下载大小: {format_file_size(db_stats['total_size'])}
📺 涉及频道: {db_stats['total_chats']}
❌ 失败次数: {db_stats['total_failed']}

🔄 当前会话:
✅ 成功: {stats['total_downloaded']}
❌ 失败: {stats['total_failed']}
⏭️ 跳过: {stats['total_skipped']}
📦 大小: {format_file_size(stats['total_size'])}

💾 存储信息:
📁 下载目录: {os.path.abspath(DOWNLOAD_PATH)}
💿 数据库: {os.path.abspath(DATABASE_PATH)}
            """.strip()

            await event.reply(stats_msg)

        except Exception as e:
            await event.reply(f"❌ 获取统计信息失败: {e}")

    @bot_client.on(events.NewMessage(pattern='/help'))
    async def help_handler(event):
        """显示帮助信息"""
        if event.sender_id != ADMIN_ID:
            await event.reply("❌ 权限不足")
            return

        help_msg = """
🤖 Telegram 频道下载器 - 帮助

📋 基本命令:
/start <频道> <起始ID> [限制] - 开始下载
/status - 查看当前状态
/stop - 停止下载任务
/retry <频道> - 重试失败下载
/stats - 详细统计信息
/help - 显示此帮助

📝 使用示例:
/start @channel_name 0
/start https://t.me/channel 100 1000
/retry @channel_name

⚙️ 当前配置:
📁 下载路径: {DOWNLOAD_PATH}
🔄 最大并发: {MAX_CONCURRENT}
📦 最大文件: {format_file_size(MAX_FILE_SIZE)}
☁️ GD上传: {'启用' if ENABLE_GDRIVE_UPLOAD else '禁用'}

📞 支持:
如有问题请检查日志文件或联系管理员
        """.strip()

        await event.reply(help_msg)

async def authenticate_client(client: TelegramClient, session_name: str) -> bool:
    """认证客户端"""
    try:
        await client.start()

        if not await client.is_user_authorized():
            phone = input(f"请输入手机号 (用于 {session_name}): ")
            await client.send_code_request(phone)

            try:
                code = input("请输入验证码: ")
                await client.sign_in(phone, code)
            except SessionPasswordNeededError:
                password = input("请输入两步验证密码: ")
                await client.sign_in(password=password)

        me = await client.get_me()
        logger.info(f"✅ {session_name} 认证成功: {me.first_name}")
        return True

    except Exception as e:
        logger.error(f"❌ {session_name} 认证失败: {e}")
        return False

async def setup_monitoring():
    """设置消息监控"""
    if not ENABLE_MONITORING or not MONITORED_CHATS:
        return

    logger.info("🔍 启用消息监控功能")

    @client.on(events.NewMessage)
    async def monitor_handler(event):
        """监控新消息"""
        try:
            chat = await event.get_chat()
            chat_info = get_chat_info(chat)

            # 检查是否在监控列表中
            chat_identifier = f"@{chat.username}" if hasattr(chat, 'username') and chat.username else str(chat.id)

            if chat_identifier in MONITORED_CHATS or str(chat.id) in MONITORED_CHATS:
                if event.media:
                    logger.info(f"🔔 检测到新媒体消息: {chat_info['name']}")

                    # 自动下载新文件
                    semaphore = asyncio.Semaphore(1)  # 监控模式使用单线程
                    await download_file(event.message, semaphore, chat_info['id'])

        except Exception as e:
            logger.error(f"❌ 消息监控异常: {e}")

# ==================== 主函数 ====================
async def main():
    """主函数"""
    global client, bot_client

    # 检查配置
    if API_ID == 'your_api_id' or API_HASH == 'your_api_hash':
        logger.error("❌ 请先配置 API_ID 和 API_HASH")
        logger.error("💡 请复制 config_example.py 为 config.py 并填入正确的配置")
        return

    if BOT_TOKEN == 'your_bot_token':
        logger.error("❌ 请先配置 BOT_TOKEN")
        return

    if ADMIN_ID == 123456789:
        logger.error("❌ 请先配置 ADMIN_ID")
        return

    # 创建必要目录
    ensure_dir(DOWNLOAD_PATH)
    ensure_dir(os.path.dirname(DATABASE_PATH))

    # 初始化客户端
    client = TelegramClient(SESSION_NAME, API_ID, API_HASH)
    bot_client = TelegramClient(BOT_SESSION_NAME, API_ID, API_HASH)

    try:
        logger.info("🚀 正在启动 Telegram 频道下载器...")

        # 认证用户客户端
        if not await authenticate_client(client, "用户客户端"):
            return

        # 启动Bot客户端
        await bot_client.start(bot_token=BOT_TOKEN)
        bot_me = await bot_client.get_me()
        logger.info(f"🤖 Bot 已启动: @{bot_me.username}")

        # 设置Bot事件处理器
        setup_bot_handlers()

        # 设置消息监控
        await setup_monitoring()

        # 显示启动信息
        logger.info("✅ Telegram 下载器启动完成")
        logger.info(f"📁 下载路径: {os.path.abspath(DOWNLOAD_PATH)}")
        logger.info(f"⚡ 最大并发: {MAX_CONCURRENT}")
        logger.info(f"📦 最大文件: {format_file_size(MAX_FILE_SIZE)}")
        logger.info(f"💾 数据库: {'启用' if USE_DATABASE else '禁用'}")
        logger.info(f"☁️ GD上传: {'启用' if ENABLE_GDRIVE_UPLOAD else '禁用'}")
        logger.info(f"🔍 消息监控: {'启用' if ENABLE_MONITORING else '禁用'}")
        logger.info(f"👤 管理员ID: {ADMIN_ID}")

        # 发送启动通知
        await send_notification("🚀 Telegram 频道下载器已启动\n发送 /help 查看使用帮助")

        # 保持运行
        logger.info("🔄 程序运行中，按 Ctrl+C 停止...")
        await bot_client.run_until_disconnected()

    except KeyboardInterrupt:
        logger.info("📡 收到中断信号，正在关闭...")
    except Exception as e:
        logger.error(f"❌ 启动失败: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
    finally:
        # 清理资源
        logger.info("🧹 正在清理资源...")

        # 取消所有下载任务
        for task in download_tasks.values():
            if not task.done():
                task.cancel()

        # 关闭客户端
        if client and client.is_connected():
            await client.disconnect()
            logger.info("✅ 用户客户端已断开")

        if bot_client and bot_client.is_connected():
            await bot_client.disconnect()
            logger.info("✅ Bot客户端已断开")

        logger.info("👋 程序已完全停止")

if __name__ == '__main__':
    try:
        # 设置事件循环策略 (Windows)
        if sys.platform == 'win32':
            asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())

        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 程序已停止")
    except Exception as e:
        print(f"❌ 程序异常: {e}")
        import traceback
        traceback.print_exc()
