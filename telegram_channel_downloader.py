#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Telegram 频道/群组 文件下载脚本
支持异步并发下载，自动重试，远程控制等功能
"""

import os
import sys
import asyncio
import logging
from datetime import datetime
from pathlib import Path
import subprocess
import time
import json

from telethon import TelegramClient, events
from telethon.tl.types import MessageMediaDocument, MessageMediaPhoto
from telethon.errors import FloodWaitError, TimeoutError
import aiofiles

# ==================== 配置区域 ====================
# 必填配置
API_ID = 'your_api_id'  # 从 https://my.telegram.org/apps 获取
API_HASH = 'your_api_hash'  # 从 https://my.telegram.org/apps 获取
BOT_TOKEN = 'your_bot_token'  # 从 @BotFather 获取
ADMIN_ID = 123456789  # 你的Telegram用户ID
CHAT_ID = 'your_channel_username'  # 频道用户名或ID

# 下载配置
DOWNLOAD_PATH = './downloads'  # 下载保存路径
MAX_CONCURRENT = 10  # 最大并发下载数
RETRY_TIMES = 3  # 重试次数
RETRY_DELAY = 5  # 重试延迟(秒)

# 文件过滤配置
SKIP_STICKERS = True  # 跳过贴纸
SKIP_GIFS = True  # 跳过GIF
SKIP_ANIMATIONS = True  # 跳过动态贴纸
MAX_FILE_SIZE = 2 * 1024 * 1024 * 1024  # 最大文件大小 2GB

# Google Drive 上传配置 (可选)
ENABLE_GDRIVE_UPLOAD = False  # 是否启用GD上传
GCLONE_CONFIG_PATH = './gclone_config.json'  # gclone配置文件路径
GDRIVE_FOLDER_ID = 'your_gdrive_folder_id'  # GD文件夹ID

# 监控配置
ENABLE_MONITORING = False  # 是否启用新消息监控
MONITORED_CHATS = []  # 监控的频道列表

# ==================== 全局变量 ====================
client = None
bot_client = None
download_tasks = {}
failed_messages = []
stats = {
    'total_downloaded': 0,
    'total_failed': 0,
    'start_time': None
}

# ==================== 日志配置 ====================
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('telegram_downloader.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# ==================== 工具函数 ====================
def ensure_dir(path):
    """确保目录存在"""
    Path(path).mkdir(parents=True, exist_ok=True)

def get_file_extension(filename):
    """获取文件扩展名"""
    return Path(filename).suffix.lower()

def format_file_size(size):
    """格式化文件大小"""
    for unit in ['B', 'KB', 'MB', 'GB']:
        if size < 1024.0:
            return f"{size:.1f} {unit}"
        size /= 1024.0
    return f"{size:.1f} TB"

def sanitize_filename(filename):
    """清理文件名中的非法字符"""
    invalid_chars = '<>:"/\\|?*'
    for char in invalid_chars:
        filename = filename.replace(char, '_')
    return filename[:255]  # 限制文件名长度

async def upload_to_gdrive(file_path, folder_id=None):
    """上传文件到Google Drive"""
    if not ENABLE_GDRIVE_UPLOAD:
        return True
    
    try:
        folder_arg = f"--drive-shared-with-me --drive-folder-id {folder_id}" if folder_id else ""
        cmd = f"gclone copy '{file_path}' gdrive: {folder_arg}"
        
        process = await asyncio.create_subprocess_shell(
            cmd,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )
        
        stdout, stderr = await process.communicate()
        
        if process.returncode == 0:
            logger.info(f"✅ 文件已上传到GD: {file_path}")
            return True
        else:
            logger.error(f"❌ GD上传失败: {stderr.decode()}")
            return False
            
    except Exception as e:
        logger.error(f"❌ GD上传异常: {e}")
        return False

# ==================== 下载函数 ====================
async def download_file(message, semaphore):
    """下载单个文件"""
    async with semaphore:
        try:
            media = message.media
            if not media:
                return False
            
            # 获取文件信息
            if hasattr(media, 'document') and media.document:
                document = media.document
                file_size = document.size
                
                # 获取文件名
                filename = None
                for attr in document.attributes:
                    if hasattr(attr, 'file_name') and attr.file_name:
                        filename = attr.file_name
                        break
                
                if not filename:
                    filename = f"file_{message.id}.{document.mime_type.split('/')[-1]}"
                
            elif hasattr(media, 'photo') and media.photo:
                file_size = sum(size.size for size in media.photo.sizes if hasattr(size, 'size'))
                filename = f"photo_{message.id}.jpg"
            else:
                return False
            
            # 文件过滤
            if file_size > MAX_FILE_SIZE:
                logger.info(f"⏭️ 跳过大文件: {filename} ({format_file_size(file_size)})")
                return False
            
            ext = get_file_extension(filename)
            if SKIP_STICKERS and ext in ['.webp', '.tgs']:
                logger.info(f"⏭️ 跳过贴纸: {filename}")
                return False
            
            if SKIP_GIFS and ext == '.gif':
                logger.info(f"⏭️ 跳过GIF: {filename}")
                return False
            
            # 准备下载路径
            filename = sanitize_filename(filename)
            file_path = os.path.join(DOWNLOAD_PATH, filename)
            
            # 检查文件是否已存在
            if os.path.exists(file_path):
                logger.info(f"⏭️ 文件已存在: {filename}")
                return True
            
            # 开始下载
            logger.info(f"📥 开始下载: {filename} ({format_file_size(file_size)})")
            
            await client.download_media(message, file_path)
            
            logger.info(f"✅ 下载完成: {filename}")
            stats['total_downloaded'] += 1
            
            # 上传到Google Drive
            if ENABLE_GDRIVE_UPLOAD:
                await upload_to_gdrive(file_path, GDRIVE_FOLDER_ID)
            
            return True
            
        except FloodWaitError as e:
            logger.warning(f"⏳ 触发限流，等待 {e.seconds} 秒")
            await asyncio.sleep(e.seconds)
            return False
            
        except Exception as e:
            logger.error(f"❌ 下载失败 {message.id}: {e}")
            stats['total_failed'] += 1
            failed_messages.append(message.id)
            return False

async def download_channel_files(chat_id, start_id=0):
    """下载频道文件"""
    try:
        entity = await client.get_entity(chat_id)
        logger.info(f"🎯 开始扫描频道: {entity.title}")
        
        semaphore = asyncio.Semaphore(MAX_CONCURRENT)
        tasks = []
        
        async for message in client.iter_messages(entity, offset_id=start_id, reverse=True):
            if message.media:
                task = asyncio.create_task(download_file(message, semaphore))
                tasks.append(task)
                
                # 批量处理任务
                if len(tasks) >= MAX_CONCURRENT * 2:
                    await asyncio.gather(*tasks, return_exceptions=True)
                    tasks.clear()
        
        # 处理剩余任务
        if tasks:
            await asyncio.gather(*tasks, return_exceptions=True)
        
        logger.info(f"🎉 频道扫描完成: {entity.title}")
        
    except Exception as e:
        logger.error(f"❌ 频道扫描失败: {e}")

# ==================== Bot 控制函数 ====================
@bot_client.on(events.NewMessage(pattern='/start'))
async def start_handler(event):
    """处理 /start 命令"""
    if event.sender_id != ADMIN_ID:
        return
    
    try:
        parts = event.message.text.split()
        if len(parts) < 3:
            await event.reply("❌ 格式错误！使用: /start <频道链接> <起始消息ID>")
            return
        
        chat_link = parts[1]
        start_id = int(parts[2])
        
        await event.reply(f"🚀 开始下载任务\n📺 频道: {chat_link}\n🆔 起始ID: {start_id}")
        
        # 启动下载任务
        stats['start_time'] = datetime.now()
        asyncio.create_task(download_channel_files(chat_link, start_id))
        
    except Exception as e:
        await event.reply(f"❌ 启动失败: {e}")

@bot_client.on(events.NewMessage(pattern='/status'))
async def status_handler(event):
    """查看下载状态"""
    if event.sender_id != ADMIN_ID:
        return
    
    if stats['start_time']:
        runtime = datetime.now() - stats['start_time']
        status_msg = f"""
📊 下载状态报告
⏰ 运行时间: {runtime}
✅ 成功下载: {stats['total_downloaded']}
❌ 失败数量: {stats['total_failed']}
🔄 活跃任务: {len(download_tasks)}
        """
    else:
        status_msg = "📊 暂无下载任务运行"
    
    await event.reply(status_msg)

# ==================== 主函数 ====================
async def main():
    """主函数"""
    global client, bot_client
    
    # 检查配置
    if API_ID == 'your_api_id' or API_HASH == 'your_api_hash':
        logger.error("❌ 请先配置 API_ID 和 API_HASH")
        return
    
    # 创建下载目录
    ensure_dir(DOWNLOAD_PATH)
    
    # 初始化客户端
    client = TelegramClient('downloader_session', API_ID, API_HASH)
    bot_client = TelegramClient('bot_session', API_ID, API_HASH)
    
    try:
        # 启动客户端
        await client.start()
        await bot_client.start(bot_token=BOT_TOKEN)
        
        logger.info("🤖 Telegram 下载器已启动")
        logger.info(f"📁 下载路径: {os.path.abspath(DOWNLOAD_PATH)}")
        logger.info(f"⚡ 最大并发: {MAX_CONCURRENT}")
        
        # 保持运行
        await bot_client.run_until_disconnected()
        
    except Exception as e:
        logger.error(f"❌ 启动失败: {e}")
    finally:
        if client:
            await client.disconnect()
        if bot_client:
            await bot_client.disconnect()

if __name__ == '__main__':
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("👋 程序已停止")
    except Exception as e:
        logger.error(f"❌ 程序异常: {e}")
