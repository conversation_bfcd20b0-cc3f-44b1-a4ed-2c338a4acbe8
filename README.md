# telegram_channel_downloader
Telegram 频道/群组 文件下载脚本

脚本需要python3环境，具体安装教程自行搜索。

测试环境  Ubuntu 18.04.5 LTS & Python 3.6.9

## 功能特性

- ✅ 异步并发下载，支持自定义并发数量
- ✅ 自动重试机制，处理网络异常
- ✅ 文件类型过滤，跳过贴纸、GIF等
- ✅ 文件大小限制，避免下载过大文件
- ✅ 远程Bot控制，支持多频道管理
- ✅ 下载进度监控和状态报告
- ✅ Google Drive自动上传 (可选)
- ✅ 新消息实时监控 (可选)
- ✅ 详细日志记录和错误处理

## 1. 前提

- 从 https://my.telegram.org/apps 获取自己的Telegram API密钥

- 下载脚本
```bash
git clone https://github.com/snow922841/telegram_channel_downloader.git
```

## 2. 安装

- 进入脚本目录
```bash
cd telegram_channel_downloader
```

- 安装依赖 
```bash
pip3 install -r requirements.txt
```

## 3. 配置

- 修改 `telegram_channel_downloader.py` 文件内的配置项：

### 必填配置
```python
API_ID = 'your_api_id'          # 从 https://my.telegram.org/apps 获取
API_HASH = 'your_api_hash'      # 从 https://my.telegram.org/apps 获取
BOT_TOKEN = 'your_bot_token'    # 从 @BotFather 获取
ADMIN_ID = 123456789            # 你的Telegram用户ID
CHAT_ID = 'your_channel_username'  # 频道用户名或ID
```

### 下载配置
```python
DOWNLOAD_PATH = './downloads'    # 下载保存路径
MAX_CONCURRENT = 10             # 最大并发下载数
RETRY_TIMES = 3                 # 重试次数
RETRY_DELAY = 5                 # 重试延迟(秒)
```

### 文件过滤配置
```python
SKIP_STICKERS = True            # 跳过贴纸
SKIP_GIFS = True               # 跳过GIF
SKIP_ANIMATIONS = True         # 跳过动态贴纸
MAX_FILE_SIZE = 2 * 1024 * 1024 * 1024  # 最大文件大小 2GB
```

### Google Drive 上传配置 (可选)
```python
ENABLE_GDRIVE_UPLOAD = False    # 是否启用GD上传
GCLONE_CONFIG_PATH = './gclone_config.json'  # gclone配置文件路径
GDRIVE_FOLDER_ID = 'your_gdrive_folder_id'   # GD文件夹ID
```

## 4. 使用

- 运行脚本
```bash
python3 telegram_channel_downloader.py
```

- 按照提示输入telegram绑定的手机号获取验证码并输入

- 配置完成后需要给bot发送命令来控制下载：

### Bot 命令

- `/start <频道链接> <起始消息ID>` - 开始下载任务
  ```
  /start @channel_name 0
  /start https://t.me/channel_name 100
  ```

- `/status` - 查看当前下载状态和统计信息

### 使用示例

1. 启动脚本后，向你的bot发送：
   ```
   /start @your_channel 0
   ```
   这将从消息ID 0开始下载频道中的所有文件

2. 查看下载状态：
   ```
   /status
   ```

## 5. 测试配置

在运行主程序前，建议先测试配置：

```bash
python3 test_config.py
```

这将检查：
- Python版本兼容性
- 依赖包安装情况
- 配置文件正确性
- Telegram连接状态
- 文件权限

## 6. Google Drive 上传 (可选)

如果需要自动上传到Google Drive，需要：

### 6.1 安装 gclone

**Linux/Mac:**
```bash
# 下载 gclone
wget https://github.com/donwa/gclone/releases/download/v1.60.1/gclone_v1.60.1_linux_amd64.zip
unzip gclone_v1.60.1_linux_amd64.zip
sudo mv gclone /usr/local/bin/
```

**Windows:**
1. 从 [gclone releases](https://github.com/donwa/gclone/releases) 下载Windows版本
2. 解压到系统PATH目录或项目目录

### 6.2 配置 gclone

1. 创建Google Drive应用凭据
2. 配置gclone认证：
```bash
gclone config
```

### 6.3 启用上传功能

在 `config.py` 中设置：
```python
ENABLE_GDRIVE_UPLOAD = True
GDRIVE_FOLDER_ID = 'your_folder_id'  # 可选，指定上传文件夹
DELETE_AFTER_UPLOAD = False  # 上传后是否删除本地文件
```

## 7. 高级功能

### 7.1 新消息监控
启用后可以实时监控频道新消息并自动下载：
```python
ENABLE_MONITORING = True
MONITORED_CHATS = ['@channel1', '@channel2', '-1001234567890']
MONITORING_INTERVAL = 60  # 检查间隔(秒)
```

### 7.2 数据库记录
自动记录下载历史，避免重复下载：
```python
USE_DATABASE = True
DATABASE_PATH = './download_history.db'
```

### 7.3 自定义过滤器
支持自定义文件过滤逻辑：
```python
def custom_file_filter(message, filename, file_size):
    # 只下载大于10MB的文件
    if file_size < 10 * 1024 * 1024:
        return False

    # 只下载特定格式
    allowed_extensions = ['.mp4', '.mkv', '.avi']
    if not any(filename.lower().endswith(ext) for ext in allowed_extensions):
        return False

    return True
```

### 7.4 自定义文件名和路径
```python
def custom_filename_format(message, original_filename):
    # 添加日期前缀
    date_prefix = message.date.strftime('%Y%m%d_')
    return f"{date_prefix}{original_filename}"

def custom_download_path(message, filename):
    # 按文件类型分类存储
    import os
    from pathlib import Path

    ext = Path(filename).suffix.lower()
    if ext in ['.mp4', '.avi', '.mkv']:
        folder = 'videos'
    elif ext in ['.jpg', '.png', '.gif']:
        folder = 'images'
    else:
        folder = 'others'

    return os.path.join(DOWNLOAD_PATH, folder, filename)
```

### 7.5 批量频道管理
支持通过Bot命令管理多个频道的下载任务：
- `/start @channel1 0` - 下载频道1
- `/start @channel2 100 1000` - 下载频道2的100-1100条消息
- `/retry @channel1` - 重试频道1的失败下载

## 8. 故障排除

### 8.1 常见问题

#### 配置相关
1. **API密钥错误**:
   - 检查API_ID和API_HASH是否正确
   - 确保从 https://my.telegram.org/apps 获取

2. **Bot Token无效**:
   - 检查BOT_TOKEN格式是否正确
   - 确保Bot未被禁用

3. **权限不足**:
   - 确保ADMIN_ID配置正确
   - 检查Bot是否有发送消息权限

#### 下载相关
4. **API限流**:
   - 脚本会自动处理Telegram的API限流，等待后重试
   - 降低MAX_CONCURRENT值可减少限流

5. **下载失败**:
   - 失败的消息ID会记录到数据库中
   - 使用 `/retry` 命令重试失败下载
   - 检查网络连接和磁盘空间

6. **文件已存在**:
   - 脚本会自动跳过已下载的文件
   - 使用数据库记录避免重复下载

7. **权限问题**:
   - 确保对下载目录有写入权限
   - Linux/Mac: `chmod 755 downloads/`

#### 性能相关
8. **内存占用过高**:
   - 降低MAX_CONCURRENT值
   - 定期重启程序

9. **下载速度慢**:
   - 检查网络连接
   - 适当增加MAX_CONCURRENT值
   - 使用代理可能影响速度

### 8.2 日志文件
- `telegram_downloader.log` - 详细的运行日志
- 控制台输出 - 实时状态信息
- 数据库文件 - 下载历史记录

### 8.3 调试模式
修改日志级别获取更多信息：
```python
LOG_LEVEL = 'DEBUG'  # 在config.py中设置
```

### 8.4 重置和清理
如果遇到严重问题，可以：
1. 删除会话文件: `*.session*`
2. 清空数据库: 删除 `download_history.db`
3. 重新认证: 重新运行程序并输入验证码

## 8. 更新日志

### 2024版本更新
- 重构代码架构，提升稳定性
- 优化异步并发处理
- 增强错误处理和重试机制
- 改进Bot控制界面
- 添加详细的状态监控
- 优化文件命名和路径处理

### 历史更新
- 2020-09-15: 移除redis依赖，增加异步并发
- 2020-09-03: 添加超时重试机制
- 2020-09-01: 使用Bot控制，脚本持久化
- 2020-08-29: 更换telethon库，添加GD上传
- 2020-08-19: 添加Google Drive上传功能

## 9. 许可证

MIT License

## 10. 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 11. 免责声明

本工具仅供学习和研究使用，请遵守相关法律法规和Telegram服务条款。使用者需要对自己的行为负责。
