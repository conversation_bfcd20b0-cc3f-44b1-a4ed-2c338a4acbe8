# telegram_channel_downloader
Telegram 频道/群组 文件下载脚本

脚本需要python3环境，具体安装教程自行搜索。

测试环境  Ubuntu 18.04.5 LTS & Python 3.6.9

## 功能特性

- ✅ 异步并发下载，支持自定义并发数量
- ✅ 自动重试机制，处理网络异常
- ✅ 文件类型过滤，跳过贴纸、GIF等
- ✅ 文件大小限制，避免下载过大文件
- ✅ 远程Bot控制，支持多频道管理
- ✅ 下载进度监控和状态报告
- ✅ Google Drive自动上传 (可选)
- ✅ 新消息实时监控 (可选)
- ✅ 详细日志记录和错误处理

## 1. 前提

- 从 https://my.telegram.org/apps 获取自己的Telegram API密钥

- 下载脚本
```bash
git clone https://github.com/snow922841/telegram_channel_downloader.git
```

## 2. 安装

- 进入脚本目录
```bash
cd telegram_channel_downloader
```

- 安装依赖 
```bash
pip3 install -r requirements.txt
```

## 3. 配置

- 修改 `telegram_channel_downloader.py` 文件内的配置项：

### 必填配置
```python
API_ID = 'your_api_id'          # 从 https://my.telegram.org/apps 获取
API_HASH = 'your_api_hash'      # 从 https://my.telegram.org/apps 获取
BOT_TOKEN = 'your_bot_token'    # 从 @BotFather 获取
ADMIN_ID = 123456789            # 你的Telegram用户ID
CHAT_ID = 'your_channel_username'  # 频道用户名或ID
```

### 下载配置
```python
DOWNLOAD_PATH = './downloads'    # 下载保存路径
MAX_CONCURRENT = 10             # 最大并发下载数
RETRY_TIMES = 3                 # 重试次数
RETRY_DELAY = 5                 # 重试延迟(秒)
```

### 文件过滤配置
```python
SKIP_STICKERS = True            # 跳过贴纸
SKIP_GIFS = True               # 跳过GIF
SKIP_ANIMATIONS = True         # 跳过动态贴纸
MAX_FILE_SIZE = 2 * 1024 * 1024 * 1024  # 最大文件大小 2GB
```

### Google Drive 上传配置 (可选)
```python
ENABLE_GDRIVE_UPLOAD = False    # 是否启用GD上传
GCLONE_CONFIG_PATH = './gclone_config.json'  # gclone配置文件路径
GDRIVE_FOLDER_ID = 'your_gdrive_folder_id'   # GD文件夹ID
```

## 4. 使用

- 运行脚本
```bash
python3 telegram_channel_downloader.py
```

- 按照提示输入telegram绑定的手机号获取验证码并输入

- 配置完成后需要给bot发送命令来控制下载：

### Bot 命令

- `/start <频道链接> <起始消息ID>` - 开始下载任务
  ```
  /start @channel_name 0
  /start https://t.me/channel_name 100
  ```

- `/status` - 查看当前下载状态和统计信息

### 使用示例

1. 启动脚本后，向你的bot发送：
   ```
   /start @your_channel 0
   ```
   这将从消息ID 0开始下载频道中的所有文件

2. 查看下载状态：
   ```
   /status
   ```

## 5. Google Drive 上传 (可选)

如果需要自动上传到Google Drive，需要：

1. 安装 gclone
```bash
# 下载 gclone
wget https://github.com/donwa/gclone/releases/download/v1.60.1/gclone_v1.60.1_linux_amd64.zip
unzip gclone_v1.60.1_linux_amd64.zip
sudo mv gclone /usr/local/bin/
```

2. 配置 gclone 认证文件

3. 在脚本中启用GD上传功能：
```python
ENABLE_GDRIVE_UPLOAD = True
GDRIVE_FOLDER_ID = 'your_folder_id'
```

## 6. 高级功能

### 新消息监控
启用后可以实时监控频道新消息并自动下载：
```python
ENABLE_MONITORING = True
MONITORED_CHATS = ['@channel1', '@channel2']
```

### 批量频道管理
支持通过Bot命令管理多个频道的下载任务

## 7. 故障排除

### 常见问题

1. **API限流**: 脚本会自动处理Telegram的API限流，等待后重试

2. **下载失败**: 失败的消息ID会记录到日志中，可以手动重试

3. **文件已存在**: 脚本会自动跳过已下载的文件

4. **权限问题**: 确保对下载目录有写入权限

### 日志文件
- `telegram_downloader.log` - 详细的运行日志
- 控制台输出 - 实时状态信息

## 8. 更新日志

### 2024版本更新
- 重构代码架构，提升稳定性
- 优化异步并发处理
- 增强错误处理和重试机制
- 改进Bot控制界面
- 添加详细的状态监控
- 优化文件命名和路径处理

### 历史更新
- 2020-09-15: 移除redis依赖，增加异步并发
- 2020-09-03: 添加超时重试机制
- 2020-09-01: 使用Bot控制，脚本持久化
- 2020-08-29: 更换telethon库，添加GD上传
- 2020-08-19: 添加Google Drive上传功能

## 9. 许可证

MIT License

## 10. 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 11. 免责声明

本工具仅供学习和研究使用，请遵守相关法律法规和Telegram服务条款。使用者需要对自己的行为负责。
