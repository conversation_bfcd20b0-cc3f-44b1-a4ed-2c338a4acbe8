#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置测试脚本
用于验证配置是否正确
"""

import os
import sys
import asyncio
from pathlib import Path

def test_python_version():
    """测试Python版本"""
    print("🐍 Python版本检查...")
    version = sys.version_info
    if version.major >= 3 and version.minor >= 7:
        print(f"✅ Python版本: {version.major}.{version.minor}.{version.micro}")
        return True
    else:
        print(f"❌ Python版本过低: {version.major}.{version.minor}.{version.micro}")
        print("💡 需要Python 3.7或更高版本")
        return False

def test_dependencies():
    """测试依赖包"""
    print("\n📦 依赖包检查...")
    required_packages = [
        'telethon',
        'aiofiles',
        'aiohttp'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package} - 未安装")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n💡 请安装缺失的包: pip install {' '.join(missing_packages)}")
        return False
    
    return True

def test_config():
    """测试配置文件"""
    print("\n⚙️ 配置文件检查...")
    
    if not os.path.exists('config.py'):
        print("❌ 未找到config.py文件")
        print("💡 请复制config_example.py为config.py并修改配置")
        return False
    
    try:
        from config import API_ID, API_HASH, BOT_TOKEN, ADMIN_ID, DOWNLOAD_PATH
        
        # 检查必填配置
        if API_ID == 'your_api_id' or not API_ID:
            print("❌ API_ID 未配置")
            return False
        else:
            print("✅ API_ID 已配置")
        
        if API_HASH == 'your_api_hash' or not API_HASH:
            print("❌ API_HASH 未配置")
            return False
        else:
            print("✅ API_HASH 已配置")
        
        if BOT_TOKEN == 'your_bot_token' or not BOT_TOKEN:
            print("❌ BOT_TOKEN 未配置")
            return False
        else:
            print("✅ BOT_TOKEN 已配置")
        
        if ADMIN_ID == 123456789:
            print("❌ ADMIN_ID 未配置")
            return False
        else:
            print(f"✅ ADMIN_ID: {ADMIN_ID}")
        
        # 检查下载路径
        if not os.path.exists(DOWNLOAD_PATH):
            try:
                Path(DOWNLOAD_PATH).mkdir(parents=True, exist_ok=True)
                print(f"✅ 下载路径已创建: {DOWNLOAD_PATH}")
            except Exception as e:
                print(f"❌ 无法创建下载路径: {e}")
                return False
        else:
            print(f"✅ 下载路径存在: {DOWNLOAD_PATH}")
        
        return True
        
    except ImportError as e:
        print(f"❌ 配置文件导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 配置检查失败: {e}")
        return False

async def test_telegram_connection():
    """测试Telegram连接"""
    print("\n📡 Telegram连接测试...")
    
    try:
        from config import API_ID, API_HASH
        from telethon import TelegramClient
        
        client = TelegramClient('test_session', API_ID, API_HASH)
        
        print("🔗 正在连接Telegram...")
        await client.connect()
        
        if await client.is_user_authorized():
            me = await client.get_me()
            print(f"✅ 已授权用户: {me.first_name}")
        else:
            print("⚠️ 用户未授权，首次运行需要验证")
        
        await client.disconnect()
        
        # 清理测试会话文件
        for file in ['test_session.session', 'test_session.session-journal']:
            if os.path.exists(file):
                os.remove(file)
        
        return True
        
    except Exception as e:
        print(f"❌ Telegram连接失败: {e}")
        return False

def test_file_permissions():
    """测试文件权限"""
    print("\n📁 文件权限检查...")
    
    try:
        from config import DOWNLOAD_PATH
        
        # 测试写入权限
        test_file = os.path.join(DOWNLOAD_PATH, 'test_write.tmp')
        with open(test_file, 'w') as f:
            f.write('test')
        
        # 测试读取权限
        with open(test_file, 'r') as f:
            content = f.read()
        
        # 清理测试文件
        os.remove(test_file)
        
        print("✅ 文件读写权限正常")
        return True
        
    except Exception as e:
        print(f"❌ 文件权限测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 Telegram频道下载器 - 配置测试")
    print("=" * 50)
    
    tests = [
        ("Python版本", test_python_version),
        ("依赖包", test_dependencies),
        ("配置文件", test_config),
        ("文件权限", test_file_permissions),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            results.append((test_name, False))
    
    # 异步测试
    try:
        result = asyncio.run(test_telegram_connection())
        results.append(("Telegram连接", result))
    except Exception as e:
        print(f"❌ Telegram连接测试异常: {e}")
        results.append(("Telegram连接", False))
    
    # 显示测试结果
    print("\n" + "=" * 50)
    print("📋 测试结果汇总:")
    
    all_passed = True
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if not result:
            all_passed = False
    
    print("\n" + "=" * 50)
    if all_passed:
        print("🎉 所有测试通过！可以运行主程序了")
        print("💡 运行命令: python telegram_channel_downloader.py")
    else:
        print("⚠️ 部分测试失败，请检查配置后重试")
        print("💡 参考README.md文档进行配置")

if __name__ == '__main__':
    main()
