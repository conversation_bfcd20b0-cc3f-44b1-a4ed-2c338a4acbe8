/* OSI 3D 生物仿生艺术模拟器 - 全新布局系统 */

/* === 基础重置 === */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* === CSS变量定义 === */
:root {
    /* 颜色系统 */
    --primary-bg: #0a0b1e;
    --secondary-bg: #1a1b2e;
    --accent-color: #4facfe;
    --text-primary: #ffffff;
    --text-secondary: #cbd5e1;
    --text-muted: #94a3b8;
    --border-color: rgba(255, 255, 255, 0.1);
    --glass-bg: rgba(255, 255, 255, 0.05);
    
    /* 间距系统 */
    --space-xs: 0.25rem;
    --space-sm: 0.5rem;
    --space-md: 1rem;
    --space-lg: 1.5rem;
    --space-xl: 2rem;
    
    /* 字体系统 */
    --font-xs: 0.75rem;
    --font-sm: 0.875rem;
    --font-base: 1rem;
    --font-lg: 1.125rem;
    --font-xl: 1.25rem;
    
    /* 布局尺寸 */
    --header-height: 60px;
    --control-height: 80px;
    --sidebar-width: 320px;
    --footer-height: 50px;
    
    /* 阴影系统 */
    --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.12);
    --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.15);
    --shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.2);
    
    /* 动画 */
    --transition-fast: 0.15s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;
}

/* === 基础样式 === */
html {
    height: 100%;
    font-size: 16px;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    background: linear-gradient(135deg, var(--primary-bg) 0%, var(--secondary-bg) 100%);
    color: var(--text-primary);
    height: 100vh;
    overflow: hidden;
    line-height: 1.5;
}

/* === 主布局容器 === */
.app-container {
    display: grid;
    grid-template-areas: 
        "header header"
        "controls controls"
        "main sidebar"
        "footer footer";
    grid-template-rows: var(--header-height) var(--control-height) 1fr var(--footer-height);
    grid-template-columns: 1fr var(--sidebar-width);
    height: 100vh;
    gap: 0;
}

/* === 顶部导航 === */
.top-navigation {
    grid-area: header;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 var(--space-lg);
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--border-color);
    z-index: 100;
}

.nav-brand {
    display: flex;
    align-items: center;
    gap: var(--space-md);
}

.brand-icon {
    font-size: var(--font-xl);
    color: var(--accent-color);
}

.brand-text {
    display: flex;
    flex-direction: column;
}

.brand-name {
    font-weight: 600;
    font-size: var(--font-lg);
}

.brand-tagline {
    font-size: var(--font-xs);
    color: var(--text-muted);
}

.nav-actions {
    display: flex;
    gap: var(--space-sm);
}

.nav-btn {
    width: 40px;
    height: 40px;
    border: none;
    background: transparent;
    color: var(--text-secondary);
    border-radius: 8px;
    cursor: pointer;
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
}

.nav-btn:hover {
    background: var(--glass-bg);
    color: var(--text-primary);
    transform: scale(1.05);
}

/* === 控制面板 === */
.control-panel {
    grid-area: controls;
    display: flex;
    align-items: center;
    gap: var(--space-lg);
    padding: 0 var(--space-lg);
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--border-color);
    overflow-x: auto;
}

.panel-section {
    display: flex;
    align-items: center;
    gap: var(--space-md);
    flex-shrink: 0;
}

.section-title {
    font-size: var(--font-sm);
    color: var(--text-muted);
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: var(--space-xs);
    white-space: nowrap;
}

.view-controls,
.simulation-controls {
    display: flex;
    gap: var(--space-sm);
}

.view-btn,
.sim-btn {
    padding: var(--space-sm) var(--space-md);
    border: 1px solid var(--border-color);
    background: transparent;
    color: var(--text-secondary);
    border-radius: 6px;
    cursor: pointer;
    transition: all var(--transition-fast);
    font-size: var(--font-sm);
    white-space: nowrap;
    display: flex;
    align-items: center;
    gap: var(--space-xs);
}

.view-btn:hover,
.sim-btn:hover {
    background: var(--glass-bg);
    color: var(--text-primary);
    border-color: var(--accent-color);
}

.view-btn.active {
    background: var(--accent-color);
    color: white;
    border-color: var(--accent-color);
}

/* === 主工作区 === */
.main-workspace {
    grid-area: main;
    position: relative;
    overflow: hidden;
    background: var(--primary-bg);
}

.scene-container {
    width: 100%;
    height: 100%;
    position: relative;
}

#three-canvas {
    width: 100%;
    height: 100%;
    display: block;
}

/* === 侧边栏 === */
.info-panel {
    grid-area: sidebar;
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    border-left: 1px solid var(--border-color);
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.panel-header {
    padding: var(--space-md);
    border-bottom: 1px solid var(--border-color);
    flex-shrink: 0;
}

.panel-tabs {
    display: flex;
    gap: var(--space-xs);
}

.tab-btn {
    padding: var(--space-sm) var(--space-md);
    border: none;
    background: transparent;
    color: var(--text-muted);
    border-radius: 6px;
    cursor: pointer;
    transition: all var(--transition-fast);
    font-size: var(--font-sm);
    display: flex;
    align-items: center;
    gap: var(--space-xs);
}

.tab-btn:hover {
    background: var(--glass-bg);
    color: var(--text-secondary);
}

.tab-btn.active {
    background: var(--accent-color);
    color: white;
}

.panel-content {
    flex: 1;
    padding: var(--space-md);
    overflow-y: auto;
}

/* === 底部状态栏 === */
.status-bar {
    grid-area: footer;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 var(--space-lg);
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    border-top: 1px solid var(--border-color);
    font-size: var(--font-sm);
}

.status-group {
    display: flex;
    gap: var(--space-lg);
}

.status-item {
    display: flex;
    align-items: center;
    gap: var(--space-xs);
    color: var(--text-muted);
}

/* === 响应式设计 === */

/* 大屏幕优化 (1200px+) */
@media (min-width: 1200px) {
    :root {
        --sidebar-width: 380px;
        --space-lg: 2rem;
        --space-xl: 2.5rem;
    }

    .control-panel {
        padding: 0 var(--space-xl);
    }

    .panel-section {
        gap: var(--space-lg);
    }
}

/* 中等屏幕 (1024px - 1199px) */
@media (max-width: 1199px) and (min-width: 1024px) {
    :root {
        --sidebar-width: 320px;
    }

    .control-panel {
        gap: var(--space-md);
        padding: 0 var(--space-lg);
    }

    .panel-section {
        gap: var(--space-sm);
    }

    .view-btn,
    .sim-btn {
        padding: var(--space-xs) var(--space-sm);
        font-size: var(--font-xs);
    }
}

/* 平板设备 (768px - 1023px) */
@media (max-width: 1023px) and (min-width: 768px) {
    :root {
        --sidebar-width: 280px;
        --control-height: 100px;
    }

    .control-panel {
        flex-wrap: wrap;
        height: auto;
        min-height: var(--control-height);
        gap: var(--space-sm);
        padding: var(--space-sm) var(--space-md);
    }

    .panel-section {
        flex: 1 1 auto;
        min-width: 200px;
        gap: var(--space-sm);
    }

    .section-title {
        font-size: var(--font-xs);
    }

    .view-controls,
    .simulation-controls,
    .interaction-modes {
        flex-wrap: wrap;
        gap: var(--space-xs);
    }
}

/* 移动设备 (最大767px) */
@media (max-width: 767px) {
    :root {
        --header-height: 50px;
        --control-height: auto;
        --footer-height: 40px;
    }

    .app-container {
        grid-template-areas:
            "header"
            "controls"
            "main"
            "sidebar"
            "footer";
        grid-template-columns: 1fr;
        grid-template-rows: var(--header-height) auto 1fr 180px var(--footer-height);
    }

    .top-navigation {
        padding: 0 var(--space-md);
    }

    .nav-brand .brand-tagline {
        display: none;
    }

    .nav-actions {
        gap: var(--space-xs);
    }

    .nav-btn {
        width: 36px;
        height: 36px;
    }

    .control-panel {
        flex-direction: column;
        gap: var(--space-md);
        padding: var(--space-md);
        align-items: stretch;
    }

    .panel-section {
        text-align: center;
        gap: var(--space-sm);
    }

    .view-controls,
    .simulation-controls {
        justify-content: center;
        flex-wrap: wrap;
        gap: var(--space-xs);
    }

    .interaction-modes {
        justify-content: center;
        gap: var(--space-sm);
    }

    .info-panel {
        border-left: none;
        border-top: 1px solid var(--border-color);
        max-height: 180px;
    }

    .panel-content {
        padding: var(--space-sm);
    }

    .status-bar {
        padding: 0 var(--space-sm);
        font-size: var(--font-xs);
    }

    .status-group {
        gap: var(--space-sm);
    }

    .status-item {
        font-size: var(--font-xs);
    }
}

/* 小屏手机 (最大479px) */
@media (max-width: 479px) {
    :root {
        --space-sm: 0.375rem;
        --space-md: 0.5rem;
        --space-lg: 0.75rem;
        --header-height: 45px;
        --footer-height: 35px;
    }

    .app-container {
        grid-template-rows: var(--header-height) auto 1fr 150px var(--footer-height);
    }

    .nav-brand .brand-name {
        font-size: var(--font-base);
    }

    .nav-btn {
        width: 32px;
        height: 32px;
    }

    .control-panel {
        padding: var(--space-sm);
        gap: var(--space-sm);
    }

    .section-title {
        font-size: var(--font-xs);
        margin-bottom: var(--space-xs);
    }

    .view-btn,
    .sim-btn {
        padding: var(--space-xs);
        font-size: var(--font-xs);
        min-width: 40px;
    }

    .view-btn span,
    .sim-btn span {
        display: none;
    }

    .interaction-mode {
        min-width: 50px;
        padding: var(--space-xs);
    }

    .interaction-mode span {
        font-size: 0.625rem;
    }

    .info-panel {
        max-height: 150px;
    }

    .panel-tabs {
        gap: var(--space-xs);
    }

    .tab-btn {
        padding: var(--space-xs) var(--space-sm);
        font-size: var(--font-xs);
    }

    .tab-btn span {
        display: none;
    }

    .status-bar {
        flex-direction: column;
        gap: var(--space-xs);
        padding: var(--space-xs) var(--space-sm);
        text-align: center;
    }

    .status-group {
        justify-content: center;
        gap: var(--space-sm);
    }
}

/* 超小屏幕 (最大359px) */
@media (max-width: 359px) {
    .control-panel {
        padding: var(--space-xs);
    }

    .panel-section {
        gap: var(--space-xs);
    }

    .view-controls,
    .simulation-controls {
        gap: 2px;
    }

    .view-btn,
    .sim-btn {
        min-width: 36px;
        padding: 4px;
    }

    .interaction-modes {
        gap: var(--space-xs);
    }

    .interaction-mode {
        min-width: 44px;
    }
}

/* 横屏模式优化 */
@media (orientation: landscape) and (max-height: 500px) {
    .app-container {
        grid-template-rows: 40px auto 1fr 120px 30px;
    }

    .control-panel {
        padding: var(--space-xs) var(--space-sm);
        min-height: 60px;
    }

    .info-panel {
        max-height: 120px;
    }

    .panel-content {
        padding: var(--space-xs);
    }
}

/* 高分辨率屏幕优化 */
@media (min-resolution: 2dppx) {
    .layer-label,
    .indicator,
    .nav-btn,
    .view-btn,
    .sim-btn {
        border-width: 0.5px;
    }
}

/* === 场景覆盖层和标签 === */
.scene-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 10;
}

.layer-labels {
    position: relative;
    width: 100%;
    height: 100%;
}

.layer-label {
    position: absolute;
    pointer-events: auto;
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: var(--space-sm);
    min-width: 120px;
    transition: all var(--transition-normal);
    cursor: pointer;
}

.layer-label:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: var(--accent-color);
    transform: scale(1.05);
}

.label-content {
    display: flex;
    flex-direction: column;
    gap: var(--space-xs);
}

.label-header {
    display: flex;
    align-items: center;
    gap: var(--space-xs);
}

.label-number {
    width: 24px;
    height: 24px;
    background: var(--accent-color);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-sm);
    font-weight: 600;
}

.label-info {
    display: flex;
    flex-direction: column;
}

.label-name {
    font-size: var(--font-sm);
    font-weight: 500;
    color: var(--text-primary);
}

.label-english {
    font-size: var(--font-xs);
    color: var(--text-muted);
}

.label-description {
    font-size: var(--font-xs);
    color: var(--text-secondary);
    margin-top: var(--space-xs);
}

/* === 性能指标 === */
.performance-indicators {
    position: absolute;
    top: var(--space-md);
    right: var(--space-md);
    display: flex;
    flex-direction: column;
    gap: var(--space-sm);
    z-index: 20;
    pointer-events: none;
}

.indicator {
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    padding: var(--space-sm);
    display: flex;
    align-items: center;
    gap: var(--space-xs);
    font-size: var(--font-xs);
    color: var(--text-secondary);
}

.indicator-value {
    color: var(--accent-color);
    font-weight: 600;
}

/* === 交互模式 === */
.interaction-modes {
    display: flex;
    gap: var(--space-sm);
}

.interaction-mode {
    padding: var(--space-sm);
    border: 1px solid var(--border-color);
    background: transparent;
    border-radius: 6px;
    cursor: pointer;
    transition: all var(--transition-fast);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--space-xs);
    min-width: 60px;
}

.interaction-mode:hover {
    background: var(--glass-bg);
    border-color: var(--accent-color);
}

.interaction-mode.active {
    background: var(--accent-color);
    color: white;
    border-color: var(--accent-color);
}

.mode-icon {
    font-size: var(--font-lg);
}

.interaction-mode span {
    font-size: var(--font-xs);
}

/* === 启动画面 === */
.startup-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--primary-bg);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    transition: opacity var(--transition-slow);
}

.startup-screen.hidden {
    opacity: 0;
    pointer-events: none;
}

.startup-content {
    text-align: center;
    max-width: 400px;
}

.startup-logo {
    margin-bottom: var(--space-xl);
}

.logo-symbol {
    font-size: 4rem;
    margin-bottom: var(--space-md);
    color: var(--accent-color);
}

.startup-title {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: var(--space-sm);
    background: linear-gradient(45deg, var(--accent-color), #ff6b9d);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.startup-subtitle {
    color: var(--text-muted);
    margin-bottom: var(--space-xl);
}

.progress-bar {
    width: 100%;
    height: 4px;
    background: var(--secondary-bg);
    border-radius: 2px;
    overflow: hidden;
    margin-bottom: var(--space-md);
}

.progress-fill {
    height: 100%;
    background: var(--accent-color);
    width: 0%;
    transition: width var(--transition-normal);
}

.progress-text {
    font-size: var(--font-sm);
    color: var(--text-muted);
}

/* === 模态框 === */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    opacity: 0;
    pointer-events: none;
    transition: opacity var(--transition-normal);
}

.modal-overlay.show {
    opacity: 1;
    pointer-events: auto;
}

.modal-container {
    background: var(--secondary-bg);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow: hidden;
    transform: scale(0.9);
    transition: transform var(--transition-normal);
}

.modal-overlay.show .modal-container {
    transform: scale(1);
}

.modal-header {
    padding: var(--space-lg);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.modal-body {
    padding: var(--space-lg);
    overflow-y: auto;
    max-height: 60vh;
}

.modal-footer {
    padding: var(--space-lg);
    border-top: 1px solid var(--border-color);
    display: flex;
    gap: var(--space-md);
    justify-content: flex-end;
}

.modal-btn {
    padding: var(--space-sm) var(--space-lg);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    cursor: pointer;
    transition: all var(--transition-fast);
    font-size: var(--font-sm);
}

.modal-btn.primary {
    background: var(--accent-color);
    color: white;
    border-color: var(--accent-color);
}

.modal-btn.secondary {
    background: transparent;
    color: var(--text-secondary);
}

.modal-btn:hover {
    opacity: 0.8;
}

/* === 层级详情样式 === */
.layer-detail-card {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: var(--space-lg);
    margin-bottom: var(--space-md);
}

.layer-header {
    display: flex;
    align-items: center;
    gap: var(--space-md);
    margin-bottom: var(--space-md);
}

.layer-number {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: var(--font-lg);
}

.layer-title h4 {
    margin: 0;
    font-size: var(--font-lg);
    color: var(--text-primary);
}

.layer-title p {
    margin: 0;
    font-size: var(--font-sm);
    color: var(--text-muted);
}

.layer-description {
    margin-bottom: var(--space-md);
    line-height: 1.6;
}

.layer-protocols,
.layer-devices {
    margin-bottom: var(--space-md);
}

.layer-protocols h5,
.layer-devices h5 {
    margin-bottom: var(--space-sm);
    color: var(--text-secondary);
    font-size: var(--font-sm);
}

.protocol-tags {
    display: flex;
    flex-wrap: wrap;
    gap: var(--space-xs);
}

.protocol-tag {
    background: var(--accent-color);
    color: white;
    padding: var(--space-xs) var(--space-sm);
    border-radius: 4px;
    font-size: var(--font-xs);
    font-weight: 500;
}

.layer-devices ul {
    list-style: none;
    padding: 0;
}

.layer-devices li {
    padding: var(--space-xs) 0;
    color: var(--text-secondary);
    font-size: var(--font-sm);
}

.layer-devices li::before {
    content: "•";
    color: var(--accent-color);
    margin-right: var(--space-sm);
}

/* === 层级摘要样式 === */
.layer-summary {
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    padding: var(--space-md);
    margin-bottom: var(--space-sm);
    transition: all var(--transition-fast);
    cursor: pointer;
}

.layer-summary:hover {
    background: rgba(255, 255, 255, 0.08);
    border-color: var(--accent-color);
    transform: translateX(4px);
}

.layer-summary-header {
    display: flex;
    align-items: center;
    gap: var(--space-md);
    margin-bottom: var(--space-sm);
}

.layer-summary-info {
    flex: 1;
}

.layer-summary-info h4 {
    margin: 0;
    font-size: var(--font-base);
    color: var(--text-primary);
}

.layer-summary-info p {
    margin: 0;
    font-size: var(--font-xs);
    color: var(--text-muted);
}

.focus-btn {
    width: 32px;
    height: 32px;
    border: 1px solid var(--border-color);
    background: transparent;
    color: var(--text-muted);
    border-radius: 4px;
    cursor: pointer;
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
}

.focus-btn:hover {
    background: var(--accent-color);
    color: white;
    border-color: var(--accent-color);
}

.layer-summary-desc {
    font-size: var(--font-sm);
    color: var(--text-secondary);
    margin: 0;
    line-height: 1.5;
}

/* === 分析面板样式 === */
.analytics-content {
    display: flex;
    flex-direction: column;
    gap: var(--space-lg);
}

.analytics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: var(--space-md);
}

.analytics-card {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: var(--space-md);
    text-align: center;
    transition: all var(--transition-fast);
}

.analytics-card:hover {
    background: rgba(255, 255, 255, 0.08);
    border-color: var(--accent-color);
}

.analytics-card h4 {
    margin: 0 0 var(--space-sm) 0;
    font-size: var(--font-sm);
    color: var(--text-secondary);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-xs);
}

.metric-value {
    font-size: var(--font-xl);
    font-weight: 600;
    color: var(--accent-color);
    margin-bottom: var(--space-xs);
}

.metric-trend {
    font-size: var(--font-xs);
    color: var(--text-muted);
}

.analytics-chart {
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: var(--space-lg);
}

.analytics-chart h4 {
    margin: 0 0 var(--space-md) 0;
    color: var(--text-primary);
}

.chart-placeholder {
    height: 200px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.02);
    border-radius: 4px;
    color: var(--text-muted);
}

.chart-placeholder small {
    margin-top: var(--space-md);
    font-size: var(--font-xs);
}

/* === 设置面板样式 === */
.settings-content {
    display: flex;
    flex-direction: column;
    gap: var(--space-lg);
}

.settings-section {
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: var(--space-md);
}

.settings-section h4 {
    margin: 0 0 var(--space-md) 0;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: var(--space-sm);
}

.setting-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--space-sm) 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.setting-item:last-child {
    border-bottom: none;
}

.setting-item label {
    color: var(--text-secondary);
    font-size: var(--font-sm);
}

.setting-item select,
.setting-item input[type="range"] {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    padding: var(--space-xs) var(--space-sm);
    color: var(--text-primary);
    font-size: var(--font-sm);
}

.setting-item input[type="checkbox"] {
    width: 18px;
    height: 18px;
    accent-color: var(--accent-color);
}

.settings-actions {
    display: flex;
    gap: var(--space-md);
    justify-content: flex-end;
    padding-top: var(--space-md);
    border-top: 1px solid var(--border-color);
}

/* === ASCII图表样式 === */
.ascii-chart {
    font-family: 'Courier New', monospace;
    background: rgba(255, 255, 255, 0.02);
    border-radius: 4px;
    padding: var(--space-md);
    height: 100%;
    display: flex;
    flex-direction: column;
}

.chart-title {
    font-size: var(--font-sm);
    color: var(--text-primary);
    margin-bottom: var(--space-md);
    text-align: center;
}

.chart-bars {
    display: flex;
    align-items: flex-end;
    gap: 2px;
    height: 120px;
    flex: 1;
}

.chart-bar {
    flex: 1;
    min-height: 2px;
    border-radius: 2px 2px 0 0;
    transition: all var(--transition-fast);
}

.chart-bar:hover {
    opacity: 0.8;
    transform: scaleY(1.1);
}

.chart-info {
    font-size: var(--font-xs);
    color: var(--text-muted);
    text-align: center;
    margin-top: var(--space-sm);
}

/* === 交互指南样式 === */
.interaction-guide {
    margin-top: var(--space-lg);
}

.interaction-guide h4 {
    margin-bottom: var(--space-md);
    color: var(--text-primary);
}

.guide-items {
    display: flex;
    flex-direction: column;
    gap: var(--space-sm);
}

.guide-item {
    display: flex;
    align-items: center;
    gap: var(--space-md);
    padding: var(--space-sm);
    background: rgba(255, 255, 255, 0.03);
    border-radius: 6px;
    transition: all var(--transition-fast);
}

.guide-item:hover {
    background: rgba(255, 255, 255, 0.08);
    transform: translateX(4px);
}

.guide-item i {
    color: var(--accent-color);
    font-size: var(--font-lg);
    width: 24px;
    text-align: center;
}

.guide-item > div {
    flex: 1;
}

.guide-item > div > div:first-child {
    font-size: var(--font-sm);
    color: var(--text-primary);
    font-weight: 500;
}

.guide-item > div > div:last-child {
    font-size: var(--font-xs);
    color: var(--text-muted);
}

/* === 通知样式 === */
@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOutRight {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

@keyframes fadeInOut {
    0%, 100% { opacity: 0; }
    10%, 90% { opacity: 1; }
}

.notification {
    box-shadow: var(--shadow-lg);
    backdrop-filter: blur(10px);
}

/* === 语音反馈样式 === */
.voice-feedback {
    animation: fadeInOut 2s ease-in-out;
}

/* === 层级摘要激活状态 === */
.layer-summary.active {
    background: rgba(79, 172, 254, 0.1);
    border-color: var(--accent-color);
    transform: translateX(8px);
}

.layer-summary.active .layer-number {
    box-shadow: 0 0 10px rgba(79, 172, 254, 0.5);
}

/* === 协议标签样式 === */
.layer-protocols {
    margin-top: var(--space-sm);
}

.layer-protocols small {
    color: var(--text-muted);
    font-size: var(--font-xs);
}

/* === 工具类 === */
.hidden {
    display: none !important;
}

.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}
