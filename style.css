/* OSI 3D 生物仿生艺术模拟器 - 现代CSS样式系统 */

/* === 现代CSS重置 === */
*,
*::before,
*::after {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* === CSS自定义属性（现代变量系统）=== */
:root {
    /* 颜色系统 - 使用oklch色彩空间 */
    --primary-bg: oklch(0.1 0.05 240);
    --secondary-bg: oklch(0.15 0.08 240);
    --accent-color: oklch(0.7 0.15 220);
    --accent-hover: oklch(0.75 0.18 220);
    --text-primary: oklch(1 0 0);
    --text-secondary: oklch(0.8 0.02 240);
    --text-muted: oklch(0.6 0.05 240);
    --border-color: oklch(1 0 0 / 0.1);
    --glass-bg: oklch(1 0 0 / 0.05);
    --success-color: oklch(0.7 0.15 140);
    --warning-color: oklch(0.7 0.15 60);
    --error-color: oklch(0.65 0.2 20);

    /* 间距系统 - 使用逻辑单位 */
    --space-3xs: 0.125rem;
    --space-2xs: 0.25rem;
    --space-xs: 0.5rem;
    --space-sm: 0.75rem;
    --space-md: 1rem;
    --space-lg: 1.5rem;
    --space-xl: 2rem;
    --space-2xl: 3rem;
    --space-3xl: 4rem;

    /* 字体系统 - 使用clamp()响应式字体 */
    --font-xs: clamp(0.7rem, 0.8vw, 0.75rem);
    --font-sm: clamp(0.8rem, 1vw, 0.875rem);
    --font-base: clamp(0.9rem, 1.2vw, 1rem);
    --font-lg: clamp(1rem, 1.4vw, 1.125rem);
    --font-xl: clamp(1.1rem, 1.6vw, 1.25rem);
    --font-2xl: clamp(1.3rem, 2vw, 1.5rem);
    --font-3xl: clamp(1.6rem, 2.5vw, 2rem);

    /* 布局尺寸 - 使用容器查询单位 */
    --header-height: 4rem;
    --control-height: 5rem;
    --sidebar-width: min(25vw, 22rem);
    --footer-height: 3.5rem;

    /* 现代动画系统 */
    --ease-out-expo: cubic-bezier(0.16, 1, 0.3, 1);
    --ease-in-out-back: cubic-bezier(0.68, -0.6, 0.32, 1.6);
    --ease-spring: cubic-bezier(0.175, 0.885, 0.32, 1.275);
    --transition-fast: 0.15s var(--ease-out-expo);
    --transition-normal: 0.3s var(--ease-out-expo);
    --transition-slow: 0.5s var(--ease-out-expo);
    --transition-spring: 0.4s var(--ease-spring);

    /* 阴影系统 */
    --shadow-sm: 0 1px 2px oklch(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px oklch(0 0 0 / 0.1), 0 2px 4px oklch(0 0 0 / 0.06);
    --shadow-lg: 0 10px 15px oklch(0 0 0 / 0.1), 0 4px 6px oklch(0 0 0 / 0.05);
    --shadow-xl: 0 20px 25px oklch(0 0 0 / 0.1), 0 8px 10px oklch(0 0 0 / 0.04);

    /* 边框半径系统 */
    --radius-sm: 0.25rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-full: 9999px;
}

/* === 现代基础样式 === */
html {
    block-size: 100%;
    font-size: clamp(14px, 1.2vw, 18px);
    color-scheme: dark;
    scroll-behavior: smooth;
}

body {
    font-family: 'Inter Variable', 'Inter', system-ui, -apple-system, sans-serif;
    font-optical-sizing: auto;
    font-variation-settings: 'wght' 400;
    background:
        radial-gradient(circle at 20% 80%, var(--accent-color) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, oklch(0.7 0.15 300) 0%, transparent 50%),
        linear-gradient(135deg, var(--primary-bg) 0%, var(--secondary-bg) 100%);
    color: var(--text-primary);
    block-size: 100vh;
    overflow: hidden;
    line-height: 1.6;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* === 现代容器查询支持 === */
@container (min-width: 768px) {
    .app-container {
        --sidebar-width: 22rem;
    }
}

@container (max-width: 767px) {
    .app-container {
        --sidebar-width: 100%;
    }
}

/* === 现代布局容器 === */
.app-container {
    display: grid;
    grid-template-areas:
        "header header"
        "controls controls"
        "main sidebar"
        "footer footer";
    grid-template-rows: var(--header-height) var(--control-height) 1fr var(--footer-height);
    grid-template-columns: 1fr var(--sidebar-width);
    block-size: 100vh;
    gap: 0;
    container-type: inline-size;
    isolation: isolate;
}

/* === 现代Subgrid支持 === */
@supports (grid-template-rows: subgrid) {
    .app-container {
        grid-template-rows: subgrid;
    }
}

/* === 现代顶部导航 === */
.top-navigation {
    grid-area: header;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-inline: var(--space-lg);
    background: var(--glass-bg);
    backdrop-filter: blur(20px) saturate(180%);
    border-block-end: 1px solid var(--border-color);
    z-index: 100;
    position: relative;
}

.top-navigation::before {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(90deg,
        transparent 0%,
        var(--accent-color) 50%,
        transparent 100%);
    opacity: 0.1;
    pointer-events: none;
}

.nav-brand {
    display: flex;
    align-items: center;
    gap: var(--space-md);
}

.brand-icon {
    font-size: var(--font-xl);
    color: var(--accent-color);
}

.brand-text {
    display: flex;
    flex-direction: column;
}

.brand-name {
    font-weight: 600;
    font-size: var(--font-lg);
}

.brand-tagline {
    font-size: var(--font-xs);
    color: var(--text-muted);
}

.nav-actions {
    display: flex;
    gap: var(--space-sm);
}

.nav-btn {
    inline-size: 2.5rem;
    block-size: 2.5rem;
    border: none;
    background: transparent;
    color: var(--text-secondary);
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all var(--transition-spring);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.nav-btn::before {
    content: '';
    position: absolute;
    inset: 0;
    background: var(--glass-bg);
    border-radius: inherit;
    opacity: 0;
    transition: opacity var(--transition-fast);
    z-index: -1;
}

.nav-btn:hover::before {
    opacity: 1;
}

.nav-btn:hover {
    color: var(--text-primary);
    transform: scale(1.05) translateZ(0);
}

.nav-btn:active {
    transform: scale(0.95) translateZ(0);
}

/* === 现代焦点样式 === */
.nav-btn:focus-visible {
    outline: 2px solid var(--accent-color);
    outline-offset: 2px;
}

/* === 控制面板 === */
.control-panel {
    grid-area: controls;
    display: flex;
    align-items: center;
    gap: var(--space-lg);
    padding: 0 var(--space-lg);
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--border-color);
    overflow-x: auto;
}

.panel-section {
    display: flex;
    align-items: center;
    gap: var(--space-md);
    flex-shrink: 0;
}

.section-title {
    font-size: var(--font-sm);
    color: var(--text-muted);
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: var(--space-xs);
    white-space: nowrap;
}

.view-controls,
.simulation-controls {
    display: flex;
    gap: var(--space-sm);
}

.view-btn,
.sim-btn {
    padding: var(--space-sm) var(--space-md);
    border: 1px solid var(--border-color);
    background: transparent;
    color: var(--text-secondary);
    border-radius: 6px;
    cursor: pointer;
    transition: all var(--transition-fast);
    font-size: var(--font-sm);
    white-space: nowrap;
    display: flex;
    align-items: center;
    gap: var(--space-xs);
}

.view-btn:hover,
.sim-btn:hover {
    background: var(--glass-bg);
    color: var(--text-primary);
    border-color: var(--accent-color);
}

.view-btn.active {
    background: var(--accent-color);
    color: white;
    border-color: var(--accent-color);
}

.sim-btn.primary {
    background: var(--accent-color);
    color: white;
    border-color: var(--accent-color);
}

/* === 主工作区 === */
.main-workspace {
    grid-area: main;
    position: relative;
    overflow: hidden;
    background: var(--primary-bg);
}

.scene-container {
    width: 100%;
    height: 100%;
    position: relative;
}

#three-canvas {
    width: 100%;
    height: 100%;
    display: block;
}

/* === 侧边栏 === */
.info-panel {
    grid-area: sidebar;
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    border-left: 1px solid var(--border-color);
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.panel-header {
    padding: var(--space-md);
    border-bottom: 1px solid var(--border-color);
    flex-shrink: 0;
}

.panel-tabs {
    display: flex;
    gap: var(--space-xs);
}

.tab-btn {
    padding: var(--space-sm) var(--space-md);
    border: none;
    background: transparent;
    color: var(--text-muted);
    border-radius: 6px;
    cursor: pointer;
    transition: all var(--transition-fast);
    font-size: var(--font-sm);
    display: flex;
    align-items: center;
    gap: var(--space-xs);
}

.tab-btn:hover {
    background: var(--glass-bg);
    color: var(--text-secondary);
}

.tab-btn.active {
    background: var(--accent-color);
    color: white;
}

.panel-content {
    flex: 1;
    padding: var(--space-md);
    overflow-y: auto;
}

.tab-panel {
    display: none;
}

.tab-panel.active {
    display: block;
}

/* === 底部状态栏 === */
.status-bar {
    grid-area: footer;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 var(--space-lg);
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    border-top: 1px solid var(--border-color);
    font-size: var(--font-sm);
}

.status-group {
    display: flex;
    gap: var(--space-lg);
}

.status-item {
    display: flex;
    align-items: center;
    gap: var(--space-xs);
    color: var(--text-muted);
}

/* === 场景覆盖层和标签 === */
.scene-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 10;
}

.layer-labels {
    position: relative;
    width: 100%;
    height: 100%;
}

.layer-label {
    position: absolute;
    pointer-events: auto;
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: var(--space-sm);
    min-width: 120px;
    transition: all var(--transition-normal);
    cursor: pointer;
}

.layer-label:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: var(--accent-color);
    transform: scale(1.05);
}

.label-content {
    display: flex;
    flex-direction: column;
    gap: var(--space-xs);
}

.label-header {
    display: flex;
    align-items: center;
    gap: var(--space-xs);
}

.label-number {
    width: 24px;
    height: 24px;
    background: var(--accent-color);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-sm);
    font-weight: 600;
}

.label-info {
    display: flex;
    flex-direction: column;
}

.label-name {
    font-size: var(--font-sm);
    font-weight: 500;
    color: var(--text-primary);
}

.label-english {
    font-size: var(--font-xs);
    color: var(--text-muted);
}

.label-description {
    font-size: var(--font-xs);
    color: var(--text-secondary);
    margin-top: var(--space-xs);
}

/* === 性能指标 === */
.performance-indicators {
    position: absolute;
    top: var(--space-md);
    right: var(--space-md);
    display: flex;
    flex-direction: column;
    gap: var(--space-sm);
    z-index: 20;
    pointer-events: none;
}

.indicator {
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    padding: var(--space-sm);
    display: flex;
    align-items: center;
    gap: var(--space-xs);
    font-size: var(--font-xs);
    color: var(--text-secondary);
}

.indicator-value {
    color: var(--accent-color);
    font-weight: 600;
}

/* === 交互模式 === */
.interaction-modes {
    display: flex;
    gap: var(--space-sm);
}

.interaction-mode {
    padding: var(--space-sm);
    border: 1px solid var(--border-color);
    background: transparent;
    border-radius: 6px;
    cursor: pointer;
    transition: all var(--transition-fast);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--space-xs);
    min-width: 60px;
}

.interaction-mode:hover {
    background: var(--glass-bg);
    border-color: var(--accent-color);
}

.interaction-mode.active {
    background: var(--accent-color);
    color: white;
    border-color: var(--accent-color);
}

.mode-icon {
    font-size: var(--font-lg);
}

.interaction-mode span {
    font-size: var(--font-xs);
}

/* === 启动画面 === */
.startup-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--primary-bg);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    transition: opacity var(--transition-slow);
}

.startup-screen.hidden {
    opacity: 0;
    pointer-events: none;
}

.startup-content {
    text-align: center;
    max-width: 400px;
}

.startup-logo {
    margin-bottom: var(--space-xl);
}

.logo-symbol {
    font-size: 4rem;
    margin-bottom: var(--space-md);
    color: var(--accent-color);
}

.startup-title {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: var(--space-sm);
    background: linear-gradient(45deg, var(--accent-color), #ff6b9d);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.startup-subtitle {
    color: var(--text-muted);
    margin-bottom: var(--space-xl);
}

.progress-bar {
    width: 100%;
    height: 4px;
    background: var(--secondary-bg);
    border-radius: 2px;
    overflow: hidden;
    margin-bottom: var(--space-md);
}

.progress-fill {
    height: 100%;
    background: var(--accent-color);
    width: 0%;
    transition: width var(--transition-normal);
}

.progress-text {
    font-size: var(--font-sm);
    color: var(--text-muted);
}

/* === 工具类 === */
.hidden {
    display: none !important;
}

/* === 现代响应式设计 === */
@container (max-width: 768px) {
    .app-container {
        grid-template-areas:
            "header"
            "controls"
            "main"
            "sidebar"
            "footer";
        grid-template-columns: 1fr;
        grid-template-rows: var(--header-height) auto 1fr 180px var(--footer-height);
    }

    .info-panel {
        border-left: none;
        border-top: 1px solid var(--border-color);
    }

    .control-panel {
        flex-wrap: wrap;
        block-size: auto;
        min-block-size: var(--control-height);
    }

    .nav-brand .brand-tagline {
        display: none;
    }
}

/* === 现代动画和过渡 === */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* === 现代颜色方案支持 === */
@media (prefers-color-scheme: light) {
    :root {
        --primary-bg: oklch(0.95 0.02 240);
        --secondary-bg: oklch(0.9 0.05 240);
        --text-primary: oklch(0.1 0 0);
        --text-secondary: oklch(0.3 0.02 240);
        --text-muted: oklch(0.5 0.05 240);
    }
}

/* === 现代高对比度支持 === */
@media (prefers-contrast: high) {
    :root {
        --border-color: oklch(1 0 0 / 0.3);
        --glass-bg: oklch(1 0 0 / 0.1);
    }
}

/* === 现代字体显示优化 === */
@supports (font-display: swap) {
    @font-face {
        font-family: 'Inter Variable';
        font-display: swap;
    }
}

/* === 现代滚动条样式 === */
::-webkit-scrollbar {
    inline-size: 8px;
}

::-webkit-scrollbar-track {
    background: var(--glass-bg);
}

::-webkit-scrollbar-thumb {
    background: var(--accent-color);
    border-radius: var(--radius-full);
}

::-webkit-scrollbar-thumb:hover {
    background: var(--accent-hover);
}

/* === 现代选择样式 === */
::selection {
    background: var(--accent-color);
    color: var(--text-primary);
}

/* === 现代焦点指示器 === */
:focus-visible {
    outline: 2px solid var(--accent-color);
    outline-offset: 2px;
    border-radius: var(--radius-sm);
}

/* === 现代加载状态 === */
.loading {
    background: linear-gradient(90deg,
        transparent 0%,
        var(--glass-bg) 50%,
        transparent 100%);
    background-size: 200% 100%;
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
}

/* === 现代错误状态 === */
.error {
    color: var(--error-color);
    border-color: var(--error-color);
}

.success {
    color: var(--success-color);
    border-color: var(--success-color);
}

.warning {
    color: var(--warning-color);
    border-color: var(--warning-color);
}

/* === 现代工具提示 === */
[data-tooltip] {
    position: relative;
}

[data-tooltip]::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: var(--secondary-bg);
    color: var(--text-primary);
    padding: var(--space-xs) var(--space-sm);
    border-radius: var(--radius-sm);
    font-size: var(--font-xs);
    white-space: nowrap;
    opacity: 0;
    pointer-events: none;
    transition: opacity var(--transition-fast);
    z-index: 1000;
}

[data-tooltip]:hover::after {
    opacity: 1;
}
