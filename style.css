/* OSI 3D 生物仿生艺术模拟器 - 样式系统 */

/* === 基础重置 === */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* === CSS变量定义 === */
:root {
    /* 颜色系统 */
    --primary-bg: #0a0b1e;
    --secondary-bg: #1a1b2e;
    --accent-color: #4facfe;
    --text-primary: #ffffff;
    --text-secondary: #cbd5e1;
    --text-muted: #94a3b8;
    --border-color: rgba(255, 255, 255, 0.1);
    --glass-bg: rgba(255, 255, 255, 0.05);
    
    /* 间距系统 */
    --space-xs: 0.25rem;
    --space-sm: 0.5rem;
    --space-md: 1rem;
    --space-lg: 1.5rem;
    --space-xl: 2rem;
    
    /* 字体系统 */
    --font-xs: 0.75rem;
    --font-sm: 0.875rem;
    --font-base: 1rem;
    --font-lg: 1.125rem;
    --font-xl: 1.25rem;
    
    /* 布局尺寸 */
    --header-height: 60px;
    --control-height: 80px;
    --sidebar-width: 320px;
    --footer-height: 50px;
    
    /* 动画 */
    --transition-fast: 0.15s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;
}

/* === 基础样式 === */
html {
    height: 100%;
    font-size: 16px;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    background: linear-gradient(135deg, var(--primary-bg) 0%, var(--secondary-bg) 100%);
    color: var(--text-primary);
    height: 100vh;
    overflow: hidden;
    line-height: 1.5;
}

/* === 主布局容器 === */
.app-container {
    display: grid;
    grid-template-areas: 
        "header header"
        "controls controls"
        "main sidebar"
        "footer footer";
    grid-template-rows: var(--header-height) var(--control-height) 1fr var(--footer-height);
    grid-template-columns: 1fr var(--sidebar-width);
    height: 100vh;
    gap: 0;
}

/* === 顶部导航 === */
.top-navigation {
    grid-area: header;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 var(--space-lg);
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--border-color);
    z-index: 100;
}

.nav-brand {
    display: flex;
    align-items: center;
    gap: var(--space-md);
}

.brand-icon {
    font-size: var(--font-xl);
    color: var(--accent-color);
}

.brand-text {
    display: flex;
    flex-direction: column;
}

.brand-name {
    font-weight: 600;
    font-size: var(--font-lg);
}

.brand-tagline {
    font-size: var(--font-xs);
    color: var(--text-muted);
}

.nav-actions {
    display: flex;
    gap: var(--space-sm);
}

.nav-btn {
    width: 40px;
    height: 40px;
    border: none;
    background: transparent;
    color: var(--text-secondary);
    border-radius: 8px;
    cursor: pointer;
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
}

.nav-btn:hover {
    background: var(--glass-bg);
    color: var(--text-primary);
    transform: scale(1.05);
}

/* === 控制面板 === */
.control-panel {
    grid-area: controls;
    display: flex;
    align-items: center;
    gap: var(--space-lg);
    padding: 0 var(--space-lg);
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--border-color);
    overflow-x: auto;
}

.panel-section {
    display: flex;
    align-items: center;
    gap: var(--space-md);
    flex-shrink: 0;
}

.section-title {
    font-size: var(--font-sm);
    color: var(--text-muted);
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: var(--space-xs);
    white-space: nowrap;
}

.view-controls,
.simulation-controls {
    display: flex;
    gap: var(--space-sm);
}

.view-btn,
.sim-btn {
    padding: var(--space-sm) var(--space-md);
    border: 1px solid var(--border-color);
    background: transparent;
    color: var(--text-secondary);
    border-radius: 6px;
    cursor: pointer;
    transition: all var(--transition-fast);
    font-size: var(--font-sm);
    white-space: nowrap;
    display: flex;
    align-items: center;
    gap: var(--space-xs);
}

.view-btn:hover,
.sim-btn:hover {
    background: var(--glass-bg);
    color: var(--text-primary);
    border-color: var(--accent-color);
}

.view-btn.active {
    background: var(--accent-color);
    color: white;
    border-color: var(--accent-color);
}

.sim-btn.primary {
    background: var(--accent-color);
    color: white;
    border-color: var(--accent-color);
}

/* === 主工作区 === */
.main-workspace {
    grid-area: main;
    position: relative;
    overflow: hidden;
    background: var(--primary-bg);
}

.scene-container {
    width: 100%;
    height: 100%;
    position: relative;
}

#three-canvas {
    width: 100%;
    height: 100%;
    display: block;
}

/* === 侧边栏 === */
.info-panel {
    grid-area: sidebar;
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    border-left: 1px solid var(--border-color);
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.panel-header {
    padding: var(--space-md);
    border-bottom: 1px solid var(--border-color);
    flex-shrink: 0;
}

.panel-tabs {
    display: flex;
    gap: var(--space-xs);
}

.tab-btn {
    padding: var(--space-sm) var(--space-md);
    border: none;
    background: transparent;
    color: var(--text-muted);
    border-radius: 6px;
    cursor: pointer;
    transition: all var(--transition-fast);
    font-size: var(--font-sm);
    display: flex;
    align-items: center;
    gap: var(--space-xs);
}

.tab-btn:hover {
    background: var(--glass-bg);
    color: var(--text-secondary);
}

.tab-btn.active {
    background: var(--accent-color);
    color: white;
}

.panel-content {
    flex: 1;
    padding: var(--space-md);
    overflow-y: auto;
}

.tab-panel {
    display: none;
}

.tab-panel.active {
    display: block;
}

/* === 底部状态栏 === */
.status-bar {
    grid-area: footer;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 var(--space-lg);
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    border-top: 1px solid var(--border-color);
    font-size: var(--font-sm);
}

.status-group {
    display: flex;
    gap: var(--space-lg);
}

.status-item {
    display: flex;
    align-items: center;
    gap: var(--space-xs);
    color: var(--text-muted);
}

/* === 场景覆盖层和标签 === */
.scene-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 10;
}

.layer-labels {
    position: relative;
    width: 100%;
    height: 100%;
}

.layer-label {
    position: absolute;
    pointer-events: auto;
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: var(--space-sm);
    min-width: 120px;
    transition: all var(--transition-normal);
    cursor: pointer;
}

.layer-label:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: var(--accent-color);
    transform: scale(1.05);
}

.label-content {
    display: flex;
    flex-direction: column;
    gap: var(--space-xs);
}

.label-header {
    display: flex;
    align-items: center;
    gap: var(--space-xs);
}

.label-number {
    width: 24px;
    height: 24px;
    background: var(--accent-color);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-sm);
    font-weight: 600;
}

.label-info {
    display: flex;
    flex-direction: column;
}

.label-name {
    font-size: var(--font-sm);
    font-weight: 500;
    color: var(--text-primary);
}

.label-english {
    font-size: var(--font-xs);
    color: var(--text-muted);
}

.label-description {
    font-size: var(--font-xs);
    color: var(--text-secondary);
    margin-top: var(--space-xs);
}

/* === 性能指标 === */
.performance-indicators {
    position: absolute;
    top: var(--space-md);
    right: var(--space-md);
    display: flex;
    flex-direction: column;
    gap: var(--space-sm);
    z-index: 20;
    pointer-events: none;
}

.indicator {
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    padding: var(--space-sm);
    display: flex;
    align-items: center;
    gap: var(--space-xs);
    font-size: var(--font-xs);
    color: var(--text-secondary);
}

.indicator-value {
    color: var(--accent-color);
    font-weight: 600;
}

/* === 交互模式 === */
.interaction-modes {
    display: flex;
    gap: var(--space-sm);
}

.interaction-mode {
    padding: var(--space-sm);
    border: 1px solid var(--border-color);
    background: transparent;
    border-radius: 6px;
    cursor: pointer;
    transition: all var(--transition-fast);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--space-xs);
    min-width: 60px;
}

.interaction-mode:hover {
    background: var(--glass-bg);
    border-color: var(--accent-color);
}

.interaction-mode.active {
    background: var(--accent-color);
    color: white;
    border-color: var(--accent-color);
}

.mode-icon {
    font-size: var(--font-lg);
}

.interaction-mode span {
    font-size: var(--font-xs);
}

/* === 启动画面 === */
.startup-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--primary-bg);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    transition: opacity var(--transition-slow);
}

.startup-screen.hidden {
    opacity: 0;
    pointer-events: none;
}

.startup-content {
    text-align: center;
    max-width: 400px;
}

.startup-logo {
    margin-bottom: var(--space-xl);
}

.logo-symbol {
    font-size: 4rem;
    margin-bottom: var(--space-md);
    color: var(--accent-color);
}

.startup-title {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: var(--space-sm);
    background: linear-gradient(45deg, var(--accent-color), #ff6b9d);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.startup-subtitle {
    color: var(--text-muted);
    margin-bottom: var(--space-xl);
}

.progress-bar {
    width: 100%;
    height: 4px;
    background: var(--secondary-bg);
    border-radius: 2px;
    overflow: hidden;
    margin-bottom: var(--space-md);
}

.progress-fill {
    height: 100%;
    background: var(--accent-color);
    width: 0%;
    transition: width var(--transition-normal);
}

.progress-text {
    font-size: var(--font-sm);
    color: var(--text-muted);
}

/* === 工具类 === */
.hidden {
    display: none !important;
}

/* === 响应式设计 === */
@media (max-width: 768px) {
    .app-container {
        grid-template-areas: 
            "header"
            "controls"
            "main"
            "sidebar"
            "footer";
        grid-template-columns: 1fr;
        grid-template-rows: var(--header-height) auto 1fr 180px var(--footer-height);
    }
    
    .info-panel {
        border-left: none;
        border-top: 1px solid var(--border-color);
    }
    
    .control-panel {
        flex-wrap: wrap;
        height: auto;
        min-height: var(--control-height);
    }
    
    .nav-brand .brand-tagline {
        display: none;
    }
}
