/* OSI 3D 生物仿生艺术模拟器样式 - 高级美化版本 */

/* === 基础重置和变量定义 === */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* 主题色彩 - 增强版 */
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%);
    --secondary-gradient: linear-gradient(45deg, #fa709a 0%, #fee140 100%);
    --neon-blue: linear-gradient(45deg, #4facfe, #00f2fe);
    --neon-purple: linear-gradient(45deg, #c471f5, #fa71cd);
    --neon-green: linear-gradient(45deg, #11998e, #38ef7d);
    --accent-color: #f59e0b;
    --success-color: #10b981;
    --danger-color: #ef4444;
    --warning-color: #f59e0b;
    
    /* 全息效果色彩 */
    --hologram-cyan: #00ffff;
    --hologram-magenta: #ff00ff;
    --hologram-yellow: #ffff00;
    --hologram-white: #ffffff;
    
    /* 背景色彩 - 增强版 */
    --bg-primary: radial-gradient(ellipse at center, #0a0b1e 0%, #050614 100%);
    --bg-secondary: linear-gradient(135deg, #1e1b2e 0%, #16213e 50%, #0f3460 100%);
    --bg-tertiary: linear-gradient(45deg, #2a1e3e, #1a1a2e, #2d1b69);
    --bg-card: rgba(255, 255, 255, 0.08);
    --bg-hover: rgba(255, 255, 255, 0.12);
    --bg-modal: rgba(10, 11, 30, 0.95);
    
    /* 文字色彩 - 层次增强 */
    --text-primary: #f8fafc;
    --text-secondary: #e2e8f0;
    --text-muted: #94a3b8;
    --text-accent: transparent;
    
    /* 边框和装饰 - 全息效果 */
    --border-color: rgba(255, 255, 255, 0.15);
    --border-hover: rgba(255, 255, 255, 0.25);
    --border-glow: rgba(79, 172, 254, 0.5);
    --glass-bg: rgba(255, 255, 255, 0.08);
    --glass-border: rgba(255, 255, 255, 0.15);
    --glass-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
    
    /* 阴影系统 */
    --shadow-small: 0 2px 8px rgba(0, 0, 0, 0.1);
    --shadow-medium: 0 4px 16px rgba(0, 0, 0, 0.15);
    --shadow-large: 0 8px 32px rgba(0, 0, 0, 0.2);
    --shadow-glow: 0 0 20px rgba(79, 172, 254, 0.3);
    --shadow-neon: 0 0 10px currentColor, 0 0 20px currentColor, 0 0 30px currentColor;
    
    /* 文字色彩 */
    --text-primary: #f8fafc;
    --text-secondary: #cbd5e1;
    --text-muted: #94a3b8;
    
    /* 边框和装饰 */
    --border-color: rgba(255, 255, 255, 0.2);
    --border-hover: rgba(255, 255, 255, 0.3);
    --glass-bg: rgba(255, 255, 255, 0.1);
    --glass-border: rgba(255, 255, 255, 0.2);
    
    /* 响应式间距 */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    
    /* 响应式字体 */
    --font-xs: 0.75rem;
    --font-sm: 0.875rem;
    --font-base: 1rem;
    --font-lg: 1.125rem;
    --font-xl: 1.25rem;
    --font-2xl: 1.5rem;
    --font-3xl: 1.875rem;
    
    /* OSI层级颜色 */
    --layer-7-color: #E6B3FF;
    --layer-6-color: #FF6B9D;
    --layer-5-color: #00E5FF;
    --layer-4-color: #69F0AE;
    --layer-3-color: #FFD54F;
    --layer-2-color: #FF5722;
    --layer-1-color: #9C27B0;
}

/* 流体排版 */
html {
    font-size: clamp(14px, 1.5vw, 16px);
    scroll-behavior: smooth;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: var(--bg-primary);
    color: var(--text-primary);
    line-height: 1.6;
    overflow: hidden;
    height: 100vh;
    min-height: 100dvh;
    position: relative;
}

/* 动态背景效果 */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: 
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.1) 0%, transparent 50%);
    animation: backgroundShift 20s ease-in-out infinite;
    pointer-events: none;
    z-index: -1;
}

@keyframes backgroundShift {
    0%, 100% { opacity: 1; transform: scale(1) rotate(0deg); }
    33% { opacity: 0.8; transform: scale(1.1) rotate(2deg); }
    66% { opacity: 0.9; transform: scale(0.95) rotate(-1deg); }
}

/* 应用容器 */
.app-container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    min-height: 100dvh;
    position: relative;
}

/* === 启动画面 === */
.startup-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 50%, var(--bg-tertiary) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    opacity: 1;
    transition: opacity 0.8s ease-out;
}

.startup-screen.hidden {
    opacity: 0;
    pointer-events: none;
}

.startup-content {
    text-align: center;
    max-width: 500px;
    padding: var(--spacing-xl);
}

.startup-logo {
    margin-bottom: var(--spacing-2xl);
}

.logo-symbol {
    font-size: clamp(3rem, 8vw, 5rem);
    margin-bottom: var(--spacing-lg);
    background: var(--primary-gradient);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    animation: logoFloat 4s ease-in-out infinite;
    position: relative;
    display: inline-block;
}

.logo-symbol::before {
    content: '🧬';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--neon-blue);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    opacity: 0;
    animation: logoGlow 3s ease-in-out infinite alternate;
}

@keyframes logoFloat {
    0%, 100% { 
        transform: translateY(0px) rotate(0deg); 
        filter: hue-rotate(0deg);
    }
    25% { 
        transform: translateY(-10px) rotate(5deg); 
        filter: hue-rotate(90deg);
    }
    50% { 
        transform: translateY(-5px) rotate(0deg); 
        filter: hue-rotate(180deg);
    }
    75% { 
        transform: translateY(-15px) rotate(-5deg); 
        filter: hue-rotate(270deg);
    }
}

@keyframes logoGlow {
    0% { opacity: 0; transform: scale(1); }
    100% { opacity: 0.3; transform: scale(1.05); }
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

.startup-title {
    font-size: clamp(2rem, 6vw, 3.5rem);
    font-weight: 800;
    margin-bottom: var(--spacing-sm);
    background: var(--primary-gradient);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    position: relative;
    overflow: hidden;
    animation: titleGlow 2s ease-in-out infinite alternate;
}

.startup-title::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    animation: titleShine 3s ease-in-out infinite;
}

@keyframes titleGlow {
    0% { 
        filter: brightness(1) saturate(1);
        text-shadow: 0 0 20px rgba(79, 172, 254, 0.3);
    }
    100% { 
        filter: brightness(1.2) saturate(1.3);
        text-shadow: 0 0 30px rgba(79, 172, 254, 0.6);
    }
}

@keyframes titleShine {
    0% { left: -100%; }
    50% { left: 100%; }
    100% { left: 100%; }
}

.startup-subtitle {
    font-size: clamp(1rem, 2.5vw, 1.4rem);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xl);
    font-weight: 300;
    opacity: 0;
    animation: subtitleFadeIn 1s ease-out 0.5s forwards;
}

@keyframes subtitleFadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.progress-bar {
    width: 100%;
    height: 6px;
    background: var(--bg-secondary);
    border-radius: 3px;
    overflow: hidden;
    margin-bottom: var(--spacing-md);
    position: relative;
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.3);
}

.progress-bar::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, transparent 25%, rgba(255, 255, 255, 0.1) 25%, rgba(255, 255, 255, 0.1) 50%, transparent 50%, transparent 75%, rgba(255, 255, 255, 0.1) 75%);
    background-size: 20px 20px;
    animation: progressPattern 1s linear infinite;
}

@keyframes progressPattern {
    0% { transform: translateX(0); }
    100% { transform: translateX(20px); }
}

.progress-fill {
    height: 100%;
    background: var(--primary-gradient);
    border-radius: 3px;
    transition: width 0.3s ease;
    width: 0%;
    position: relative;
    box-shadow: var(--shadow-glow);
}

.progress-fill::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 20px;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.5));
    border-radius: 0 3px 3px 0;
    animation: progressShine 1.5s ease-in-out infinite;
}

@keyframes progressShine {
    0%, 100% { opacity: 0; }
    50% { opacity: 1; }
}

.startup-progress {
    width: 100%;
    max-width: 400px;
    margin: 0 auto;
    opacity: 0;
    animation: progressFadeIn 1s ease-out 1s forwards;
}

@keyframes progressFadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.progress-text {
    font-size: var(--font-sm);
    color: var(--text-secondary);
    text-align: center;
    opacity: 0;
    animation: textPulse 2s ease-in-out infinite, progressTextFade 1s ease-out 1.5s forwards;
}

@keyframes textPulse {
    0%, 100% { opacity: 0.7; }
    50% { opacity: 1; }
}

@keyframes progressTextFade {
    from { opacity: 0; }
    to { opacity: 1; }
}

/* === 顶部导航栏 - 增强版 === */
.top-navigation {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-md) var(--spacing-xl);
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid var(--border-color);
    box-shadow: var(--glass-shadow);
    z-index: 100;
    position: relative;
}

.top-navigation::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(79, 172, 254, 0.05), transparent);
    animation: navGlow 4s ease-in-out infinite;
    pointer-events: none;
}

@keyframes navGlow {
    0%, 100% { opacity: 0; }
    50% { opacity: 1; }
}

.nav-brand {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    z-index: 1;
}

.brand-icon {
    font-size: var(--font-2xl);
    background: var(--neon-blue);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    animation: brandPulse 3s ease-in-out infinite;
}

@keyframes brandPulse {
    0%, 100% { 
        transform: scale(1);
        filter: brightness(1);
    }
    50% { 
        transform: scale(1.1);
        filter: brightness(1.3);
    }
}

.brand-text {
    display: flex;
    flex-direction: column;
}

.brand-name {
    font-size: var(--font-lg);
    font-weight: 700;
    color: var(--text-primary);
    text-shadow: 0 0 10px rgba(79, 172, 254, 0.3);
}

.brand-tagline {
    font-size: var(--font-xs);
    color: var(--text-muted);
    opacity: 0.8;
}

.nav-actions {
    display: flex;
    gap: var(--spacing-sm);
    z-index: 1;
}

.nav-btn {
    width: 44px;
    height: 44px;
    border: none;
    background: var(--glass-bg);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    color: var(--text-secondary);
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.nav-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.nav-btn:hover {
    background: var(--bg-hover);
    border-color: var(--border-glow);
    color: var(--text-primary);
    transform: translateY(-2px);
    box-shadow: var(--shadow-glow);
}

.nav-btn:hover::before {
    left: 100%;
}

.nav-btn:active {
    transform: translateY(0);
}

/* === 主控制面板 - 增强版 === */
.control-panel {
    display: flex;
    gap: var(--spacing-lg);
    padding: var(--spacing-lg) var(--spacing-xl);
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid var(--border-color);
    box-shadow: var(--glass-shadow);
    flex-wrap: wrap;
    position: relative;
    overflow: hidden;
}

.control-panel::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(79, 172, 254, 0.03), transparent);
    animation: panelSweep 8s ease-in-out infinite;
    pointer-events: none;
}

@keyframes panelSweep {
    0% { left: -100%; }
    50% { left: 100%; }
    100% { left: 100%; }
}

.panel-section {
    flex: 1;
    min-width: 200px;
    position: relative;
    z-index: 1;
}

.section-title {
    font-size: var(--font-sm);
    font-weight: 600;
    color: var(--text-secondary);
    margin-bottom: var(--spacing-md);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    position: relative;
}

.section-title::after {
    content: '';
    flex: 1;
    height: 1px;
    background: linear-gradient(90deg, var(--border-color), transparent);
    margin-left: var(--spacing-md);
}

.view-controls, .simulation-controls, .interaction-modes {
    display: flex;
    gap: var(--spacing-sm);
    flex-wrap: wrap;
}

.view-btn, .sim-btn {
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid var(--border-color);
    background: var(--glass-bg);
    border-radius: 10px;
    color: var(--text-secondary);
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: var(--font-sm);
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
}

.view-btn::before, .sim-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transform: translateX(-100%);
    transition: transform 0.6s ease;
}

.view-btn:hover, .sim-btn:hover {
    background: var(--bg-hover);
    border-color: var(--border-glow);
    color: var(--text-primary);
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.view-btn:hover::before, .sim-btn:hover::before {
    transform: translateX(100%);
}

.view-btn.active {
    background: var(--primary-gradient);
    border-color: transparent;
    color: white;
    box-shadow: var(--shadow-glow);
    transform: translateY(-1px);
}

.sim-btn.primary {
    background: var(--neon-blue);
    border-color: transparent;
    color: white;
    box-shadow: var(--shadow-glow);
    animation: primaryPulse 2s ease-in-out infinite;
}

@keyframes primaryPulse {
    0%, 100% { 
        box-shadow: var(--shadow-glow);
        transform: scale(1);
    }
    50% { 
        box-shadow: 0 0 25px rgba(79, 172, 254, 0.5);
        transform: scale(1.02);
    }
}

.sim-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
    animation: none;
}

.interaction-mode {
    padding: var(--spacing-md);
    border: 1px solid var(--border-color);
    background: var(--glass-bg);
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-xs);
    min-width: 70px;
    position: relative;
    overflow: hidden;
}

.interaction-mode::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: radial-gradient(circle, rgba(79, 172, 254, 0.2), transparent);
    transform: translate(-50%, -50%);
    transition: all 0.3s ease;
}

.interaction-mode:hover {
    background: var(--bg-hover);
    border-color: var(--border-glow);
    transform: translateY(-2px) scale(1.05);
    box-shadow: var(--shadow-medium);
}

.interaction-mode:hover::before {
    width: 100%;
    height: 100%;
}

.interaction-mode.active {
    background: var(--neon-purple);
    border-color: transparent;
    color: white;
    box-shadow: 0 0 20px rgba(196, 113, 245, 0.4);
}

.mode-icon {
    font-size: var(--font-lg);
    transition: transform 0.3s ease;
}

.interaction-mode:hover .mode-icon {
    transform: scale(1.2) rotate(5deg);
}

/* === 主工作区 === */
.main-workspace {
    display: flex;
    flex: 1;
    overflow: hidden;
}

.scene-container {
    flex: 1;
    position: relative;
    background: var(--bg-primary);
}

#three-canvas {
    width: 100%;
    height: 100%;
    display: block;
}

/* === 层级标签 - 高级美化版 === */
.layer-label {
    position: absolute;
    pointer-events: auto;
    cursor: pointer;
    transform: translate(-50%, -50%);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.layer-label.enhanced {
    background: var(--glass-bg);
    backdrop-filter: blur(15px);
    border: 1px solid var(--border-color);
    border-radius: 16px;
    padding: var(--spacing-md);
    min-width: 220px;
    position: relative;
    overflow: hidden;
    box-shadow: var(--glass-shadow);
}

.layer-label.enhanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, 
        rgba(255, 255, 255, 0.1), 
        transparent 30%, 
        transparent 70%, 
        rgba(255, 255, 255, 0.05));
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.layer-label:hover {
    transform: translate(-50%, -50%) scale(1.08) rotate(1deg);
    background: var(--bg-hover);
    border-color: var(--layer-color);
    box-shadow: 
        var(--glass-shadow),
        0 0 30px rgba(var(--layer-color-rgb), 0.3);
}

.layer-label:hover.enhanced::before {
    opacity: 1;
}

.layer-label[data-layer="7"] { 
    --layer-color: var(--layer-7-color); 
    --layer-color-rgb: 230, 179, 255;
}
.layer-label[data-layer="6"] { 
    --layer-color: var(--layer-6-color);
    --layer-color-rgb: 255, 107, 157;
}
.layer-label[data-layer="5"] { 
    --layer-color: var(--layer-5-color);
    --layer-color-rgb: 0, 229, 255;
}
.layer-label[data-layer="4"] { 
    --layer-color: var(--layer-4-color);
    --layer-color-rgb: 105, 240, 174;
}
.layer-label[data-layer="3"] { 
    --layer-color: var(--layer-3-color);
    --layer-color-rgb: 255, 213, 79;
}
.layer-label[data-layer="2"] { 
    --layer-color: var(--layer-2-color);
    --layer-color-rgb: 255, 87, 34;
}
.layer-label[data-layer="1"] { 
    --layer-color: var(--layer-1-color);
    --layer-color-rgb: 156, 39, 176;
}

.label-content {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    position: relative;
    z-index: 1;
}

.label-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.label-number {
    width: 36px;
    height: 36px;
    background: linear-gradient(135deg, var(--layer-color), color-mix(in srgb, var(--layer-color), #000 20%));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    color: var(--bg-primary);
    position: relative;
    box-shadow: 0 4px 12px rgba(var(--layer-color-rgb), 0.3);
}

.label-number::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: conic-gradient(var(--layer-color), transparent, var(--layer-color));
    border-radius: 50%;
    z-index: -1;
    opacity: 0;
    animation: numberGlow 3s ease-in-out infinite;
}

@keyframes numberGlow {
    0%, 100% { opacity: 0; transform: rotate(0deg); }
    50% { opacity: 0.6; transform: rotate(180deg); }
}

.label-pulse {
    width: 10px;
    height: 10px;
    background: var(--layer-color);
    border-radius: 50%;
    position: relative;
    box-shadow: 0 0 10px var(--layer-color);
}

.label-pulse::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(var(--layer-color-rgb), 0.4), transparent);
    border-radius: 50%;
    animation: pulseExpand 2s ease-in-out infinite;
}

@keyframes pulseExpand {
    0%, 100% { transform: scale(0.5); opacity: 1; }
    50% { transform: scale(1.5); opacity: 0; }
}

.label-info {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.label-name {
    font-weight: 600;
    color: var(--text-primary);
    font-size: var(--font-base);
    text-shadow: 0 0 10px rgba(var(--layer-color-rgb), 0.3);
}

.label-english {
    font-size: var(--font-xs);
    color: var(--text-muted);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.label-description {
    font-size: var(--font-xs);
    color: var(--text-secondary);
    opacity: 0.8;
}

.label-bio-indicator {
    color: var(--layer-color);
    font-size: var(--font-xl);
    animation: indicatorFloat 4s ease-in-out infinite;
    filter: drop-shadow(0 0 8px var(--layer-color));
}

@keyframes indicatorFloat {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    25% { transform: translateY(-3px) rotate(2deg); }
    75% { transform: translateY(3px) rotate(-2deg); }
}

/* === 性能指标 === */
.performance-indicators {
    position: absolute;
    top: var(--spacing-lg);
    right: var(--spacing-lg);
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
    pointer-events: none;
}

.indicator {
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: var(--spacing-sm) var(--spacing-md);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    min-width: 120px;
}

.indicator-label {
    font-size: var(--font-xs);
    color: var(--text-muted);
}

.indicator-value {
    font-weight: 600;
    color: var(--text-primary);
    margin-left: auto;
}

/* === 信息面板增强 === */
.info-panel {
    width: 400px;
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    border-left: 1px solid var(--border-color);
    display: flex;
    flex-direction: column;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    z-index: 50;
}

.info-panel.collapsed {
    width: 60px;
    overflow: hidden;
}

.info-panel.collapsed .panel-content {
    opacity: 0;
    pointer-events: none;
}

.info-panel.collapsed .panel-tabs {
    opacity: 0;
    pointer-events: none;
}

.panel-toggle {
    position: absolute;
    top: 50%;
    left: -20px;
    width: 40px;
    height: 40px;
    background: var(--glass-bg);
    border: 1px solid var(--border-color);
    border-radius: 50%;
    cursor: pointer;
    transform: translateY(-50%);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-muted);
    backdrop-filter: blur(10px);
    z-index: 60;
}

.panel-toggle:hover {
    background: var(--bg-hover);
    border-color: var(--border-glow);
    color: var(--text-primary);
    transform: translateY(-50%) scale(1.1);
    box-shadow: var(--shadow-glow);
}

.panel-header {
    display: flex;
    align-items: center;
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.05), transparent);
    position: relative;
}

.panel-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(79, 172, 254, 0.03), transparent);
    animation: headerGlow 4s ease-in-out infinite;
    pointer-events: none;
}

@keyframes headerGlow {
    0%, 100% { opacity: 0; }
    50% { opacity: 1; }
}

.panel-tabs {
    display: flex;
    flex: 1;
    gap: var(--spacing-xs);
}

.tab-btn {
    padding: var(--spacing-sm) var(--spacing-md);
    border: none;
    background: transparent;
    color: var(--text-muted);
    cursor: pointer;
    border-radius: 6px;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: var(--font-sm);
}

.tab-btn:hover {
    background: var(--bg-hover);
    color: var(--text-secondary);
}

.tab-btn.active {
    background: var(--primary-gradient);
    color: white;
}

.panel-content {
    flex: 1;
    overflow-y: auto;
    padding: var(--spacing-lg);
}

.tab-panel {
    display: none;
}

.tab-panel.active {
    display: block;
}

/* === 层级详情卡片 === */
.layer-detail-card {
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-md);
}

.layer-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-md);
    border-bottom: 1px solid var(--border-color);
}

.layer-number {
    width: 48px;
    height: 48px;
    background: var(--primary-gradient);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: var(--font-xl);
    color: white;
}

.layer-info h3 {
    font-size: var(--font-xl);
    font-weight: 600;
    margin-bottom: var(--spacing-xs);
}

.layer-english {
    font-size: var(--font-sm);
    color: var(--text-muted);
}

.layer-content .section {
    margin-bottom: var(--spacing-lg);
}

.layer-content h4 {
    font-size: var(--font-lg);
    font-weight: 600;
    margin-bottom: var(--spacing-md);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.protocol-tags {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-sm);
}

.protocol-tag {
    background: linear-gradient(45deg, rgba(79, 172, 254, 0.1), rgba(0, 242, 254, 0.1));
    border: 1px solid rgba(79, 172, 254, 0.3);
    padding: var(--spacing-xs) var(--spacing-md);
    border-radius: 20px;
    font-size: var(--font-xs);
    color: var(--text-secondary);
    transition: all 0.3s ease;
}

.protocol-tag:hover {
    background: linear-gradient(45deg, rgba(79, 172, 254, 0.2), rgba(0, 242, 254, 0.2));
    border-color: rgba(79, 172, 254, 0.5);
    color: var(--text-primary);
    transform: translateY(-1px);
}

/* === 加载覆盖层 === */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(10, 11, 30, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 1;
    transition: opacity 0.5s ease;
}

.loading-overlay.hidden {
    opacity: 0;
    pointer-events: none;
}

.loading-content {
    text-align: center;
    max-width: 400px;
    padding: var(--spacing-xl);
}

.loading-animation {
    margin-bottom: var(--spacing-xl);
}

.dna-helix {
    width: 80px;
    height: 80px;
    margin: 0 auto var(--spacing-lg);
    position: relative;
}

.helix-strand {
    position: absolute;
    width: 100%;
    height: 100%;
    border: 3px solid;
    border-radius: 50%;
    animation: helixRotate 2s linear infinite;
}

.strand-1 {
    border-color: #4facfe transparent #4facfe transparent;
}

.strand-2 {
    border-color: transparent #00f2fe transparent #00f2fe;
    animation-delay: -1s;
}

@keyframes helixRotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-details {
    font-size: var(--font-sm);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-md);
}

.loading-progress {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.progress-track {
    flex: 1;
    height: 4px;
    background: var(--bg-secondary);
    border-radius: 2px;
    overflow: hidden;
}

.progress-indicator {
    height: 100%;
    background: var(--primary-gradient);
    border-radius: 2px;
    transition: width 0.3s ease;
    width: 0%;
}

.progress-percentage {
    font-size: var(--font-sm);
    color: var(--text-secondary);
    min-width: 40px;
}

/* === 底部状态栏 === */
.status-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-md) var(--spacing-xl);
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    border-top: 1px solid var(--border-color);
    font-size: var(--font-sm);
}

.status-group {
    display: flex;
    gap: var(--spacing-lg);
    align-items: center;
}

.status-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    color: var(--text-secondary);
}

.status-item i {
    color: var(--text-muted);
}

.simulation-status {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-weight: 500;
}

.status-indicator {
    position: relative;
    width: 12px;
    height: 12px;
}

.status-dot {
    width: 8px;
    height: 8px;
    background: var(--success-color);
    border-radius: 50%;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.status-ring {
    width: 12px;
    height: 12px;
    border: 1px solid var(--success-color);
    border-radius: 50%;
    animation: statusPulse 2s infinite;
}

@keyframes statusPulse {
    0% { opacity: 1; transform: scale(1); }
    100% { opacity: 0; transform: scale(1.5); }
}

/* === 响应式设计增强 === */
@media (max-width: 1024px) {
    .info-panel {
        width: 350px;
    }
    
    .control-panel {
        flex-direction: column;
        gap: var(--spacing-md);
    }
    
    .panel-section {
        min-width: auto;
    }
    
    .performance-indicators {
        scale: 0.9;
        gap: var(--spacing-xs);
    }
    
    .layer-label.enhanced {
        min-width: 180px;
        padding: var(--spacing-sm) var(--spacing-md);
    }
    
    .toast-notification {
        max-width: 280px;
        right: var(--spacing-md);
        top: var(--spacing-md);
    }
}

@media (max-width: 768px) {
    :root {
        --font-base: 0.875rem;
        --font-sm: 0.75rem;
        --font-xs: 0.625rem;
        --spacing-md: 0.75rem;
        --spacing-lg: 1rem;
        --spacing-xl: 1.5rem;
    }
    
    .main-workspace {
        flex-direction: column;
    }
    
    .info-panel {
        width: 100%;
        max-height: 40vh;
        order: -1;
        border-left: none;
        border-bottom: 1px solid var(--border-color);
    }
    
    .info-panel.collapsed {
        height: 60px;
        width: 100%;
    }
    
    .panel-toggle {
        top: -20px;
        left: 50%;
        transform: translateX(-50%);
    }
    
    .info-panel.collapsed .panel-toggle {
        transform: translateX(-50%) rotate(180deg);
    }
    
    .control-panel {
        padding: var(--spacing-md);
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .panel-section {
        flex: 1 1 100%;
        min-width: 100%;
        text-align: center;
    }
    
    .view-controls,
    .simulation-controls,
    .interaction-modes {
        justify-content: center;
        flex-wrap: wrap;
        gap: var(--spacing-xs);
    }
    
    .view-btn, .sim-btn {
        flex: 1 1 auto;
        min-width: 120px;
        padding: var(--spacing-sm);
        font-size: var(--font-xs);
    }
    
    .interaction-mode {
        min-width: 60px;
        padding: var(--spacing-sm);
    }
    
    .top-navigation {
        padding: var(--spacing-md);
        flex-wrap: wrap;
        gap: var(--spacing-sm);
    }
    
    .nav-brand {
        flex: 1;
        min-width: 200px;
    }
    
    .nav-actions {
        flex-shrink: 0;
    }
    
    .nav-actions .nav-btn {
        width: 36px;
        height: 36px;
        font-size: var(--font-sm);
    }
    
    .performance-indicators {
        top: var(--spacing-md);
        right: var(--spacing-md);
        scale: 0.8;
        flex-direction: row;
        gap: var(--spacing-xs);
        max-width: calc(100% - 2rem);
        flex-wrap: wrap;
    }
    
    .indicator {
        min-width: 80px;
        padding: var(--spacing-xs);
        flex: 1;
    }
    
    .layer-labels {
        display: none; /* 在移动端隐藏层级标签以减少混乱 */
    }
    
    .scene-overlay {
        touch-action: manipulation;
    }
    
    .status-bar {
        flex-direction: column;
        gap: var(--spacing-sm);
        text-align: center;
        padding: var(--spacing-sm);
    }
    
    .status-group {
        gap: var(--spacing-sm);
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .status-item {
        font-size: var(--font-xs);
    }
    
    .modal-container {
        margin: var(--spacing-md);
        width: calc(100% - 2rem);
        max-height: calc(100vh - 2rem);
    }
    
    .modal-header,
    .modal-body,
    .modal-footer {
        padding: var(--spacing-md);
    }
    
    .toast-notification {
        left: var(--spacing-md);
        right: var(--spacing-md);
        max-width: none;
        width: calc(100% - 2rem);
    }
    
    .advanced-hints {
        left: var(--spacing-md);
        right: var(--spacing-md);
        bottom: var(--spacing-md);
        max-width: none;
        width: calc(100% - 2rem);
    }
}

@media (max-width: 480px) {
    :root {
        --font-base: 0.8rem;
        --font-sm: 0.7rem;
        --font-xs: 0.6rem;
        --spacing-sm: 0.375rem;
        --spacing-md: 0.5rem;
        --spacing-lg: 0.75rem;
        --spacing-xl: 1rem;
    }
    
    .startup-content {
        padding: var(--spacing-md);
    }
    
    .startup-title {
        font-size: clamp(1.5rem, 8vw, 2rem);
    }
    
    .startup-subtitle {
        font-size: clamp(0.875rem, 4vw, 1rem);
    }
    
    .control-panel {
        gap: var(--spacing-sm);
        padding: var(--spacing-sm);
    }
    
    .section-title {
        font-size: var(--font-sm);
        margin-bottom: var(--spacing-sm);
    }
    
    .view-btn, .sim-btn {
        min-width: 100px;
        padding: var(--spacing-xs) var(--spacing-sm);
        font-size: var(--font-xs);
    }
    
    .view-btn span, .sim-btn span {
        display: none; /* 在小屏幕上只显示图标 */
    }
    
    .interaction-mode {
        min-width: 50px;
        padding: var(--spacing-xs);
    }
    
    .interaction-mode span {
        font-size: var(--font-xs);
    }
    
    .performance-indicators {
        position: static;
        margin: var(--spacing-sm) 0;
        background: var(--glass-bg);
        border: 1px solid var(--border-color);
        border-radius: 12px;
        padding: var(--spacing-sm);
    }
    
    .indicator {
        min-width: 70px;
        font-size: var(--font-xs);
    }
    
    .indicator-label {
        display: none; /* 在小屏幕上只显示数值 */
    }
    
    .brand-tagline {
        display: none;
    }
    
    .nav-actions .nav-btn {
        width: 32px;
        height: 32px;
    }
    
    .panel-tabs {
        gap: 2px;
        padding: 2px;
    }
    
    .tab-btn {
        min-height: 36px;
        font-size: var(--font-xs);
        padding: var(--spacing-xs);
    }
    
    .tab-btn i {
        font-size: var(--font-sm);
    }
}

/* === 触摸设备优化 === */
@media (hover: none) and (pointer: coarse) {
    .view-btn, .sim-btn, .tab-btn, .interaction-mode, .nav-btn {
        min-height: 44px; /* iOS建议的最小触摸目标 */
        min-width: 44px;
    }
    
    .layer-label {
        min-height: 44px;
        padding: var(--spacing-md);
    }
    
    .modal-btn {
        min-height: 44px;
        padding: var(--spacing-md) var(--spacing-lg);
    }
    
    /* 移除悬停效果，使用触摸反馈 */
    .view-btn:hover, .sim-btn:hover, .tab-btn:hover,
    .interaction-mode:hover, .nav-btn:hover {
        transform: none;
    }
    
    .view-btn:active, .sim-btn:active, .tab-btn:active,
    .interaction-mode:active, .nav-btn:active {
        transform: scale(0.95);
        transition: transform 0.1s ease;
    }
    
    /* 优化滚动性能 */
    .panel-content, .modal-body {
        -webkit-overflow-scrolling: touch;
        overscroll-behavior: contain;
    }
}

/* === 高对比度和辅助功能 === */
@media (prefers-contrast: high) {
    :root {
        --border-color: rgba(255, 255, 255, 0.6);
        --glass-bg: rgba(255, 255, 255, 0.2);
        --text-muted: #e2e8f0;
        --bg-hover: rgba(255, 255, 255, 0.25);
    }
    
    .view-btn, .sim-btn, .tab-btn {
        border-width: 2px;
    }
    
    .layer-label {
        border-width: 2px;
    }
}

/* === 减少动画（用户偏好） === */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
    
    .particles,
    .background-particles,
    .helix-strand {
        animation: none !important;
    }
}

/* === 深色模式检测 === */
@media (prefers-color-scheme: light) {
    :root {
        --bg-primary: radial-gradient(ellipse at center, #f8fafc 0%, #e2e8f0 100%);
        --text-primary: #1e293b;
        --text-secondary: #475569;
        --text-muted: #64748b;
        --border-color: rgba(0, 0, 0, 0.1);
        --glass-bg: rgba(255, 255, 255, 0.8);
    }
}

/* === 交互反馈增强 === */
.feedback-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 9998;
}

.ripple-effect {
    position: absolute;
    border-radius: 50%;
    background: radial-gradient(circle, rgba(79, 172, 254, 0.4), transparent);
    transform: scale(0);
    animation: rippleExpand 0.6s ease-out;
    pointer-events: none;
}

@keyframes rippleExpand {
    to {
        transform: scale(4);
        opacity: 0;
    }
}

.toast-notification {
    position: fixed;
    top: var(--spacing-xl);
    right: var(--spacing-xl);
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: var(--spacing-md) var(--spacing-lg);
    color: var(--text-primary);
    box-shadow: var(--glass-shadow), 0 0 20px rgba(79, 172, 254, 0.2);
    transform: translateX(400px);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 9999;
    max-width: 300px;
    min-width: 250px;
}

.toast-notification.show {
    transform: translateX(0);
}

.toast-notification.success {
    border-left: 4px solid var(--success-color);
}

.toast-notification.warning {
    border-left: 4px solid var(--warning-color);
}

.toast-notification.error {
    border-left: 4px solid var(--danger-color);
}

.toast-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-xs);
    font-weight: 600;
}

.toast-body {
    font-size: var(--font-sm);
    color: var(--text-secondary);
    line-height: 1.4;
}

.toast-close {
    position: absolute;
    top: var(--spacing-sm);
    right: var(--spacing-sm);
    width: 20px;
    height: 20px;
    border: none;
    background: transparent;
    color: var(--text-muted);
    cursor: pointer;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    transition: all 0.2s ease;
}

.toast-close:hover {
    background: var(--bg-hover);
    color: var(--text-primary);
}

/* === 上下文菜单 === */
.context-menu {
    position: fixed;
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: var(--spacing-sm);
    box-shadow: var(--glass-shadow), 0 0 30px rgba(0, 0, 0, 0.3);
    z-index: 9999;
    min-width: 200px;
    opacity: 0;
    transform: scale(0.8) translateY(10px);
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    pointer-events: none;
}

.context-menu.active {
    opacity: 1;
    transform: scale(1) translateY(0);
    pointer-events: auto;
}

.context-menu-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-sm) var(--spacing-md);
    color: var(--text-secondary);
    cursor: pointer;
    border-radius: 8px;
    transition: all 0.2s ease;
    font-size: var(--font-sm);
}

.context-menu-item:hover {
    background: var(--bg-hover);
    color: var(--text-primary);
    transform: translateX(4px);
}

.context-menu-item.danger:hover {
    background: rgba(239, 68, 68, 0.1);
    color: var(--danger-color);
}

.context-menu-divider {
    height: 1px;
    background: var(--border-color);
    margin: var(--spacing-sm) 0;
}

/* === 工具提示增强 === */
.tooltip {
    position: absolute;
    background: var(--bg-modal);
    color: var(--text-primary);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: 8px;
    font-size: var(--font-xs);
    line-height: 1.4;
    max-width: 200px;
    box-shadow: var(--shadow-large);
    z-index: 9999;
    opacity: 0;
    transform: translateY(8px);
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    pointer-events: none;
    border: 1px solid var(--border-color);
    backdrop-filter: blur(15px);
}

.tooltip.show {
    opacity: 1;
    transform: translateY(0);
}

.tooltip::before {
    content: '';
    position: absolute;
    top: -5px;
    left: 50%;
    transform: translateX(-50%);
    width: 10px;
    height: 10px;
    background: var(--bg-modal);
    border: 1px solid var(--border-color);
    border-bottom: none;
    border-right: none;
    transform: translateX(-50%) rotate(45deg);
}

/* === 键盘快捷键显示 === */
.keyboard-shortcuts {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: var(--glass-bg);
    backdrop-filter: blur(25px);
    border: 1px solid var(--border-color);
    border-radius: 20px;
    padding: var(--spacing-2xl);
    max-width: 600px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: var(--glass-shadow), 0 0 50px rgba(0, 0, 0, 0.5);
    z-index: 9999;
    opacity: 0;
    visibility: hidden;
    transform: translate(-50%, -50%) scale(0.8);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.keyboard-shortcuts.active {
    opacity: 1;
    visibility: visible;
    transform: translate(-50%, -50%) scale(1);
}

.shortcuts-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-xl);
    padding-bottom: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
}

.shortcuts-title {
    font-size: var(--font-2xl);
    font-weight: 700;
    color: var(--text-primary);
}

.shortcuts-close {
    width: 40px;
    height: 40px;
    border: none;
    background: transparent;
    color: var(--text-muted);
    cursor: pointer;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.shortcuts-close:hover {
    background: var(--bg-hover);
    color: var(--text-primary);
    transform: scale(1.1);
}

.shortcuts-grid {
    display: grid;
    gap: var(--spacing-lg);
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}

.shortcuts-category {
    background: rgba(255, 255, 255, 0.03);
    border-radius: 12px;
    padding: var(--spacing-lg);
}

.category-title {
    font-size: var(--font-lg);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.shortcut-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-sm) 0;
    border-bottom: 1px solid var(--border-color);
}

.shortcut-item:last-child {
    border-bottom: none;
}

.shortcut-desc {
    font-size: var(--font-sm);
    color: var(--text-secondary);
}

.shortcut-keys {
    display: flex;
    gap: var(--spacing-xs);
}

.key {
    background: var(--bg-secondary);
    color: var(--text-primary);
    padding: 2px 8px;
    border-radius: 6px;
    font-size: var(--font-xs);
    font-family: 'JetBrains Mono', monospace;
    border: 1px solid var(--border-color);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    min-width: 24px;
    text-align: center;
}

/* === 主题选择器 === */
.theme-selector {
    padding: var(--spacing-lg);
}

.theme-selector h3 {
    font-size: var(--font-xl);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-lg);
    text-align: center;
}

.theme-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-xl);
}

.theme-option {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    background: var(--glass-bg);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    backdrop-filter: blur(10px);
}

.theme-option:hover {
    background: var(--bg-hover);
    border-color: var(--border-glow);
    transform: translateY(-2px) scale(1.02);
    box-shadow: var(--shadow-medium);
}

.theme-option.active {
    border-color: var(--accent-color);
    background: linear-gradient(135deg, rgba(245, 158, 11, 0.1), var(--glass-bg));
    box-shadow: 0 0 20px rgba(245, 158, 11, 0.2);
}

.theme-option.active::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 12px;
    background: linear-gradient(135deg, transparent, rgba(245, 158, 11, 0.05), transparent);
    pointer-events: none;
}

.theme-preview {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    position: relative;
    overflow: hidden;
    flex-shrink: 0;
}

.theme-preview::after {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    right: 2px;
    bottom: 2px;
    border-radius: 50%;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.3), transparent);
}

.theme-info {
    flex: 1;
    min-width: 0;
}

.theme-name {
    font-weight: 600;
    color: var(--text-primary);
    font-size: var(--font-base);
    margin-bottom: var(--spacing-xs);
}

.theme-desc {
    font-size: var(--font-sm);
    color: var(--text-muted);
    line-height: 1.4;
}

.theme-option i {
    color: var(--accent-color);
    font-size: var(--font-lg);
    margin-left: auto;
}

.theme-actions {
    display: flex;
    gap: var(--spacing-md);
    justify-content: center;
    margin-bottom: var(--spacing-lg);
}

.theme-btn {
    padding: var(--spacing-sm) var(--spacing-lg);
    background: var(--glass-bg);
    border: 1px solid var(--border-color);
    border-radius: 10px;
    color: var(--text-secondary);
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: var(--font-sm);
    backdrop-filter: blur(10px);
}

.theme-btn:hover {
    background: var(--bg-hover);
    border-color: var(--border-glow);
    color: var(--text-primary);
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.theme-shortcuts {
    text-align: center;
    padding: var(--spacing-md);
    background: rgba(255, 255, 255, 0.03);
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

.theme-shortcuts p {
    font-size: var(--font-sm);
    color: var(--text-muted);
    margin: 0;
}

.theme-shortcuts kbd {
    background: var(--bg-secondary);
    color: var(--text-primary);
    padding: 2px 6px;
    border-radius: 4px;
    font-size: var(--font-xs);
    font-family: 'JetBrains Mono', monospace;
    border: 1px solid var(--border-color);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    margin: 0 2px;
}

/* === 主题变体支持 === */
.theme-light {
    --bg-primary: radial-gradient(ellipse at center, #f8fafc 0%, #e2e8f0 100%);
    --bg-secondary: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 50%, #94a3b8 100%);
    --text-primary: #1e293b;
    --text-secondary: #475569;
    --text-muted: #64748b;
    --border-color: rgba(0, 0, 0, 0.1);
    --glass-bg: rgba(255, 255, 255, 0.8);
    --bg-hover: rgba(0, 0, 0, 0.05);
}

.theme-ocean {
    --bg-primary: radial-gradient(ellipse at center, #0f172a 0%, #1e293b 100%);
    --bg-secondary: linear-gradient(135deg, #1e293b 0%, #334155 50%, #475569 100%);
    --primary-gradient: linear-gradient(135deg, #06b6d4 0%, #0891b2 25%, #0e7490 50%, #155e75 75%, #164e63 100%);
    --neon-blue: linear-gradient(45deg, #06b6d4, #67e8f9);
    --accent-color: #06b6d4;
}

.theme-forest {
    --bg-primary: radial-gradient(ellipse at center, #14532d 0%, #166534 100%);
    --bg-secondary: linear-gradient(135deg, #166534 0%, #15803d 50%, #16a34a 100%);
    --primary-gradient: linear-gradient(135deg, #22c55e 0%, #16a34a 25%, #15803d 50%, #166534 75%, #14532d 100%);
    --neon-blue: linear-gradient(45deg, #22c55e, #4ade80);
    --accent-color: #22c55e;
}

.theme-sunset {
    --bg-primary: radial-gradient(ellipse at center, #431407 0%, #7c2d12 100%);
    --bg-secondary: linear-gradient(135deg, #7c2d12 0%, #c2410c 50%, #ea580c 100%);
    --primary-gradient: linear-gradient(135deg, #f97316 0%, #ea580c 25%, #dc2626 50%, #b91c1c 75%, #991b1b 100%);
    --neon-blue: linear-gradient(45deg, #f97316, #fb923c);
    --accent-color: #f97316;
}

/* === 实用工具类 === */
.hidden {
    display: none !important;
}

.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

.fade-in {
    animation: fadeIn 0.5s ease-in;
}

.fade-out {
    animation: fadeOut 0.5s ease-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes fadeOut {
    from { opacity: 1; transform: translateY(0); }
    to { opacity: 0; transform: translateY(-20px); }
}

.slide-in-left {
    animation: slideInLeft 0.4s ease-out;
}

.slide-in-right {
    animation: slideInRight 0.4s ease-out;
}

@keyframes slideInLeft {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}

@keyframes slideInRight {
    from { transform: translateX(100%); }
    to { transform: translateX(0); }
}

.scale-in {
    animation: scaleIn 0.3s ease-out;
}

@keyframes scaleIn {
    from { transform: scale(0.8); opacity: 0; }
    to { transform: scale(1); opacity: 1; }
}

/* === 滚动条样式 === */
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--border-hover);
}

/* === 场景覆盖层样式增强 === */
.scene-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 10;
    background: radial-gradient(circle at 30% 70%, rgba(79, 172, 254, 0.02) 0%, transparent 50%),
                radial-gradient(circle at 70% 30%, rgba(230, 179, 255, 0.02) 0%, transparent 50%);
}

.scene-overlay > * {
    pointer-events: auto;
}

.scene-overlay::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: conic-gradient(from 45deg at 50% 50%, 
        transparent 0deg, 
        rgba(79, 172, 254, 0.01) 60deg, 
        transparent 120deg,
        rgba(230, 179, 255, 0.01) 180deg,
        transparent 240deg,
        rgba(0, 229, 255, 0.01) 300deg,
        transparent 360deg);
    animation: overlayRotate 60s linear infinite;
    pointer-events: none;
}

@keyframes overlayRotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* === 数据轨迹增强 === */
.data-trail {
    position: absolute;
    width: 100%;
    height: 100%;
    pointer-events: none;
    opacity: 0;
    transition: opacity 0.5s ease;
}

.data-trail.active {
    opacity: 1;
}

.data-packet-3d {
    position: absolute;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: var(--neon-green);
    box-shadow: 0 0 20px rgba(17, 153, 142, 0.6);
    animation: packetPulse 2s ease-in-out infinite;
}

.packet-core {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: var(--bg-primary);
    font-size: 10px;
    animation: coreRotate 3s linear infinite;
}

.packet-aura {
    position: absolute;
    top: -10px;
    left: -10px;
    width: 40px;
    height: 40px;
    border: 2px solid rgba(17, 153, 142, 0.3);
    border-radius: 50%;
    animation: auraExpand 2s ease-in-out infinite;
}

@keyframes packetPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.2); }
}

@keyframes coreRotate {
    from { transform: translate(-50%, -50%) rotate(0deg); }
    to { transform: translate(-50%, -50%) rotate(360deg); }
}

@keyframes auraExpand {
    0%, 100% { transform: scale(1); opacity: 0.6; }
    50% { transform: scale(1.5); opacity: 0.2; }
}

/* === 模态框增强 === */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(10, 11, 30, 0.95);
    backdrop-filter: blur(20px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    opacity: 0;
    visibility: hidden;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.modal-overlay.active {
    opacity: 1;
    visibility: visible;
}

.modal-container {
    background: var(--glass-bg);
    backdrop-filter: blur(25px);
    border: 1px solid var(--border-color);
    border-radius: 20px;
    max-width: 600px;
    width: 90%;
    max-height: 80vh;
    overflow: hidden;
    box-shadow: var(--glass-shadow), 0 0 40px rgba(79, 172, 254, 0.2);
    transform: scale(0.8) translateY(40px);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
}

.modal-overlay.active .modal-container {
    transform: scale(1) translateY(0);
}

.modal-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--primary-gradient);
    animation: modalGlow 3s ease-in-out infinite alternate;
}

@keyframes modalGlow {
    0% { opacity: 0.6; }
    100% { opacity: 1; box-shadow: 0 0 20px rgba(79, 172, 254, 0.5); }
}

.modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-xl);
    border-bottom: 1px solid var(--border-color);
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.05), transparent);
}

.modal-title-section {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.modal-icon {
    font-size: var(--font-xl);
    color: var(--hologram-cyan);
    animation: iconPulse 2s ease-in-out infinite;
}

@keyframes iconPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); filter: brightness(1.3); }
}

.modal-close {
    width: 40px;
    height: 40px;
    border: none;
    background: transparent;
    color: var(--text-muted);
    cursor: pointer;
    border-radius: 50%;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-close:hover {
    background: var(--bg-hover);
    color: var(--text-primary);
    transform: scale(1.1);
}

.modal-body {
    padding: var(--spacing-xl);
    max-height: 60vh;
    overflow-y: auto;
}

.modal-footer {
    display: flex;
    gap: var(--spacing-md);
    padding: var(--spacing-xl);
    border-top: 1px solid var(--border-color);
    background: linear-gradient(135deg, transparent, rgba(255, 255, 255, 0.02));
}

.modal-btn {
    padding: var(--spacing-md) var(--spacing-xl);
    border: 1px solid var(--border-color);
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    font-weight: 600;
    position: relative;
    overflow: hidden;
}

.modal-btn.primary {
    background: var(--primary-gradient);
    border-color: transparent;
    color: white;
    box-shadow: var(--shadow-glow);
}

.modal-btn.secondary {
    background: var(--glass-bg);
    color: var(--text-secondary);
}

.modal-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

/* === 高级提示系统 === */
.advanced-hints {
    position: fixed;
    bottom: var(--spacing-xl);
    left: var(--spacing-xl);
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--border-color);
    border-radius: 16px;
    max-width: 320px;
    box-shadow: var(--glass-shadow);
    transform: translateY(100px);
    opacity: 0;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 1000;
}

.advanced-hints.visible {
    transform: translateY(0);
    opacity: 1;
}

.hints-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-md) var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.05), transparent);
}

.hints-toggle {
    width: 32px;
    height: 32px;
    border: none;
    background: transparent;
    color: var(--text-muted);
    cursor: pointer;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.hints-toggle:hover {
    background: var(--bg-hover);
    color: var(--text-primary);
    transform: scale(1.1);
}

.hints-content {
    padding: var(--spacing-lg);
    max-height: 300px;
    overflow-y: auto;
}

.hint-category {
    margin-bottom: var(--spacing-lg);
}

.hint-category h5 {
    font-size: var(--font-sm);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-md);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.hint-items {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.hint-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-sm);
    background: rgba(255, 255, 255, 0.03);
    border-radius: 8px;
    transition: all 0.3s ease;
}

.hint-item:hover {
    background: rgba(255, 255, 255, 0.08);
    transform: translateX(4px);
}

.hint-item kbd {
    background: var(--bg-secondary);
    color: var(--text-primary);
    padding: 2px 6px;
    border-radius: 4px;
    font-size: var(--font-xs);
    font-family: 'JetBrains Mono', monospace;
    border: 1px solid var(--border-color);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}