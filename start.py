#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Telegram 频道下载器启动脚本
简化版本，方便快速使用
"""

import os
import sys
import asyncio
import logging
from pathlib import Path

# 检查配置文件
if os.path.exists('config.py'):
    from config import *
    print("✅ 使用 config.py 配置文件")
else:
    print("⚠️  未找到 config.py，使用默认配置")
    print("💡 建议复制 config_example.py 为 config.py 并修改配置")
    
    # 默认配置
    API_ID = input("请输入 API_ID: ").strip()
    API_HASH = input("请输入 API_HASH: ").strip()
    BOT_TOKEN = input("请输入 BOT_TOKEN: ").strip()
    ADMIN_ID = int(input("请输入 ADMIN_ID: ").strip())
    
    # 其他默认配置
    DOWNLOAD_PATH = './downloads'
    MAX_CONCURRENT = 10
    RETRY_TIMES = 3
    RETRY_DELAY = 5
    SKIP_STICKERS = True
    SKIP_GIFS = True
    SKIP_ANIMATIONS = True
    MAX_FILE_SIZE = 2 * 1024 * 1024 * 1024
    ENABLE_GDRIVE_UPLOAD = False

# 导入主程序
try:
    from telegram_channel_downloader import main
    
    if __name__ == '__main__':
        print("🚀 启动 Telegram 频道下载器...")
        print(f"📁 下载路径: {os.path.abspath(DOWNLOAD_PATH)}")
        
        try:
            asyncio.run(main())
        except KeyboardInterrupt:
            print("\n👋 程序已停止")
        except Exception as e:
            print(f"❌ 程序异常: {e}")
            
except ImportError as e:
    print(f"❌ 导入失败: {e}")
    print("💡 请确保已安装所有依赖: pip3 install -r requirements.txt")
