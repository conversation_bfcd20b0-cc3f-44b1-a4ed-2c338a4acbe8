// 交互控制系统
class InteractionSystem {
    constructor(simulator) {
        this.simulator = simulator;
        this.currentMode = 'touch';
        this.isVoiceEnabled = false;
        this.gestureRecognizer = null;
        this.voiceCommands = {};
        
        // 手势识别配置
        this.gestureConfig = {
            swipeThreshold: 50,
            pinchThreshold: 0.1,
            rotateThreshold: 15
        };
        
        // 语音命令配置
        this.voiceCommands = {
            '聚焦第一层': () => this.simulator.focusOnLayer(1),
            '聚焦第二层': () => this.simulator.focusOnLayer(2),
            '聚焦第三层': () => this.simulator.focusOnLayer(3),
            '聚焦第四层': () => this.simulator.focusOnLayer(4),
            '聚焦第五层': () => this.simulator.focusOnLayer(5),
            '聚焦第六层': () => this.simulator.focusOnLayer(6),
            '聚焦第七层': () => this.simulator.focusOnLayer(7),
            '启动仿真': () => this.simulator.toggleSimulation(),
            '停止仿真': () => this.simulator.resetSimulation(),
            '重置视角': () => this.simulator.updateCameraPosition(),
            '全屏模式': () => this.toggleFullscreen(),
            '退出全屏': () => this.exitFullscreen()
        };
        
        this.init();
    }
    
    init() {
        this.setupTouchControls();
        this.setupKeyboardControls();
        this.setupMouseControls();
        this.initializeVoiceRecognition();
        this.setupGestureRecognition();
        
        console.log('🎮 交互系统初始化完成');
    }
    
    setupTouchControls() {
        const canvas = this.simulator.renderer.domElement;
        let touchState = {
            touches: [],
            lastDistance: 0,
            lastAngle: 0,
            isRotating: false,
            isPinching: false
        };
        
        canvas.addEventListener('touchstart', (e) => {
            e.preventDefault();
            touchState.touches = Array.from(e.touches);
            
            if (e.touches.length === 2) {
                const touch1 = e.touches[0];
                const touch2 = e.touches[1];
                
                touchState.lastDistance = this.getTouchDistance(touch1, touch2);
                touchState.lastAngle = this.getTouchAngle(touch1, touch2);
                touchState.isPinching = true;
            }
        });
        
        canvas.addEventListener('touchmove', (e) => {
            e.preventDefault();
            
            if (e.touches.length === 1) {
                // 单指旋转
                this.handleSingleTouchRotation(e.touches[0], touchState);
            } else if (e.touches.length === 2) {
                // 双指缩放和旋转
                this.handleMultiTouchGestures(e.touches, touchState);
            }
        });
        
        canvas.addEventListener('touchend', (e) => {
            e.preventDefault();
            touchState.touches = Array.from(e.touches);
            touchState.isPinching = false;
            touchState.isRotating = false;
        });
    }
    
    handleSingleTouchRotation(touch, touchState) {
        if (touchState.touches.length > 0) {
            const lastTouch = touchState.touches[0];
            const deltaX = touch.clientX - lastTouch.clientX;
            const deltaY = touch.clientY - lastTouch.clientY;
            
            // 旋转相机
            this.rotateCamera(deltaX * 0.01, deltaY * 0.01);
        }
        
        touchState.touches = [touch];
    }
    
    handleMultiTouchGestures(touches, touchState) {
        const touch1 = touches[0];
        const touch2 = touches[1];
        
        const currentDistance = this.getTouchDistance(touch1, touch2);
        const currentAngle = this.getTouchAngle(touch1, touch2);
        
        // 缩放手势
        if (touchState.isPinching) {
            const scale = currentDistance / touchState.lastDistance;
            this.zoomCamera(scale);
        }
        
        // 旋转手势
        const angleDiff = currentAngle - touchState.lastAngle;
        if (Math.abs(angleDiff) > this.gestureConfig.rotateThreshold) {
            this.rotateCamera(0, 0, angleDiff * 0.01);
        }
        
        touchState.lastDistance = currentDistance;
        touchState.lastAngle = currentAngle;
    }
    
    getTouchDistance(touch1, touch2) {
        const dx = touch1.clientX - touch2.clientX;
        const dy = touch1.clientY - touch2.clientY;
        return Math.sqrt(dx * dx + dy * dy);
    }
    
    getTouchAngle(touch1, touch2) {
        return Math.atan2(touch2.clientY - touch1.clientY, touch2.clientX - touch1.clientX) * 180 / Math.PI;
    }
    
    setupKeyboardControls() {
        document.addEventListener('keydown', (e) => {
            switch (e.code) {
                case 'Space':
                    e.preventDefault();
                    this.simulator.toggleSimulation();
                    break;
                case 'KeyR':
                    e.preventDefault();
                    this.simulator.resetSimulation();
                    break;
                case 'KeyF':
                    e.preventDefault();
                    this.toggleFullscreen();
                    break;
                case 'Digit1':
                case 'Digit2':
                case 'Digit3':
                case 'Digit4':
                case 'Digit5':
                case 'Digit6':
                case 'Digit7':
                    e.preventDefault();
                    const layer = parseInt(e.code.slice(-1));
                    this.simulator.focusOnLayer(layer);
                    break;
                case 'ArrowUp':
                    e.preventDefault();
                    this.moveCamera(0, 1, 0);
                    break;
                case 'ArrowDown':
                    e.preventDefault();
                    this.moveCamera(0, -1, 0);
                    break;
                case 'ArrowLeft':
                    e.preventDefault();
                    this.rotateCamera(-0.1, 0);
                    break;
                case 'ArrowRight':
                    e.preventDefault();
                    this.rotateCamera(0.1, 0);
                    break;
            }
        });
    }
    
    setupMouseControls() {
        const canvas = this.simulator.renderer.domElement;
        let mouseState = {
            isDown: false,
            lastX: 0,
            lastY: 0
        };
        
        canvas.addEventListener('mousedown', (e) => {
            mouseState.isDown = true;
            mouseState.lastX = e.clientX;
            mouseState.lastY = e.clientY;
            canvas.style.cursor = 'grabbing';
        });
        
        canvas.addEventListener('mousemove', (e) => {
            if (!mouseState.isDown) return;
            
            const deltaX = e.clientX - mouseState.lastX;
            const deltaY = e.clientY - mouseState.lastY;
            
            if (e.shiftKey) {
                // Shift + 拖拽 = 平移
                this.moveCamera(deltaX * 0.1, -deltaY * 0.1, 0);
            } else {
                // 普通拖拽 = 旋转
                this.rotateCamera(deltaX * 0.01, deltaY * 0.01);
            }
            
            mouseState.lastX = e.clientX;
            mouseState.lastY = e.clientY;
        });
        
        canvas.addEventListener('mouseup', () => {
            mouseState.isDown = false;
            canvas.style.cursor = 'grab';
        });
        
        canvas.addEventListener('wheel', (e) => {
            e.preventDefault();
            const scale = e.deltaY > 0 ? 1.1 : 0.9;
            this.zoomCamera(scale);
        });
        
        // 双击聚焦
        canvas.addEventListener('dblclick', (e) => {
            this.handleDoubleClick(e);
        });
    }
    
    initializeVoiceRecognition() {
        if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
            const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
            this.speechRecognition = new SpeechRecognition();
            
            this.speechRecognition.continuous = true;
            this.speechRecognition.interimResults = false;
            this.speechRecognition.lang = 'zh-CN';
            
            this.speechRecognition.onresult = (event) => {
                const command = event.results[event.results.length - 1][0].transcript.trim();
                this.processVoiceCommand(command);
            };
            
            this.speechRecognition.onerror = (event) => {
                console.warn('语音识别错误:', event.error);
            };
            
            console.log('🎤 语音识别系统已准备');
        } else {
            console.warn('⚠️ 浏览器不支持语音识别');
        }
    }
    
    setupGestureRecognition() {
        // 简单的手势识别实现
        const canvas = this.simulator.renderer.domElement;
        let gestureState = {
            startX: 0,
            startY: 0,
            endX: 0,
            endY: 0,
            isTracking: false
        };
        
        canvas.addEventListener('mousedown', (e) => {
            if (e.ctrlKey) { // Ctrl + 鼠标 = 手势模式
                gestureState.isTracking = true;
                gestureState.startX = e.clientX;
                gestureState.startY = e.clientY;
            }
        });
        
        canvas.addEventListener('mouseup', (e) => {
            if (gestureState.isTracking) {
                gestureState.endX = e.clientX;
                gestureState.endY = e.clientY;
                this.processGesture(gestureState);
                gestureState.isTracking = false;
            }
        });
    }
    
    // 相机控制方法
    rotateCamera(deltaX, deltaY, deltaZ = 0) {
        const camera = this.simulator.camera;
        const target = new THREE.Vector3(0, 10, 0);
        
        // 计算相机围绕目标点的旋转
        const spherical = new THREE.Spherical();
        spherical.setFromVector3(camera.position.clone().sub(target));
        
        spherical.theta -= deltaX;
        spherical.phi += deltaY;
        spherical.phi = Math.max(0.1, Math.min(Math.PI - 0.1, spherical.phi));
        
        camera.position.setFromSpherical(spherical).add(target);
        camera.lookAt(target);
    }
    
    zoomCamera(scale) {
        const camera = this.simulator.camera;
        const target = new THREE.Vector3(0, 10, 0);
        const direction = camera.position.clone().sub(target);
        
        direction.multiplyScalar(scale);
        camera.position.copy(target).add(direction);
        
        // 限制缩放范围
        const distance = camera.position.distanceTo(target);
        if (distance < 10) {
            camera.position.copy(target).add(direction.normalize().multiplyScalar(10));
        } else if (distance > 100) {
            camera.position.copy(target).add(direction.normalize().multiplyScalar(100));
        }
    }
    
    moveCamera(deltaX, deltaY, deltaZ) {
        const camera = this.simulator.camera;
        const right = new THREE.Vector3();
        const up = new THREE.Vector3(0, 1, 0);
        
        camera.getWorldDirection(right);
        right.cross(up).normalize();
        
        camera.position.add(right.multiplyScalar(deltaX));
        camera.position.add(up.multiplyScalar(deltaY));
    }
    
    // 语音命令处理
    processVoiceCommand(command) {
        console.log('🎤 语音命令:', command);
        
        // 查找匹配的命令
        for (const [key, action] of Object.entries(this.voiceCommands)) {
            if (command.includes(key)) {
                action();
                this.showVoiceCommandFeedback(key);
                return;
            }
        }
        
        console.log('未识别的语音命令:', command);
    }
    
    showVoiceCommandFeedback(command) {
        // 显示语音命令反馈
        const feedback = document.createElement('div');
        feedback.className = 'voice-feedback';
        feedback.textContent = `✓ ${command}`;
        feedback.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(79, 172, 254, 0.9);
            color: white;
            padding: 10px 20px;
            border-radius: 20px;
            font-size: 14px;
            z-index: 10000;
            animation: fadeInOut 2s ease-in-out;
        `;
        
        document.body.appendChild(feedback);
        
        setTimeout(() => {
            document.body.removeChild(feedback);
        }, 2000);
    }
    
    // 手势处理
    processGesture(gestureState) {
        const deltaX = gestureState.endX - gestureState.startX;
        const deltaY = gestureState.endY - gestureState.startY;
        const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);
        
        if (distance < this.gestureConfig.swipeThreshold) return;
        
        const angle = Math.atan2(deltaY, deltaX) * 180 / Math.PI;
        
        // 根据手势方向执行不同操作
        if (angle > -45 && angle < 45) {
            // 右滑 - 下一个视角
            this.simulator.switchView('next');
        } else if (angle > 45 && angle < 135) {
            // 下滑 - 重置视角
            this.simulator.updateCameraPosition();
        } else if (angle > 135 || angle < -135) {
            // 左滑 - 上一个视角
            this.simulator.switchView('prev');
        } else {
            // 上滑 - 聚焦模式
            this.simulator.toggleFocusMode();
        }
    }
    
    // 双击处理
    handleDoubleClick(event) {
        const raycaster = new THREE.Raycaster();
        const mouse = new THREE.Vector2();
        
        const rect = this.simulator.renderer.domElement.getBoundingClientRect();
        mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1;
        mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1;
        
        raycaster.setFromCamera(mouse, this.simulator.camera);
        const intersects = raycaster.intersectObjects(this.simulator.layerMeshes);
        
        if (intersects.length > 0) {
            const layerNum = intersects[0].object.userData.layerNum;
            this.simulator.focusOnLayer(layerNum);
        }
    }
    
    // 交互模式切换
    switchMode(mode) {
        this.currentMode = mode;
        
        switch (mode) {
            case 'voice':
                this.enableVoiceControl();
                break;
            case 'gesture':
                this.enableGestureControl();
                break;
            case 'touch':
            default:
                this.enableTouchControl();
                break;
        }
        
        console.log(`🎮 切换到${mode}模式`);
    }
    
    enableVoiceControl() {
        if (this.speechRecognition) {
            this.speechRecognition.start();
            this.isVoiceEnabled = true;
        }
    }
    
    disableVoiceControl() {
        if (this.speechRecognition) {
            this.speechRecognition.stop();
            this.isVoiceEnabled = false;
        }
    }
    
    enableGestureControl() {
        // 启用手势控制特定功能
        console.log('手势控制已启用');
    }
    
    enableTouchControl() {
        // 确保触摸控制正常工作
        console.log('触摸控制已启用');
    }
    
    // 全屏控制
    toggleFullscreen() {
        if (!document.fullscreenElement) {
            document.documentElement.requestFullscreen();
        } else {
            document.exitFullscreen();
        }
    }
    
    exitFullscreen() {
        if (document.fullscreenElement) {
            document.exitFullscreen();
        }
    }
}
