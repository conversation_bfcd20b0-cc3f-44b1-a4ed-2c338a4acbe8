<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>布局对比测试 - OSI 3D 生物仿生艺术模拟器</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #0a0b1e 0%, #1a1b2e 100%);
            color: white;
            line-height: 1.5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }
        
        .header {
            text-align: center;
            margin-bottom: 3rem;
        }
        
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 1rem;
            background: linear-gradient(45deg, #4facfe, #ff6b9d);
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        
        .comparison-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
            margin-bottom: 3rem;
        }
        
        .comparison-item {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 1.5rem;
            text-align: center;
        }
        
        .comparison-item h2 {
            font-size: 1.5rem;
            margin-bottom: 1rem;
            color: #4facfe;
        }
        
        .comparison-item p {
            color: #cbd5e1;
            margin-bottom: 1.5rem;
        }
        
        .btn {
            display: inline-block;
            padding: 0.75rem 1.5rem;
            background: #4facfe;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            font-size: 1rem;
        }
        
        .btn:hover {
            background: #3d8bfe;
            transform: translateY(-2px);
        }
        
        .btn.secondary {
            background: transparent;
            border: 1px solid #4facfe;
            color: #4facfe;
        }
        
        .btn.secondary:hover {
            background: #4facfe;
            color: white;
        }
        
        .features {
            margin-bottom: 3rem;
        }
        
        .features h2 {
            text-align: center;
            font-size: 2rem;
            margin-bottom: 2rem;
            color: #4facfe;
        }
        
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
        }
        
        .feature-card {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            padding: 1.5rem;
        }
        
        .feature-card h3 {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 1rem;
            color: #4facfe;
        }
        
        .feature-card ul {
            list-style: none;
            padding: 0;
        }
        
        .feature-card li {
            padding: 0.25rem 0;
            color: #cbd5e1;
        }
        
        .feature-card li::before {
            content: "✓";
            color: #10b981;
            margin-right: 0.5rem;
        }
        
        .device-info {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 2rem;
            text-align: center;
        }
        
        .device-info h3 {
            margin-bottom: 0.5rem;
            color: #4facfe;
        }
        
        .device-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }
        
        .stat-item {
            background: rgba(255, 255, 255, 0.03);
            padding: 0.5rem;
            border-radius: 4px;
        }
        
        .stat-label {
            font-size: 0.875rem;
            color: #94a3b8;
        }
        
        .stat-value {
            font-size: 1.125rem;
            font-weight: 600;
            color: #4facfe;
        }
        
        @media (max-width: 768px) {
            .comparison-grid {
                grid-template-columns: 1fr;
            }
            
            .container {
                padding: 1rem;
            }
            
            .header h1 {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧬 OSI 3D 布局对比测试</h1>
            <p>比较原始布局和重构后的新布局系统</p>
        </div>
        
        <div class="device-info">
            <h3>当前设备信息</h3>
            <div class="device-stats">
                <div class="stat-item">
                    <div class="stat-label">屏幕分辨率</div>
                    <div class="stat-value" id="screen-resolution">-</div>
                </div>
                <div class="stat-item">
                    <div class="stat-label">视窗尺寸</div>
                    <div class="stat-value" id="viewport-size">-</div>
                </div>
                <div class="stat-item">
                    <div class="stat-label">设备类型</div>
                    <div class="stat-value" id="device-type">-</div>
                </div>
                <div class="stat-item">
                    <div class="stat-label">像素比</div>
                    <div class="stat-value" id="pixel-ratio">-</div>
                </div>
            </div>
        </div>
        
        <div class="comparison-grid">
            <div class="comparison-item">
                <h2>🔴 原始布局</h2>
                <p>存在元素重叠、响应式失效、布局混乱等问题</p>
                <a href="index.html" class="btn secondary" target="_blank">
                    <i class="fas fa-external-link-alt"></i>
                    查看原始版本
                </a>
            </div>
            
            <div class="comparison-item">
                <h2>🟢 重构布局</h2>
                <p>使用CSS Grid + Flexbox，完全重新设计的响应式布局系统</p>
                <a href="index-new.html" class="btn" target="_blank">
                    <i class="fas fa-rocket"></i>
                    查看新版本
                </a>
            </div>
        </div>
        
        <div class="features">
            <h2>新布局系统的改进</h2>
            <div class="features-grid">
                <div class="feature-card">
                    <h3>
                        <i class="fas fa-grid-3x3"></i>
                        CSS Grid 布局
                    </h3>
                    <ul>
                        <li>使用现代CSS Grid技术</li>
                        <li>清晰的区域定义</li>
                        <li>灵活的响应式调整</li>
                        <li>消除元素重叠问题</li>
                    </ul>
                </div>
                
                <div class="feature-card">
                    <h3>
                        <i class="fas fa-mobile-alt"></i>
                        响应式优化
                    </h3>
                    <ul>
                        <li>5个主要断点覆盖</li>
                        <li>移动端优先设计</li>
                        <li>横屏模式适配</li>
                        <li>高分辨率屏幕支持</li>
                    </ul>
                </div>
                
                <div class="feature-card">
                    <h3>
                        <i class="fas fa-palette"></i>
                        设计系统
                    </h3>
                    <ul>
                        <li>统一的颜色变量</li>
                        <li>一致的间距系统</li>
                        <li>标准化的字体大小</li>
                        <li>优化的阴影效果</li>
                    </ul>
                </div>
                
                <div class="feature-card">
                    <h3>
                        <i class="fas fa-tachometer-alt"></i>
                        性能优化
                    </h3>
                    <ul>
                        <li>减少CSS重绘</li>
                        <li>优化动画性能</li>
                        <li>简化DOM结构</li>
                        <li>更快的渲染速度</li>
                    </ul>
                </div>
                
                <div class="feature-card">
                    <h3>
                        <i class="fas fa-universal-access"></i>
                        可访问性
                    </h3>
                    <ul>
                        <li>语义化HTML结构</li>
                        <li>键盘导航支持</li>
                        <li>屏幕阅读器友好</li>
                        <li>高对比度模式</li>
                    </ul>
                </div>
                
                <div class="feature-card">
                    <h3>
                        <i class="fas fa-code"></i>
                        代码质量
                    </h3>
                    <ul>
                        <li>模块化CSS架构</li>
                        <li>BEM命名规范</li>
                        <li>减少代码重复</li>
                        <li>更好的可维护性</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div style="text-align: center; margin-top: 3rem;">
            <h2 style="margin-bottom: 1rem; color: #4facfe;">测试建议</h2>
            <p style="margin-bottom: 2rem; color: #cbd5e1;">
                请在不同设备和屏幕尺寸下测试两个版本，体验布局的差异
            </p>
            <div style="display: flex; gap: 1rem; justify-content: center; flex-wrap: wrap;">
                <button class="btn" onclick="window.open('index.html', '_blank')">
                    <i class="fas fa-bug"></i>
                    原始版本（有问题）
                </button>
                <button class="btn" onclick="window.open('index-new.html', '_blank')">
                    <i class="fas fa-check-circle"></i>
                    新版本（已修复）
                </button>
                <button class="btn secondary" onclick="window.open('layout-test.html', '_blank')">
                    <i class="fas fa-flask"></i>
                    布局测试页面
                </button>
            </div>
        </div>
    </div>
    
    <script>
        // 更新设备信息
        function updateDeviceInfo() {
            document.getElementById('screen-resolution').textContent = `${screen.width}×${screen.height}`;
            document.getElementById('viewport-size').textContent = `${window.innerWidth}×${window.innerHeight}`;
            document.getElementById('device-type').textContent = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ? '移动设备' : '桌面设备';
            document.getElementById('pixel-ratio').textContent = window.devicePixelRatio;
        }
        
        // 初始化和监听窗口变化
        updateDeviceInfo();
        window.addEventListener('resize', updateDeviceInfo);
        
        console.log('🔍 布局对比测试页面已加载');
        console.log('📱 当前设备信息:', {
            screen: `${screen.width}×${screen.height}`,
            viewport: `${window.innerWidth}×${window.innerHeight}`,
            devicePixelRatio: window.devicePixelRatio,
            userAgent: navigator.userAgent
        });
    </script>
</body>
</html>
