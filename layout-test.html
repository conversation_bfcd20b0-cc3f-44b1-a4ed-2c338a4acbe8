<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>布局测试 - OSI 3D 生物仿生艺术模拟器</title>
    <link rel="stylesheet" href="style.css">
    <style>
        /* 测试用的临时样式 */
        .test-content {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: var(--spacing-md);
            margin: var(--spacing-sm);
        }
        
        .debug-info {
            position: fixed;
            top: 10px;
            left: 10px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            z-index: 9999;
        }
        
        .layout-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: var(--spacing-md);
            padding: var(--spacing-md);
        }
        
        .test-box {
            background: linear-gradient(45deg, rgba(79, 172, 254, 0.2), rgba(255, 119, 198, 0.2));
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: var(--spacing-lg);
            text-align: center;
            min-height: 100px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
    </style>
</head>
<body>
    <div class="debug-info" id="debug-info">
        屏幕尺寸: <span id="screen-size"></span><br>
        视窗尺寸: <span id="viewport-size"></span><br>
        设备类型: <span id="device-type"></span>
    </div>

    <div class="app-container">
        <!-- 顶部导航栏 -->
        <nav class="top-navigation">
            <div class="nav-brand">
                <div class="brand-icon">
                    <i class="fas fa-dna"></i>
                </div>
                <div class="brand-text">
                    <span class="brand-name">布局测试</span>
                    <span class="brand-tagline">Layout Test</span>
                </div>
            </div>
            
            <div class="nav-actions">
                <button class="nav-btn" title="测试按钮1">
                    <i class="fas fa-expand"></i>
                </button>
                <button class="nav-btn" title="测试按钮2">
                    <i class="fas fa-cog"></i>
                </button>
                <button class="nav-btn" title="测试按钮3">
                    <i class="fas fa-question-circle"></i>
                </button>
            </div>
        </nav>

        <!-- 主控制面板 -->
        <header class="control-panel">
            <div class="panel-section view-section">
                <h3 class="section-title">
                    <i class="fas fa-eye"></i>
                    视角控制
                </h3>
                <div class="view-controls">
                    <button class="view-btn active" data-view="overview">
                        <i class="fas fa-globe-americas"></i>
                        <span>生态总览</span>
                    </button>
                    <button class="view-btn" data-view="building">
                        <i class="fas fa-layer-group"></i>
                        <span>层级聚焦</span>
                    </button>
                    <button class="view-btn" data-view="datacenter">
                        <i class="fas fa-server"></i>
                        <span>数据中心</span>
                    </button>
                </div>
            </div>
            
            <div class="panel-section simulation-section">
                <h3 class="section-title">
                    <i class="fas fa-play-circle"></i>
                    仿生模拟
                </h3>
                <div class="simulation-controls">
                    <button class="sim-btn primary">
                        <i class="fas fa-play"></i>
                        <span>启动生命流</span>
                    </button>
                    <button class="sim-btn secondary">
                        <i class="fas fa-redo"></i>
                        <span>重置生态</span>
                    </button>
                </div>
            </div>
        </header>

        <!-- 主要内容区域 -->
        <main class="main-workspace">
            <!-- 3D场景容器 -->
            <div class="scene-container">
                <div class="test-content">
                    <h2>主场景区域</h2>
                    <p>这里是3D场景的容器区域</p>
                    
                    <div class="layout-grid">
                        <div class="test-box">测试盒子 1</div>
                        <div class="test-box">测试盒子 2</div>
                        <div class="test-box">测试盒子 3</div>
                        <div class="test-box">测试盒子 4</div>
                    </div>
                </div>
            </div>

            <!-- 智能信息面板 -->
            <aside class="info-panel">
                <div class="panel-header">
                    <div class="panel-tabs">
                        <button class="tab-btn active" data-tab="overview">
                            <i class="fas fa-home"></i>
                            概览
                        </button>
                        <button class="tab-btn" data-tab="layers">
                            <i class="fas fa-layer-group"></i>
                            层级
                        </button>
                        <button class="tab-btn" data-tab="analytics">
                            <i class="fas fa-chart-line"></i>
                            分析
                        </button>
                    </div>
                    <button class="panel-toggle" id="panel-toggle">
                        <i class="fas fa-angle-right"></i>
                    </button>
                </div>
                
                <div class="panel-content" id="panel-content">
                    <div class="test-content">
                        <h3>信息面板测试</h3>
                        <p>这里是信息面板的内容区域。</p>
                        
                        <div style="margin-top: 20px;">
                            <h4>测试列表</h4>
                            <ul style="list-style: none; padding: 0;">
                                <li style="padding: 8px; margin: 4px 0; background: rgba(255,255,255,0.05); border-radius: 4px;">项目 1</li>
                                <li style="padding: 8px; margin: 4px 0; background: rgba(255,255,255,0.05); border-radius: 4px;">项目 2</li>
                                <li style="padding: 8px; margin: 4px 0; background: rgba(255,255,255,0.05); border-radius: 4px;">项目 3</li>
                                <li style="padding: 8px; margin: 4px 0; background: rgba(255,255,255,0.05); border-radius: 4px;">项目 4</li>
                                <li style="padding: 8px; margin: 4px 0; background: rgba(255,255,255,0.05); border-radius: 4px;">项目 5</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </aside>
        </main>

        <!-- 底部状态栏 -->
        <footer class="status-bar enhanced">
            <div class="status-left">
                <div class="status-group">
                    <div class="status-item">
                        <i class="fas fa-eye"></i>
                        <span>视角: 测试模式</span>
                    </div>
                </div>
            </div>
            
            <div class="status-center">
                <div class="simulation-status enhanced">
                    <div class="status-indicator">
                        <div class="status-dot"></div>
                        <div class="status-ring"></div>
                    </div>
                    <span class="status-text">布局测试就绪</span>
                </div>
            </div>
            
            <div class="status-right">
                <div class="status-group">
                    <div class="status-item">
                        <i class="fas fa-clock"></i>
                        <span id="current-time">00:00</span>
                    </div>
                </div>
            </div>
        </footer>
    </div>

    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- 布局验证脚本 -->
    <script src="layout-validator.js"></script>

    <script>
        // 调试信息更新
        function updateDebugInfo() {
            const screenSize = `${screen.width}x${screen.height}`;
            const viewportSize = `${window.innerWidth}x${window.innerHeight}`;
            const deviceType = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ? 'Mobile' : 'Desktop';
            
            document.getElementById('screen-size').textContent = screenSize;
            document.getElementById('viewport-size').textContent = viewportSize;
            document.getElementById('device-type').textContent = deviceType;
        }
        
        // 时间更新
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleTimeString('zh-CN', { hour12: false });
            document.getElementById('current-time').textContent = timeString;
        }
        
        // 面板切换功能
        document.getElementById('panel-toggle').addEventListener('click', function() {
            const panel = document.querySelector('.info-panel');
            panel.classList.toggle('collapsed');
            
            const icon = this.querySelector('i');
            if (panel.classList.contains('collapsed')) {
                icon.className = 'fas fa-angle-left';
            } else {
                icon.className = 'fas fa-angle-right';
            }
        });
        
        // 初始化
        updateDebugInfo();
        updateTime();
        
        // 定时更新
        setInterval(updateDebugInfo, 1000);
        setInterval(updateTime, 1000);
        
        // 窗口大小改变时更新调试信息
        window.addEventListener('resize', updateDebugInfo);
        
        console.log('✅ 布局测试页面已加载');
    </script>
</body>
</html>
