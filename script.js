// OSI 3D 生物仿生艺术模拟器 - 现代ES模块版本
import * as THREE from 'three';
import { OrbitControls } from 'three/addons/controls/OrbitControls.js';
import { EffectComposer } from 'three/addons/postprocessing/EffectComposer.js';
import { RenderPass } from 'three/addons/postprocessing/RenderPass.js';
import { UnrealBloomPass } from 'three/addons/postprocessing/UnrealBloomPass.js';

/**
 * 现代OSI 3D模拟器类
 * 使用最新的Web API和Three.js特性
 */
class ModernOSI3DSimulator {
    // 私有字段 - ES2022特性
    #scene = null;
    #camera = null;
    #renderer = null;
    #composer = null;
    #controls = null;
    #animationId = null;
    #isSimulating = false;
    #currentView = 'overview';
    #focusedLayer = null;
    #layerMeshes = [];
    #particleSystems = [];
    #performanceObserver = null;
    
    // 使用Map存储性能数据 - 现代数据结构
    #stats = new Map([
        ['fps', 0],
        ['frameCount', 0],
        ['lastTime', performance.now()],
        ['memory', 0]
    ]);
    
    // 使用现代数据结构存储OSI层级信息
    #layerData = new Map([
        [7, {
            name: "应用层",
            english: "Application",
            color: 0xE6B3FF,
            description: "神经网络中枢",
            protocols: new Set(["HTTP", "HTTPS", "FTP", "SMTP", "DNS"]),
            devices: new Set(["Web服务器", "邮件服务器", "DNS服务器"]),
            bioAnalogy: "大脑皮层"
        }],
        [6, {
            name: "表示层",
            english: "Presentation",
            color: 0xFF6B9D,
            description: "转译生物器官",
            protocols: new Set(["SSL", "TLS", "JPEG", "MPEG", "ASCII"]),
            devices: new Set(["加密设备", "压缩器", "格式转换器"]),
            bioAnalogy: "感觉器官"
        }],
        [5, {
            name: "会话层",
            english: "Session",
            color: 0x00E5FF,
            description: "对话细胞集群",
            protocols: new Set(["NetBIOS", "RPC", "SQL", "NFS"]),
            devices: new Set(["会话管理器", "认证服务器"]),
            bioAnalogy: "神经突触"
        }],
        [4, {
            name: "传输层",
            english: "Transport",
            color: 0x69F0AE,
            description: "循环系统节点",
            protocols: new Set(["TCP", "UDP", "SPX", "SCTP"]),
            devices: new Set(["网关", "防火墙", "代理服务器"]),
            bioAnalogy: "循环系统"
        }],
        [3, {
            name: "网络层",
            english: "Network",
            color: 0xFFD54F,
            description: "导航蜂群",
            protocols: new Set(["IP", "ICMP", "IGMP", "ARP"]),
            devices: new Set(["路由器", "三层交换机"]),
            bioAnalogy: "神经网络"
        }],
        [2, {
            name: "数据链路层",
            english: "Data Link",
            color: 0xFF5722,
            description: "微生物群落",
            protocols: new Set(["Ethernet", "PPP", "Frame Relay"]),
            devices: new Set(["交换机", "网桥", "网卡"]),
            bioAnalogy: "细胞膜"
        }],
        [1, {
            name: "物理层",
            english: "Physical",
            color: 0x9C27B0,
            description: "菌丝网络基质",
            protocols: new Set(["电信号", "光信号", "无线信号"]),
            devices: new Set(["集线器", "中继器", "网线", "光纤"]),
            bioAnalogy: "DNA双螺旋"
        }]
    ]);
    
    constructor() {
        this.init();
    }
    
    // 现代异步初始化模式
    async init() {
        try {
            console.log('🚀 开始初始化现代OSI 3D模拟器...');
            
            // 显示启动画面
            this.showStartupScreen();
            
            // 检查现代Web API支持
            if (!await this.checkModernSupport()) {
                throw new Error('当前浏览器不支持所需的现代Web API');
            }
            
            // 初始化性能监控
            this.initPerformanceObserver();
            
            // 初始化3D场景
            await this.setupScene();
            
            // 创建OSI层级
            this.createOSILayers();
            
            // 生成层级标签
            this.generateLayerLabels();
            
            // 设置事件监听
            this.setupEventListeners();
            
            // 开始渲染循环
            this.startRenderLoop();
            
            // 隐藏启动画面
            setTimeout(() => this.hideStartupScreen(), 2000);
            
            console.log('✅ 现代OSI 3D模拟器初始化完成');
            
        } catch (error) {
            console.error('❌ 初始化失败:', error);
            this.showError('初始化失败: ' + error.message);
        }
    }
    
    // 现代Web API支持检查
    async checkModernSupport() {
        const checks = [
            // WebGL 2.0支持
            () => {
                const canvas = document.createElement('canvas');
                const gl = canvas.getContext('webgl2');
                return !!gl;
            },
            // ES模块支持
            () => 'noModule' in HTMLScriptElement.prototype,
            // Performance Observer支持
            () => 'PerformanceObserver' in window,
            // 私有字段支持
            () => {
                try {
                    new Function('class Test { #private = 1; }')();
                    return true;
                } catch {
                    return false;
                }
            }
        ];
        
        const results = checks.map(check => check());
        const allSupported = results.every(Boolean);
        
        if (allSupported) {
            console.log('✅ 所有现代Web API都受支持');
        } else {
            console.warn('⚠️ 部分现代Web API不受支持，将使用降级方案');
        }
        
        return results[0]; // 至少需要WebGL 2.0
    }
    
    // 现代性能监控 - Performance Observer API
    initPerformanceObserver() {
        if ('PerformanceObserver' in window) {
            this.#performanceObserver = new PerformanceObserver((list) => {
                for (const entry of list.getEntries()) {
                    if (entry.entryType === 'measure') {
                        this.#stats.set(entry.name, entry.duration);
                    }
                }
            });
            
            this.#performanceObserver.observe({ entryTypes: ['measure'] });
            console.log('📊 性能监控已启用');
        }
    }
    
    showStartupScreen() {
        const progressFill = document.getElementById('progress-fill');
        const progressText = document.getElementById('progress-text');
        
        let progress = 0;
        const interval = setInterval(() => {
            progress += Math.random() * 15;
            if (progress > 100) progress = 100;
            
            if (progressFill) progressFill.style.width = progress + '%';
            
            if (progress >= 100) {
                clearInterval(interval);
                if (progressText) progressText.textContent = '初始化完成！';
            }
        }, 200);
    }
    
    hideStartupScreen() {
        const startupScreen = document.getElementById('startup-screen');
        if (startupScreen) {
            startupScreen.classList.add('hidden');
        }
    }
    
    async setupScene() {
        const container = document.getElementById('three-canvas');
        if (!container) {
            throw new Error('找不到3D场景容器');
        }
        
        const rect = container.getBoundingClientRect();
        const width = rect.width || 800;
        const height = rect.height || 600;
        
        console.log(`📐 场景容器尺寸: ${width}x${height}`);
        
        // 创建场景
        this.#scene = new THREE.Scene();
        this.#scene.background = new THREE.Color(0x0a0b1e);
        this.#scene.fog = new THREE.Fog(0x0a0b1e, 50, 200);
        
        // 创建摄像机
        this.#camera = new THREE.PerspectiveCamera(75, width / height, 0.1, 1000);
        this.#camera.position.set(40, 35, 40);
        this.#camera.lookAt(0, 10, 0);
        
        // 创建渲染器 - 使用WebGL 2.0和现代特性
        this.#renderer = new THREE.WebGLRenderer({ 
            antialias: true,
            alpha: true,
            powerPreference: "high-performance"
        });
        this.#renderer.setSize(width, height);
        this.#renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
        this.#renderer.shadowMap.enabled = true;
        this.#renderer.shadowMap.type = THREE.PCFSoftShadowMap;
        this.#renderer.toneMapping = THREE.ACESFilmicToneMapping;
        this.#renderer.toneMappingExposure = 1.2;
        this.#renderer.outputColorSpace = THREE.SRGBColorSpace;
        
        container.appendChild(this.#renderer.domElement);
        
        // 设置后处理
        this.setupPostProcessing();
        
        // 添加光照系统
        this.setupLighting();
        
        // 创建环境
        this.createEnvironment();
        
        // 设置控制器
        this.setupControls();
    }
    
    setupPostProcessing() {
        this.#composer = new EffectComposer(this.#renderer);
        
        const renderPass = new RenderPass(this.#scene, this.#camera);
        this.#composer.addPass(renderPass);
        
        const bloomPass = new UnrealBloomPass(
            new THREE.Vector2(window.innerWidth, window.innerHeight),
            0.5, // 强度
            0.4, // 半径
            0.85 // 阈值
        );
        this.#composer.addPass(bloomPass);
        
        console.log('🎨 后处理效果已启用');
    }
    
    setupLighting() {
        // 环境光
        const ambientLight = new THREE.AmbientLight(0x404040, 0.4);
        this.#scene.add(ambientLight);
        
        // 主方向光
        const mainLight = new THREE.DirectionalLight(0xffffff, 1.2);
        mainLight.position.set(50, 100, 50);
        mainLight.castShadow = true;
        mainLight.shadow.mapSize.width = 2048;
        mainLight.shadow.mapSize.height = 2048;
        this.#scene.add(mainLight);
        
        // 为每个OSI层添加特色光源
        for (const [layerNum, data] of this.#layerData) {
            const pointLight = new THREE.PointLight(data.color, 0.8, 20);
            pointLight.position.set(0, (layerNum - 1) * 3 + 3, 0);
            pointLight.userData = { 
                originalIntensity: 0.8,
                pulsePhase: Math.random() * Math.PI * 2,
                layerNum: layerNum
            };
            this.#scene.add(pointLight);
        }
        
        console.log('💡 现代光照系统设置完成');
    }
    
    createEnvironment() {
        // 创建地面
        const groundGeometry = new THREE.PlaneGeometry(100, 100);
        const groundMaterial = new THREE.MeshLambertMaterial({ 
            color: 0x1a1b2e,
            transparent: true,
            opacity: 0.8
        });
        
        const ground = new THREE.Mesh(groundGeometry, groundMaterial);
        ground.rotation.x = -Math.PI / 2;
        ground.position.y = -2;
        ground.receiveShadow = true;
        
        this.#scene.add(ground);
        
        // 创建背景粒子
        this.createBackgroundParticles();

        console.log('🌍 现代环境创建完成');
    }

    createBackgroundParticles() {
        const particleCount = 500;
        const positions = new Float32Array(particleCount * 3);
        const colors = new Float32Array(particleCount * 3);

        for (let i = 0; i < particleCount; i++) {
            const i3 = i * 3;

            positions[i3] = (Math.random() - 0.5) * 200;
            positions[i3 + 1] = Math.random() * 100;
            positions[i3 + 2] = (Math.random() - 0.5) * 200;

            const color = new THREE.Color();
            color.setHSL(Math.random() * 0.3 + 0.5, 0.7, 0.5);
            colors[i3] = color.r;
            colors[i3 + 1] = color.g;
            colors[i3 + 2] = color.b;
        }

        const particleGeometry = new THREE.BufferGeometry();
        particleGeometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
        particleGeometry.setAttribute('color', new THREE.BufferAttribute(colors, 3));

        const particleMaterial = new THREE.PointsMaterial({
            size: 0.8,
            vertexColors: true,
            transparent: true,
            opacity: 0.6,
            blending: THREE.AdditiveBlending
        });

        const particles = new THREE.Points(particleGeometry, particleMaterial);
        this.#scene.add(particles);
        this.#particleSystems.push(particles);
    }

    createOSILayers() {
        for (const [layerNum, data] of this.#layerData) {
            this.createLayerMesh(layerNum, data);
        }
        console.log('🏗️ OSI层级创建完成');
    }

    createLayerMesh(layerNum, data) {
        // 创建有机形状的几何体
        const geometry = new THREE.BoxGeometry(8, 2, 8, 4, 2, 4);

        // 添加噪声变形，创造有机感
        const positions = geometry.attributes.position;
        for (let i = 0; i < positions.count; i++) {
            const x = positions.getX(i);
            const y = positions.getY(i);
            const z = positions.getZ(i);

            const noise = (Math.sin(x * 0.5) + Math.cos(z * 0.5)) * 0.2;
            positions.setY(i, y + noise);
        }
        geometry.computeVertexNormals();

        // 创建现代材质 - 使用PBR材质
        const material = new THREE.MeshPhysicalMaterial({
            color: data.color,
            transparent: true,
            opacity: 0.7,
            roughness: 0.3,
            metalness: 0.1,
            clearcoat: 0.5,
            clearcoatRoughness: 0.1,
            emissive: data.color,
            emissiveIntensity: 0.1
        });

        const mesh = new THREE.Mesh(geometry, material);
        mesh.position.y = (layerNum - 1) * 3;
        mesh.castShadow = true;
        mesh.receiveShadow = true;
        mesh.userData = {
            layerNum,
            data,
            type: 'osiLayer',
            originalPosition: mesh.position.clone(),
            pulsePhase: Math.random() * Math.PI * 2
        };

        this.#scene.add(mesh);
        this.#layerMeshes.push(mesh);

        // 创建发光边框
        const edges = new THREE.EdgesGeometry(geometry);
        const lineMaterial = new THREE.LineBasicMaterial({
            color: data.color,
            transparent: true,
            opacity: 0.8
        });

        const wireframe = new THREE.LineSegments(edges, lineMaterial);
        wireframe.position.copy(mesh.position);
        this.#scene.add(wireframe);
    }

    generateLayerLabels() {
        const labelsContainer = document.getElementById('layer-labels');
        if (!labelsContainer) return;

        labelsContainer.innerHTML = '';

        const positions = [
            { top: '10%', left: '20%' },
            { top: '20%', left: '70%' },
            { top: '30%', left: '25%' },
            { top: '40%', left: '65%' },
            { top: '50%', left: '30%' },
            { top: '60%', left: '60%' },
            { top: '70%', left: '35%' }
        ];

        let index = 0;
        for (const [layerNum, data] of this.#layerData) {
            const label = document.createElement('div');
            label.className = 'layer-label';
            label.dataset.layer = layerNum;
            label.style.top = positions[index].top;
            label.style.left = positions[index].left;

            label.innerHTML = `
                <div class="label-content">
                    <div class="label-header">
                        <span class="label-number">${layerNum}</span>
                    </div>
                    <div class="label-info">
                        <div class="label-name">${data.name}</div>
                        <div class="label-english">${data.english}</div>
                        <div class="label-description">${data.description}</div>
                    </div>
                </div>
            `;

            label.addEventListener('click', () => this.focusOnLayer(layerNum));
            labelsContainer.appendChild(label);
            index++;
        }
    }

    setupControls() {
        this.#controls = new OrbitControls(this.#camera, this.#renderer.domElement);
        this.#controls.enableDamping = true;
        this.#controls.dampingFactor = 0.05;
        this.#controls.screenSpacePanning = false;
        this.#controls.minDistance = 20;
        this.#controls.maxDistance = 100;
        this.#controls.maxPolarAngle = Math.PI / 2;
    }

    setupEventListeners() {
        // 窗口大小调整
        window.addEventListener('resize', () => this.onWindowResize());

        // 仿真控制按钮
        document.getElementById('start-simulation')?.addEventListener('click', () => {
            this.toggleSimulation();
        });

        document.getElementById('reset-simulation')?.addEventListener('click', () => {
            this.resetSimulation();
        });
    }

    startRenderLoop() {
        const animate = () => {
            this.#animationId = requestAnimationFrame(animate);

            // 更新控制器
            this.#controls?.update();

            // 更新动画
            this.updateAnimations();

            // 使用后处理渲染
            if (this.#composer) {
                this.#composer.render();
            } else {
                this.#renderer.render(this.#scene, this.#camera);
            }
        };

        animate();
        console.log('🎬 现代渲染循环已启动');
    }

    updateAnimations() {
        const time = performance.now() * 0.001;

        // 更新OSI层级动画
        this.#layerMeshes.forEach(mesh => {
            const userData = mesh.userData;

            // 脉冲效果
            const pulseIntensity = Math.sin(time * 2 + userData.pulsePhase) * 0.1 + 0.9;
            mesh.scale.setScalar(pulseIntensity);

            // 轻微旋转
            mesh.rotation.y += 0.005 * (userData.layerNum / 7);

            // 材质发光强度变化
            if (mesh.material.emissiveIntensity !== undefined) {
                mesh.material.emissiveIntensity = 0.1 + Math.sin(time + userData.pulsePhase) * 0.05;
            }
        });

        // 更新粒子系统
        this.#particleSystems.forEach(system => {
            system.rotation.y += 0.001;
        });
    }

    onWindowResize() {
        const container = document.getElementById('three-canvas');
        if (!container || !this.#renderer || !this.#camera) return;

        const rect = container.getBoundingClientRect();
        const width = rect.width || 800;
        const height = rect.height || 600;

        this.#camera.aspect = width / height;
        this.#camera.updateProjectionMatrix();
        this.#renderer.setSize(width, height);
        this.#composer?.setSize(width, height);
    }

    focusOnLayer(layerNum) {
        console.log(`🎯 聚焦到第${layerNum}层`);
        this.#focusedLayer = layerNum;
    }

    toggleSimulation() {
        this.#isSimulating = !this.#isSimulating;
        const btn = document.getElementById('start-simulation');
        if (btn) {
            const icon = btn.querySelector('i');
            const text = btn.querySelector('span');
            if (this.#isSimulating) {
                icon.className = 'fas fa-pause';
                text.textContent = '暂停生命流';
            } else {
                icon.className = 'fas fa-play';
                text.textContent = '启动生命流';
            }
        }
    }

    resetSimulation() {
        this.#isSimulating = false;

        // 重置所有动画状态
        this.#layerMeshes.forEach(mesh => {
            mesh.scale.setScalar(1);
            mesh.rotation.set(0, 0, 0);
            if (mesh.material.emissiveIntensity !== undefined) {
                mesh.material.emissiveIntensity = 0.1;
            }
        });

        console.log('🔄 仿真已重置');
    }

    // 公共API方法 - 使用getter提供只读访问
    get isSimulating() {
        return this.#isSimulating;
    }

    get currentView() {
        return this.#currentView;
    }

    get layerData() {
        return this.#layerData;
    }

    showError(message) {
        console.error(message);
        alert(message);
    }
}

// 导出类
export default ModernOSI3DSimulator;
