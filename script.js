// OSI 3D 生物仿生艺术模拟器 - 兼容版本
// 可直接在浏览器中运行，无需服务器

/**
 * OSI 3D模拟器类
 * 兼容版本，使用传统JavaScript语法
 */
class OSI3DSimulator {
    constructor() {
        // 核心Three.js对象
        this.scene = null;
        this.camera = null;
        this.renderer = null;
        this.composer = null;
        this.controls = null;
        this.animationId = null;
        
        // 状态管理
        this.isSimulating = false;
        this.currentView = 'overview';
        this.focusedLayer = null;
        
        // 3D对象集合
        this.layerMeshes = [];
        this.particleSystems = [];
        
        // 性能监控
        this.stats = {
            fps: 0,
            frameCount: 0,
            lastTime: performance.now(),
            memory: 0
        };
        
        // OSI层级数据
        this.layerData = {
            7: { 
                name: "应用层", 
                english: "Application", 
                color: 0xE6B3FF, 
                description: "神经网络中枢",
                protocols: ["HTTP", "HTTPS", "FTP", "SMTP", "DNS"],
                devices: ["Web服务器", "邮件服务器", "DNS服务器"],
                bioAnalogy: "大脑皮层"
            },
            6: { 
                name: "表示层", 
                english: "Presentation", 
                color: 0xFF6B9D, 
                description: "转译生物器官",
                protocols: ["SSL", "TLS", "JPEG", "MPEG", "ASCII"],
                devices: ["加密设备", "压缩器", "格式转换器"],
                bioAnalogy: "感觉器官"
            },
            5: { 
                name: "会话层", 
                english: "Session", 
                color: 0x00E5FF, 
                description: "对话细胞集群",
                protocols: ["NetBIOS", "RPC", "SQL", "NFS"],
                devices: ["会话管理器", "认证服务器"],
                bioAnalogy: "神经突触"
            },
            4: { 
                name: "传输层", 
                english: "Transport", 
                color: 0x69F0AE, 
                description: "循环系统节点",
                protocols: ["TCP", "UDP", "SPX", "SCTP"],
                devices: ["网关", "防火墙", "代理服务器"],
                bioAnalogy: "循环系统"
            },
            3: { 
                name: "网络层", 
                english: "Network", 
                color: 0xFFD54F, 
                description: "导航蜂群",
                protocols: ["IP", "ICMP", "IGMP", "ARP"],
                devices: ["路由器", "三层交换机"],
                bioAnalogy: "神经网络"
            },
            2: { 
                name: "数据链路层", 
                english: "Data Link", 
                color: 0xFF5722, 
                description: "微生物群落",
                protocols: ["Ethernet", "PPP", "Frame Relay"],
                devices: ["交换机", "网桥", "网卡"],
                bioAnalogy: "细胞膜"
            },
            1: { 
                name: "物理层", 
                english: "Physical", 
                color: 0x9C27B0, 
                description: "菌丝网络基质",
                protocols: ["电信号", "光信号", "无线信号"],
                devices: ["集线器", "中继器", "网线", "光纤"],
                bioAnalogy: "DNA双螺旋"
            }
        };
        
        this.init();
    }
    
    async init() {
        try {
            console.log('🚀 开始初始化OSI 3D模拟器...');
            
            // 显示启动画面
            this.showStartupScreen();
            
            // 检查WebGL支持
            if (!this.checkWebGLSupport()) {
                throw new Error('当前浏览器不支持WebGL');
            }
            
            // 等待Three.js加载
            await this.waitForThreeJS();
            
            // 初始化3D场景
            await this.setupScene();
            
            // 创建OSI层级
            this.createOSILayers();
            
            // 生成层级标签
            this.generateLayerLabels();
            
            // 设置事件监听
            this.setupEventListeners();
            
            // 开始渲染循环
            this.startRenderLoop();
            
            // 隐藏启动画面
            setTimeout(() => this.hideStartupScreen(), 2000);
            
            console.log('✅ OSI 3D模拟器初始化完成');
            
        } catch (error) {
            console.error('❌ 初始化失败:', error);
            this.showError('初始化失败: ' + error.message);
        }
    }
    
    async waitForThreeJS() {
        return new Promise((resolve) => {
            if (typeof THREE !== 'undefined') {
                resolve();
            } else {
                const checkInterval = setInterval(() => {
                    if (typeof THREE !== 'undefined') {
                        clearInterval(checkInterval);
                        resolve();
                    }
                }, 100);
            }
        });
    }
    
    checkWebGLSupport() {
        try {
            const canvas = document.createElement('canvas');
            const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
            if (!gl) return false;
            
            const renderer = gl.getParameter(gl.RENDERER);
            const vendor = gl.getParameter(gl.VENDOR);
            console.log(`✅ WebGL支持正常 - ${vendor}: ${renderer}`);
            return true;
        } catch (e) {
            console.error('WebGL检测失败:', e);
            return false;
        }
    }
    
    showStartupScreen() {
        const progressFill = document.getElementById('progress-fill');
        const progressText = document.getElementById('progress-text');
        
        let progress = 0;
        const interval = setInterval(() => {
            progress += Math.random() * 15;
            if (progress > 100) progress = 100;
            
            if (progressFill) progressFill.style.width = progress + '%';
            
            if (progress >= 100) {
                clearInterval(interval);
                if (progressText) progressText.textContent = '初始化完成！';
            }
        }, 200);
    }
    
    hideStartupScreen() {
        const startupScreen = document.getElementById('startup-screen');
        if (startupScreen) {
            startupScreen.classList.add('hidden');
        }
    }
    
    async setupScene() {
        const container = document.getElementById('three-canvas');
        if (!container) {
            throw new Error('找不到3D场景容器');
        }
        
        const rect = container.getBoundingClientRect();
        const width = rect.width || 800;
        const height = rect.height || 600;
        
        console.log(`📐 场景容器尺寸: ${width}x${height}`);
        
        // 创建场景
        this.scene = new THREE.Scene();
        this.scene.background = new THREE.Color(0x0a0b1e);
        this.scene.fog = new THREE.Fog(0x0a0b1e, 50, 200);
        
        // 创建摄像机
        this.camera = new THREE.PerspectiveCamera(75, width / height, 0.1, 1000);
        this.camera.position.set(40, 35, 40);
        this.camera.lookAt(0, 10, 0);
        
        // 创建渲染器
        this.renderer = new THREE.WebGLRenderer({ 
            antialias: true,
            alpha: true,
            powerPreference: "high-performance"
        });
        this.renderer.setSize(width, height);
        this.renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
        this.renderer.shadowMap.enabled = true;
        this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
        
        container.appendChild(this.renderer.domElement);
        
        // 设置后处理（如果可用）
        this.setupPostProcessing();
        
        // 添加光照系统
        this.setupLighting();
        
        // 创建环境
        this.createEnvironment();
        
        // 设置控制器
        this.setupControls();
    }
    
    setupPostProcessing() {
        // 检查后处理支持
        if (typeof THREE.EffectComposer !== 'undefined' && typeof THREE.UnrealBloomPass !== 'undefined') {
            try {
                this.composer = new THREE.EffectComposer(this.renderer);

                // 基础渲染通道
                const renderPass = new THREE.RenderPass(this.scene, this.camera);
                this.composer.addPass(renderPass);

                // Bloom效果
                const bloomPass = new THREE.UnrealBloomPass(
                    new THREE.Vector2(window.innerWidth, window.innerHeight),
                    0.5,  // 强度
                    0.4,  // 半径
                    0.85  // 阈值
                );
                this.composer.addPass(bloomPass);

                // 存储bloom通道以便动态调整
                this.bloomPass = bloomPass;

                console.log('🎨 增强后处理效果已启用 (Bloom)');
            } catch (error) {
                console.log('⚠️ 增强后处理效果不可用，尝试基础后处理');
                this.setupBasicPostProcessing();
            }
        } else {
            this.setupBasicPostProcessing();
        }
    }

    setupBasicPostProcessing() {
        if (typeof THREE.EffectComposer !== 'undefined') {
            try {
                this.composer = new THREE.EffectComposer(this.renderer);

                const renderPass = new THREE.RenderPass(this.scene, this.camera);
                this.composer.addPass(renderPass);

                console.log('🎨 基础后处理效果已启用');
            } catch (error) {
                console.log('⚠️ 后处理效果不可用，使用基础渲染');
            }
        } else {
            console.log('⚠️ 后处理效果不可用，使用基础渲染');
        }
    }
    
    setupLighting() {
        // 环境光 - 增强色彩
        const ambientLight = new THREE.AmbientLight(0x2a2a4a, 0.3);
        this.scene.add(ambientLight);

        // 主方向光 - 增强阴影
        const mainLight = new THREE.DirectionalLight(0xffffff, 1.5);
        mainLight.position.set(50, 100, 50);
        mainLight.castShadow = true;
        mainLight.shadow.mapSize.width = 4096;
        mainLight.shadow.mapSize.height = 4096;
        mainLight.shadow.camera.near = 0.1;
        mainLight.shadow.camera.far = 500;
        mainLight.shadow.camera.left = -100;
        mainLight.shadow.camera.right = 100;
        mainLight.shadow.camera.top = 100;
        mainLight.shadow.camera.bottom = -100;
        mainLight.shadow.bias = -0.0001;
        this.scene.add(mainLight);

        // 辅助光源 - 蓝色调
        const fillLight = new THREE.DirectionalLight(0x4facfe, 0.8);
        fillLight.position.set(-30, 50, -30);
        this.scene.add(fillLight);

        // 顶部光源 - 紫色调
        const topLight = new THREE.PointLight(0x9c27b0, 1.2, 100);
        topLight.position.set(0, 80, 0);
        this.scene.add(topLight);

        // 底部光源 - 青色调
        const bottomLight = new THREE.PointLight(0x00e5ff, 0.8, 80);
        bottomLight.position.set(0, -20, 0);
        this.scene.add(bottomLight);

        // 动态光源数组
        this.dynamicLights = [];

        // 创建动态彩色光源
        for (let i = 0; i < 3; i++) {
            const light = new THREE.PointLight(0xffffff, 0.6, 50);
            light.position.set(
                Math.cos(i * Math.PI * 2 / 3) * 30,
                20 + Math.sin(i * 2) * 10,
                Math.sin(i * Math.PI * 2 / 3) * 30
            );
            this.scene.add(light);
            this.dynamicLights.push({
                light: light,
                originalPosition: light.position.clone(),
                phase: i * Math.PI * 2 / 3
            });
        }

        console.log('💡 增强光照系统已设置');
    }
    
    createEnvironment() {
        // 创建地面
        const groundGeometry = new THREE.PlaneGeometry(100, 100);
        const groundMaterial = new THREE.MeshLambertMaterial({ 
            color: 0x1a1b2e,
            transparent: true,
            opacity: 0.8
        });
        
        const ground = new THREE.Mesh(groundGeometry, groundMaterial);
        ground.rotation.x = -Math.PI / 2;
        ground.position.y = -2;
        ground.receiveShadow = true;
        
        this.scene.add(ground);
        
        // 创建背景粒子
        this.createBackgroundParticles();

        console.log('🌍 环境创建完成');
    }

    createBackgroundParticles() {
        // 创建多层粒子系统
        this.createFloatingParticles();
        this.createDataStreamParticles();
        this.createEnergyParticles();
    }

    createFloatingParticles() {
        const particleCount = 500;
        const positions = new Float32Array(particleCount * 3);
        const colors = new Float32Array(particleCount * 3);
        const sizes = new Float32Array(particleCount);
        const velocities = new Float32Array(particleCount * 3);

        for (let i = 0; i < particleCount; i++) {
            const i3 = i * 3;

            positions[i3] = (Math.random() - 0.5) * 200;
            positions[i3 + 1] = Math.random() * 100;
            positions[i3 + 2] = (Math.random() - 0.5) * 200;

            // 随机速度
            velocities[i3] = (Math.random() - 0.5) * 0.02;
            velocities[i3 + 1] = Math.random() * 0.01;
            velocities[i3 + 2] = (Math.random() - 0.5) * 0.02;

            const color = new THREE.Color();
            color.setHSL(Math.random() * 0.3 + 0.5, 0.7, 0.5);
            colors[i3] = color.r;
            colors[i3 + 1] = color.g;
            colors[i3 + 2] = color.b;

            sizes[i] = Math.random() * 2 + 0.5;
        }

        const particleGeometry = new THREE.BufferGeometry();
        particleGeometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
        particleGeometry.setAttribute('color', new THREE.BufferAttribute(colors, 3));
        particleGeometry.setAttribute('size', new THREE.BufferAttribute(sizes, 1));

        const particleMaterial = new THREE.PointsMaterial({
            size: 1.0,
            vertexColors: true,
            transparent: true,
            opacity: 0.8,
            blending: THREE.AdditiveBlending,
            sizeAttenuation: true
        });

        const particles = new THREE.Points(particleGeometry, particleMaterial);
        particles.userData = {
            velocities: velocities,
            type: 'floating'
        };
        this.scene.add(particles);
        this.particleSystems.push(particles);
    }

    createDataStreamParticles() {
        // 创建数据流粒子
        const streamCount = 100;
        const positions = new Float32Array(streamCount * 3);
        const colors = new Float32Array(streamCount * 3);

        for (let i = 0; i < streamCount; i++) {
            const i3 = i * 3;
            const angle = (i / streamCount) * Math.PI * 2;
            const radius = 25 + Math.random() * 10;

            positions[i3] = Math.cos(angle) * radius;
            positions[i3 + 1] = (Math.random() - 0.5) * 50;
            positions[i3 + 2] = Math.sin(angle) * radius;

            // 蓝色调数据流
            const color = new THREE.Color(0x4facfe);
            colors[i3] = color.r;
            colors[i3 + 1] = color.g;
            colors[i3 + 2] = color.b;
        }

        const streamGeometry = new THREE.BufferGeometry();
        streamGeometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
        streamGeometry.setAttribute('color', new THREE.BufferAttribute(colors, 3));

        const streamMaterial = new THREE.PointsMaterial({
            size: 1.5,
            vertexColors: true,
            transparent: true,
            opacity: 0.9,
            blending: THREE.AdditiveBlending
        });

        const dataStream = new THREE.Points(streamGeometry, streamMaterial);
        dataStream.userData = { type: 'dataStream' };
        this.scene.add(dataStream);
        this.particleSystems.push(dataStream);
    }

    createEnergyParticles() {
        // 创建能量粒子围绕OSI层级
        for (let layer = 1; layer <= 7; layer++) {
            const energyCount = 20;
            const positions = new Float32Array(energyCount * 3);
            const colors = new Float32Array(energyCount * 3);

            const layerColor = new THREE.Color(this.layerData[layer].color);

            for (let i = 0; i < energyCount; i++) {
                const i3 = i * 3;
                const angle = (i / energyCount) * Math.PI * 2;
                const radius = 8 + Math.random() * 4;

                positions[i3] = Math.cos(angle) * radius;
                positions[i3 + 1] = (layer - 1) * 3 + 3 + (Math.random() - 0.5) * 2;
                positions[i3 + 2] = Math.sin(angle) * radius;

                colors[i3] = layerColor.r;
                colors[i3 + 1] = layerColor.g;
                colors[i3 + 2] = layerColor.b;
            }

            const energyGeometry = new THREE.BufferGeometry();
            energyGeometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
            energyGeometry.setAttribute('color', new THREE.BufferAttribute(colors, 3));

            const energyMaterial = new THREE.PointsMaterial({
                size: 0.8,
                vertexColors: true,
                transparent: true,
                opacity: 0.7,
                blending: THREE.AdditiveBlending
            });

            const energyParticles = new THREE.Points(energyGeometry, energyMaterial);
            energyParticles.userData = {
                type: 'energy',
                layer: layer,
                rotationSpeed: 0.01 + Math.random() * 0.02
            };
            this.scene.add(energyParticles);
            this.particleSystems.push(energyParticles);
        }

        console.log('✨ 增强粒子系统创建完成');
    }

    createOSILayers() {
        Object.entries(this.layerData).forEach(([layerNum, data]) => {
            this.createLayerMesh(parseInt(layerNum), data);
        });

        console.log('🏗️ OSI层级创建完成');
    }

    createLayerMesh(layerNum, data) {
        // 创建有机形状的几何体
        const geometry = new THREE.BoxGeometry(8, 2, 8, 4, 2, 4);

        // 添加噪声变形，创造有机感
        const positions = geometry.attributes.position;
        for (let i = 0; i < positions.count; i++) {
            const x = positions.getX(i);
            const y = positions.getY(i);
            const z = positions.getZ(i);

            const noise = (Math.sin(x * 0.5) + Math.cos(z * 0.5)) * 0.2;
            positions.setY(i, y + noise);
        }
        geometry.computeVertexNormals();

        // 创建材质
        const material = new THREE.MeshPhongMaterial({
            color: data.color,
            transparent: true,
            opacity: 0.7,
            shininess: 100,
            specular: 0x4facfe,
            emissive: data.color,
            emissiveIntensity: 0.1
        });

        const mesh = new THREE.Mesh(geometry, material);
        mesh.position.y = (layerNum - 1) * 3;
        mesh.castShadow = true;
        mesh.receiveShadow = true;
        mesh.userData = {
            layerNum,
            data,
            type: 'osiLayer',
            originalPosition: mesh.position.clone(),
            pulsePhase: Math.random() * Math.PI * 2
        };

        this.scene.add(mesh);
        this.layerMeshes.push(mesh);

        // 创建发光边框
        const edges = new THREE.EdgesGeometry(geometry);
        const lineMaterial = new THREE.LineBasicMaterial({
            color: data.color,
            transparent: true,
            opacity: 0.8
        });

        const wireframe = new THREE.LineSegments(edges, lineMaterial);
        wireframe.position.copy(mesh.position);
        this.scene.add(wireframe);
    }

    generateLayerLabels() {
        const labelsContainer = document.getElementById('layer-labels');
        if (!labelsContainer) return;

        labelsContainer.innerHTML = '';

        const positions = [
            { top: '10%', left: '20%' },
            { top: '20%', left: '70%' },
            { top: '30%', left: '25%' },
            { top: '40%', left: '65%' },
            { top: '50%', left: '30%' },
            { top: '60%', left: '60%' },
            { top: '70%', left: '35%' }
        ];

        Object.entries(this.layerData).forEach(([layerNum, data], index) => {
            const label = document.createElement('div');
            label.className = 'layer-label';
            label.dataset.layer = layerNum;
            label.style.top = positions[index].top;
            label.style.left = positions[index].left;

            label.innerHTML = `
                <div class="label-content">
                    <div class="label-header">
                        <span class="label-number">${layerNum}</span>
                    </div>
                    <div class="label-info">
                        <div class="label-name">${data.name}</div>
                        <div class="label-english">${data.english}</div>
                        <div class="label-description">${data.description}</div>
                    </div>
                </div>
            `;

            label.addEventListener('click', () => this.focusOnLayer(layerNum));
            labelsContainer.appendChild(label);
        });
    }

    setupControls() {
        if (typeof THREE.OrbitControls !== 'undefined') {
            this.controls = new THREE.OrbitControls(this.camera, this.renderer.domElement);
            this.controls.enableDamping = true;
            this.controls.dampingFactor = 0.05;
            this.controls.screenSpacePanning = false;
            this.controls.minDistance = 20;
            this.controls.maxDistance = 100;
            this.controls.maxPolarAngle = Math.PI / 2;
        } else {
            // 简单的鼠标控制
            this.setupSimpleControls();
        }
    }

    setupSimpleControls() {
        const canvas = this.renderer.domElement;
        let isMouseDown = false;
        let mouseX = 0, mouseY = 0;
        let cameraAngle = 0;
        let cameraRadius = 60;
        let cameraHeight = 35;

        canvas.addEventListener('mousedown', (e) => {
            isMouseDown = true;
            mouseX = e.clientX;
            mouseY = e.clientY;
            canvas.style.cursor = 'grabbing';
        });

        canvas.addEventListener('mousemove', (e) => {
            if (!isMouseDown) return;

            const deltaX = e.clientX - mouseX;
            const deltaY = e.clientY - mouseY;

            cameraAngle += deltaX * 0.01;
            cameraHeight += deltaY * 0.1;
            cameraHeight = Math.max(10, Math.min(80, cameraHeight));

            this.updateCameraPosition(cameraAngle, cameraRadius, cameraHeight);

            mouseX = e.clientX;
            mouseY = e.clientY;
        });

        canvas.addEventListener('mouseup', () => {
            isMouseDown = false;
            canvas.style.cursor = 'grab';
        });

        canvas.addEventListener('wheel', (e) => {
            e.preventDefault();
            cameraRadius += e.deltaY * 0.1;
            cameraRadius = Math.max(20, Math.min(100, cameraRadius));

            this.updateCameraPosition(cameraAngle, cameraRadius, cameraHeight);
        });

        canvas.style.cursor = 'grab';
    }

    updateCameraPosition(angle, radius, height) {
        this.camera.position.x = Math.cos(angle) * radius;
        this.camera.position.y = height;
        this.camera.position.z = Math.sin(angle) * radius;
        this.camera.lookAt(0, 10, 0);
    }

    setupEventListeners() {
        // 窗口大小调整
        window.addEventListener('resize', () => this.onWindowResize());

        // 视角控制按钮
        document.querySelectorAll('.view-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const view = e.currentTarget.dataset.view;
                this.switchView(view);

                // 更新按钮状态
                document.querySelectorAll('.view-btn').forEach(b => b.classList.remove('active'));
                e.currentTarget.classList.add('active');
            });
        });

        // 仿真控制按钮
        const startBtn = document.getElementById('start-simulation');
        if (startBtn) {
            startBtn.addEventListener('click', () => this.toggleSimulation());
        }

        const resetBtn = document.getElementById('reset-simulation');
        if (resetBtn) {
            resetBtn.addEventListener('click', () => this.resetSimulation());
        }

        // 交互模式切换
        document.querySelectorAll('.interaction-mode').forEach(mode => {
            mode.addEventListener('click', (e) => {
                const modeType = e.currentTarget.dataset.mode;
                this.switchInteractionMode(modeType);

                // 更新模式状态
                document.querySelectorAll('.interaction-mode').forEach(m => m.classList.remove('active'));
                e.currentTarget.classList.add('active');
            });
        });

        // 标签页切换
        document.querySelectorAll('.tab-btn').forEach(tab => {
            tab.addEventListener('click', (e) => {
                const tabName = e.currentTarget.dataset.tab;
                this.switchTab(tabName);

                // 更新标签状态
                document.querySelectorAll('.tab-btn').forEach(t => t.classList.remove('active'));
                e.currentTarget.classList.add('active');

                document.querySelectorAll('.tab-panel').forEach(p => p.classList.remove('active'));
                document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');
            });
        });

        // 全屏按钮
        const fullscreenBtn = document.getElementById('fullscreen-btn');
        if (fullscreenBtn) {
            fullscreenBtn.addEventListener('click', () => this.toggleFullscreen());
        }

        // 设置按钮
        const settingsBtn = document.getElementById('settings-btn');
        if (settingsBtn) {
            settingsBtn.addEventListener('click', () => this.openSettings());
        }

        // 信息按钮
        const infoBtn = document.getElementById('info-btn');
        if (infoBtn) {
            infoBtn.addEventListener('click', () => this.showInfo());
        }

        // 3D场景点击事件
        if (this.renderer) {
            this.renderer.domElement.addEventListener('click', (event) => {
                this.onSceneClick(event);
            });
        }

        // 键盘快捷键
        document.addEventListener('keydown', (event) => {
            this.handleKeyboard(event);
        });

        console.log('🎮 事件监听器设置完成');
    }

    // 视角切换功能
    switchView(viewType) {
        this.currentView = viewType;

        switch (viewType) {
            case 'overview':
                this.animateCamera({ x: 40, y: 35, z: 40 }, { x: 0, y: 10, z: 0 });
                break;
            case 'layers':
                this.animateCamera({ x: 0, y: 50, z: 30 }, { x: 0, y: 10, z: 0 });
                break;
            case 'network':
                this.animateCamera({ x: 60, y: 20, z: 0 }, { x: 0, y: 10, z: 0 });
                break;
        }

        // 更新状态显示
        const cameraPosition = document.getElementById('camera-position');
        if (cameraPosition) {
            const viewNames = {
                'overview': '生态总览',
                'layers': '层级聚焦',
                'network': '网络拓扑'
            };
            cameraPosition.textContent = `视角: ${viewNames[viewType]}`;
        }

        console.log(`📷 切换到视角: ${viewType}`);
    }

    // 相机动画
    animateCamera(targetPosition, targetLookAt, duration = 2000) {
        if (!this.camera) return;

        const startPosition = this.camera.position.clone();
        const startTime = performance.now();

        const animate = () => {
            const elapsed = performance.now() - startTime;
            const progress = Math.min(elapsed / duration, 1);

            // 使用缓动函数
            const easeProgress = 1 - Math.pow(1 - progress, 3);

            this.camera.position.lerpVectors(startPosition, new THREE.Vector3(targetPosition.x, targetPosition.y, targetPosition.z), easeProgress);
            this.camera.lookAt(targetLookAt.x, targetLookAt.y, targetLookAt.z);

            if (progress < 1) {
                requestAnimationFrame(animate);
            }
        };

        animate();
    }

    // 交互模式切换
    switchInteractionMode(mode) {
        const interactionMode = document.getElementById('interaction-mode');
        if (interactionMode) {
            const modeNames = {
                'touch': '触控模式',
                'voice': '语音模式',
                'gesture': '手势模式'
            };
            interactionMode.textContent = `交互: ${modeNames[mode]}`;
        }

        console.log(`🎮 切换到交互模式: ${mode}`);
    }

    // 标签页切换
    switchTab(tabName) {
        if (tabName === 'layers') {
            this.updateLayerDetails();
        }
        console.log(`📋 切换到标签页: ${tabName}`);
    }

    // 更新层级详情
    updateLayerDetails() {
        const layerDetails = document.getElementById('layer-details');
        if (!layerDetails) return;

        let html = '';
        Object.entries(this.layerData).reverse().forEach(([layerNum, data]) => {
            const isActive = this.focusedLayer === parseInt(layerNum);
            const activeClass = isActive ? 'active' : '';

            html += `
                <div class="layer-detail-card ${activeClass}" data-layer="${layerNum}">
                    <div class="layer-header">
                        <div class="layer-number" style="background-color: #${data.color.toString(16).padStart(6, '0')}">${layerNum}</div>
                        <div class="layer-info">
                            <h4>${data.name}</h4>
                            <p class="layer-english">${data.english}</p>
                        </div>
                        <div class="layer-status">
                            <div class="status-indicator ${this.isSimulating ? 'active' : ''}"></div>
                        </div>
                    </div>
                    <div class="layer-content">
                        <p class="layer-description">${data.description}</p>
                        <div class="layer-bio-analogy">
                            <strong>🧬 生物类比:</strong> ${data.bioAnalogy}
                        </div>
                        <div class="layer-protocols">
                            <strong>📡 协议:</strong>
                            <div class="protocol-tags">
                                ${data.protocols.map(protocol => `<span class="protocol-tag">${protocol}</span>`).join('')}
                            </div>
                        </div>
                        <div class="layer-devices">
                            <strong>🔧 设备:</strong>
                            <div class="device-list">
                                ${data.devices.map(device => `<div class="device-item"><i class="fas fa-microchip"></i> ${device}</div>`).join('')}
                            </div>
                        </div>
                        <div class="layer-metrics">
                            <div class="metric">
                                <span class="metric-label">活跃度</span>
                                <div class="metric-bar">
                                    <div class="metric-fill" style="width: ${Math.random() * 100}%"></div>
                                </div>
                            </div>
                            <div class="metric">
                                <span class="metric-label">数据流量</span>
                                <div class="metric-bar">
                                    <div class="metric-fill" style="width: ${Math.random() * 100}%"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        });

        layerDetails.innerHTML = html;

        // 添加层级卡片点击事件
        layerDetails.querySelectorAll('.layer-detail-card').forEach(card => {
            card.addEventListener('click', () => {
                const layerNum = parseInt(card.dataset.layer);
                this.focusOnLayer(layerNum);
                this.updateLayerDetails(); // 更新活跃状态
            });
        });

        // 添加协议标签点击事件
        layerDetails.querySelectorAll('.protocol-tag').forEach(tag => {
            tag.addEventListener('click', (e) => {
                e.stopPropagation();
                this.showProtocolInfo(tag.textContent, e);
            });
        });
    }

    // 显示协议信息
    showProtocolInfo(protocol, event) {
        const protocolInfo = {
            'HTTP': '超文本传输协议 - 用于Web浏览器和服务器之间的通信',
            'HTTPS': '安全超文本传输协议 - HTTP的安全版本，使用SSL/TLS加密',
            'TCP': '传输控制协议 - 提供可靠的、面向连接的数据传输',
            'UDP': '用户数据报协议 - 提供快速的、无连接的数据传输',
            'IP': '互联网协议 - 负责数据包的路由和寻址',
            'Ethernet': '以太网协议 - 局域网中最常用的数据链路层协议'
        };

        const info = protocolInfo[protocol] || `${protocol} - 网络协议`;

        // 创建临时提示框
        const tooltip = document.createElement('div');
        tooltip.className = 'protocol-tooltip';
        tooltip.textContent = info;
        tooltip.style.cssText = `
            position: fixed;
            background: var(--secondary-bg);
            color: var(--text-primary);
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 12px;
            z-index: 1000;
            max-width: 200px;
            box-shadow: var(--shadow-lg);
            border: 1px solid var(--border-color);
        `;

        document.body.appendChild(tooltip);

        // 定位提示框
        const rect = event.target.getBoundingClientRect();
        tooltip.style.left = rect.left + 'px';
        tooltip.style.top = (rect.bottom + 5) + 'px';

        // 3秒后移除
        setTimeout(() => {
            if (tooltip.parentNode) {
                tooltip.parentNode.removeChild(tooltip);
            }
        }, 3000);
    }

    // 更新实时指标
    updateMetrics() {
        const metrics = document.querySelectorAll('.metric-fill');
        metrics.forEach(fill => {
            const newWidth = Math.random() * 100;
            fill.style.width = newWidth + '%';

            // 根据数值设置颜色
            if (newWidth > 80) {
                fill.style.background = 'var(--success-color)';
            } else if (newWidth > 50) {
                fill.style.background = 'var(--warning-color)';
            } else {
                fill.style.background = 'var(--error-color)';
            }
        });
    }

    // 聚焦到特定层级
    focusOnLayer(layerNum) {
        this.focusedLayer = layerNum;
        const targetY = (layerNum - 1) * 3 + 3;

        this.animateCamera(
            { x: 20, y: targetY, z: 20 },
            { x: 0, y: targetY, z: 0 }
        );

        // 高亮对应层级
        this.layerMeshes.forEach(mesh => {
            if (mesh.userData.layerNum === layerNum) {
                mesh.material.emissiveIntensity = 0.5;
            } else {
                mesh.material.emissiveIntensity = 0.1;
            }
        });

        console.log(`🎯 聚焦到层级: ${layerNum}`);
    }

    // 3D场景点击事件
    onSceneClick(event) {
        if (!this.camera || !this.scene) return;

        const mouse = new THREE.Vector2();
        const rect = this.renderer.domElement.getBoundingClientRect();

        mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1;
        mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1;

        const raycaster = new THREE.Raycaster();
        raycaster.setFromCamera(mouse, this.camera);

        const intersects = raycaster.intersectObjects(this.layerMeshes);

        if (intersects.length > 0) {
            const clickedMesh = intersects[0].object;
            const layerNum = clickedMesh.userData.layerNum;

            this.focusOnLayer(layerNum);

            // 切换到层级标签页
            this.switchTab('layers');
            document.querySelectorAll('.tab-btn').forEach(t => t.classList.remove('active'));
            document.querySelector('[data-tab="layers"]').classList.add('active');
            document.querySelectorAll('.tab-panel').forEach(p => p.classList.remove('active'));
            document.querySelector('[data-tab="layers"]').classList.add('active');
        }
    }

    // 键盘快捷键处理
    handleKeyboard(event) {
        switch (event.key) {
            case '1':
            case '2':
            case '3':
            case '4':
            case '5':
            case '6':
            case '7':
                const layerNum = parseInt(event.key);
                this.focusOnLayer(layerNum);
                break;
            case ' ':
                event.preventDefault();
                this.toggleSimulation();
                break;
            case 'r':
            case 'R':
                this.resetSimulation();
                break;
            case 'f':
            case 'F':
                this.toggleFullscreen();
                break;
            case 'Escape':
                this.focusedLayer = null;
                this.switchView('overview');
                break;
        }
    }

    // 全屏切换
    toggleFullscreen() {
        if (!document.fullscreenElement) {
            document.documentElement.requestFullscreen().catch(err => {
                console.log(`无法进入全屏模式: ${err.message}`);
            });
        } else {
            document.exitFullscreen();
        }
    }

    // 显示设置面板
    openSettings() {
        // 切换到设置标签页
        this.switchTab('settings');
        document.querySelectorAll('.tab-btn').forEach(t => t.classList.remove('active'));
        document.querySelector('[data-tab="settings"]').classList.add('active');
        document.querySelectorAll('.tab-panel').forEach(p => p.classList.remove('active'));
        document.querySelector('[data-tab="settings"]').classList.add('active');
    }

    // 显示信息面板
    showInfo() {
        alert('Network Poetry - OSI 3D 生物仿生艺术模拟器\\n\\n这是一个交互式的OSI七层模型可视化工具，通过生物仿生的艺术表现形式，帮助理解网络协议的层次结构。\\n\\n快捷键：\\n- 数字键1-7：聚焦到对应层级\\n- 空格键：开始/暂停模拟\\n- R键：重置模拟\\n- F键：全屏模式\\n- ESC键：返回总览视角');
    }

    startRenderLoop() {
        const animate = () => {
            this.animationId = requestAnimationFrame(animate);

            // 更新控制器
            if (this.controls && this.controls.update) {
                this.controls.update();
            }

            // 更新动画
            this.updateAnimations();

            // 渲染
            if (this.composer) {
                this.composer.render();
            } else {
                this.renderer.render(this.scene, this.camera);
            }
        };

        animate();
        console.log('🎬 渲染循环已启动');
    }

    updateAnimations() {
        const time = performance.now() * 0.001;

        // 更新OSI层级动画
        this.layerMeshes.forEach(mesh => {
            const userData = mesh.userData;

            // 增强脉冲效果
            const pulseIntensity = Math.sin(time * 2 + userData.pulsePhase) * 0.15 + 0.85;
            mesh.scale.setScalar(pulseIntensity);

            // 分层旋转效果
            mesh.rotation.y += 0.005 * (userData.layerNum / 7);
            mesh.rotation.x = Math.sin(time * 0.5 + userData.pulsePhase) * 0.1;

            // 垂直浮动
            mesh.position.y = (userData.layerNum - 1) * 3 + 3 + Math.sin(time + userData.pulsePhase) * 0.5;

            // 材质发光强度变化
            if (mesh.material.emissiveIntensity !== undefined) {
                mesh.material.emissiveIntensity = 0.2 + Math.sin(time * 1.5 + userData.pulsePhase) * 0.1;
            }
        });

        // 更新动态光源
        if (this.dynamicLights) {
            this.dynamicLights.forEach((lightData, index) => {
                const light = lightData.light;
                const phase = lightData.phase + time * 0.5;

                // 光源颜色变化
                const hue = (phase + index * 0.3) % 1;
                light.color.setHSL(hue, 0.7, 0.6);

                // 光源位置动画
                light.position.x = lightData.originalPosition.x + Math.cos(phase) * 5;
                light.position.z = lightData.originalPosition.z + Math.sin(phase) * 5;
                light.position.y = lightData.originalPosition.y + Math.sin(phase * 2) * 3;

                // 光源强度变化
                light.intensity = 0.8 + Math.sin(phase * 3) * 0.4;
            });
        }

        // 更新粒子系统
        this.particleSystems.forEach(system => {
            const userData = system.userData;

            if (userData.type === 'floating') {
                // 浮动粒子动画
                const positions = system.geometry.attributes.position.array;
                const velocities = userData.velocities;

                for (let i = 0; i < positions.length; i += 3) {
                    positions[i] += velocities[i];
                    positions[i + 1] += velocities[i + 1];
                    positions[i + 2] += velocities[i + 2];

                    // 边界检查
                    if (Math.abs(positions[i]) > 100) velocities[i] *= -1;
                    if (positions[i + 1] > 100 || positions[i + 1] < 0) velocities[i + 1] *= -1;
                    if (Math.abs(positions[i + 2]) > 100) velocities[i + 2] *= -1;
                }
                system.geometry.attributes.position.needsUpdate = true;

            } else if (userData.type === 'dataStream') {
                // 数据流旋转
                system.rotation.y += 0.01;

            } else if (userData.type === 'energy') {
                // 能量粒子围绕层级旋转
                system.rotation.y += userData.rotationSpeed;

                // 粒子大小脉冲
                const scale = 1 + Math.sin(time * 3 + userData.layer) * 0.3;
                system.scale.setScalar(scale);
            }
        });

        // 更新性能统计
        this.updatePerformanceStats();

        // 定期更新指标（每2秒）
        if (Math.floor(time) % 2 === 0 && Math.floor(time) !== this.lastMetricUpdate) {
            this.updateMetrics();
            this.lastMetricUpdate = Math.floor(time);
        }
    }

    updatePerformanceStats() {
        const now = performance.now();
        this.stats.frameCount++;

        if (now - this.stats.lastTime >= 1000) {
            this.stats.fps = Math.round((this.stats.frameCount * 1000) / (now - this.stats.lastTime));
            this.stats.frameCount = 0;
            this.stats.lastTime = now;

            // 更新UI显示
            const fpsDisplay = document.getElementById('fps-display');
            if (fpsDisplay) {
                fpsDisplay.textContent = `${this.stats.fps} FPS`;
            }

            const fpsCounter = document.getElementById('fps-counter');
            if (fpsCounter) {
                fpsCounter.textContent = `${this.stats.fps} FPS`;
            }

            // 估算内存使用
            if (this.renderer && this.renderer.info) {
                const info = this.renderer.info;
                const memoryUsage = Math.round((info.memory.geometries + info.memory.textures) / 1024);

                const memoryDisplay = document.getElementById('memory-usage');
                if (memoryDisplay) {
                    memoryDisplay.textContent = `${memoryUsage}KB`;
                }
            }
        }
    }

    onWindowResize() {
        const container = document.getElementById('three-canvas');
        if (!container || !this.renderer || !this.camera) return;

        const rect = container.getBoundingClientRect();
        const width = rect.width || 800;
        const height = rect.height || 600;

        this.camera.aspect = width / height;
        this.camera.updateProjectionMatrix();
        this.renderer.setSize(width, height);

        if (this.composer) {
            this.composer.setSize(width, height);
        }
    }

    focusOnLayer(layerNum) {
        console.log(`🎯 聚焦到第${layerNum}层`);
        this.focusedLayer = layerNum;
    }

    toggleSimulation() {
        this.isSimulating = !this.isSimulating;

        // 更新按钮状态
        const btn = document.getElementById('start-simulation');
        if (btn) {
            const icon = btn.querySelector('i');
            const text = btn.querySelector('span');
            if (this.isSimulating) {
                if (icon) icon.className = 'fas fa-pause';
                if (text) text.textContent = '暂停生命流';
                btn.classList.remove('primary');
                btn.classList.add('warning');
            } else {
                if (icon) icon.className = 'fas fa-play';
                if (text) text.textContent = '启动生命流';
                btn.classList.remove('warning');
                btn.classList.add('primary');
            }
        }

        // 更新状态显示
        const simulationStatus = document.getElementById('simulation-status');
        if (simulationStatus) {
            simulationStatus.textContent = this.isSimulating ? '生态系统运行中' : '生态系统就绪';
        }

        // 更新控制器自动旋转
        if (this.controls) {
            this.controls.autoRotate = this.isSimulating;
        }

        // 更新粒子系统活跃度
        this.particleSystems.forEach(system => {
            if (system.userData.type === 'dataStream') {
                system.userData.active = this.isSimulating;
            }
        });

        console.log(`🎮 模拟状态: ${this.isSimulating ? '运行' : '暂停'}`);
    }

    resetSimulation() {
        this.isSimulating = false;
        this.focusedLayer = null;

        // 重置所有动画状态
        this.layerMeshes.forEach(mesh => {
            mesh.scale.setScalar(1);
            mesh.rotation.set(0, 0, 0);
            mesh.position.y = (mesh.userData.layerNum - 1) * 3 + 3;
            if (mesh.material.emissiveIntensity !== undefined) {
                mesh.material.emissiveIntensity = 0.1;
            }
        });

        // 重置相机位置
        this.switchView('overview');

        // 重置按钮状态
        const btn = document.getElementById('start-simulation');
        if (btn) {
            const icon = btn.querySelector('i');
            const text = btn.querySelector('span');
            if (icon) icon.className = 'fas fa-play';
            if (text) text.textContent = '启动生命流';
            btn.classList.remove('warning');
            btn.classList.add('primary');
        }

        // 重置状态显示
        const simulationStatus = document.getElementById('simulation-status');
        if (simulationStatus) {
            simulationStatus.textContent = '生态系统就绪';
        }

        // 重置控制器
        if (this.controls) {
            this.controls.autoRotate = false;
        }

        // 重置粒子系统
        this.particleSystems.forEach(system => {
            system.rotation.set(0, 0, 0);
            if (system.userData.type === 'dataStream') {
                system.userData.active = false;
            }
        });

        console.log('🔄 仿真已重置');
    }

    showError(message) {
        console.error(message);
        alert(message);
    }
}

// 全局可用
window.OSI3DSimulator = OSI3DSimulator;
