// OSI 3D 生物仿生艺术模拟器 - 优化版本
class OSI3DSimulator {
    constructor() {
        this.scene = null;
        this.camera = null;
        this.renderer = null;
        this.buildings = [];
        this.labels = [];
        this.isSimulating = false;
        this.labelsVisible = true;
        this.currentView = 'overview';
        this.animationId = null;
        
        // OSI层级数据配置
        this.layerData = {
            7: { 
                name: "应用层", 
                english: "Application Layer",
                color: 0xE6B3FF, 
                position: { x: 0, y: 42, z: 0 }, 
                deviceType: "用户应用程序",
                description: "为应用软件提供网络服务，如HTTP、FTP、SMTP等协议",
                protocols: ["HTTP", "HTTPS", "FTP", "SMTP", "DNS", "DHCP"]
            },
            6: { 
                name: "表示层", 
                english: "Presentation Layer",
                color: 0xFF6B9D, 
                position: { x: 0, y: 36, z: 0 }, 
                deviceType: "数据转换设备",
                description: "数据的表示、安全、压缩，负责数据格式转换、加密解密",
                protocols: ["SSL", "TLS", "JPEG", "MPEG", "GIF", "ASCII"]
            },
            5: { 
                name: "会话层", 
                english: "Session Layer",
                color: 0x00E5FF, 
                position: { x: 0, y: 30, z: 0 }, 
                deviceType: "会话管理器",
                description: "建立、管理、终止会话，处理会话检查点和恢复",
                protocols: ["NetBIOS", "RPC", "SQL", "NFS", "SMB"]
            },
            4: { 
                name: "传输层", 
                english: "Transport Layer",
                color: 0x69F0AE, 
                position: { x: 0, y: 24, z: 0 }, 
                deviceType: "传输控制设备",
                description: "提供端到端的可靠数据传输，负责错误检测和恢复",
                protocols: ["TCP", "UDP", "SPX", "SCTP"]
            },
            3: { 
                name: "网络层", 
                english: "Network Layer",
                color: 0xFFD54F, 
                position: { x: 0, y: 18, z: 0 }, 
                deviceType: "路由器",
                description: "负责数据包的路由选择和转发，实现网络互连",
                protocols: ["IP", "ICMP", "ARP", "RARP", "OSPF", "BGP"]
            },
            2: { 
                name: "数据链路层", 
                english: "Data Link Layer",
                color: 0xFF5722, 
                position: { x: 0, y: 12, z: 0 }, 
                deviceType: "交换机、网桥",
                description: "提供节点间的数据传输，错误检测和纠正",
                protocols: ["Ethernet", "PPP", "HDLC", "Frame Relay", "ATM"]
            },
            1: { 
                name: "物理层", 
                english: "Physical Layer",
                color: 0x9C27B0, 
                position: { x: 0, y: 6, z: 0 }, 
                deviceType: "物理传输介质",
                description: "定义物理设备标准，负责比特流的传输",
                protocols: ["RJ45", "光纤", "同轴电缆", "无线电波", "蓝牙"]
            }
        };
        
        this.init();
    }
    
    async init() {
        try {
            console.log('🚀 开始初始化OSI 3D模拟器...');
            this.updateProgress(10, "检测WebGL支持...");
            
            if (!this.checkWebGLSupport()) {
                throw new Error('当前浏览器不支持WebGL');
            }
            
            this.updateProgress(30, "构建3D场景...");
            await this.setupScene();
            
            this.updateProgress(60, "创建OSI模型...");
            this.createOSIModel();
            
            this.updateProgress(80, "初始化交互控制...");
            this.setupControls();
            this.bindEvents();
            
            this.updateProgress(100, "初始化完成！");
            
            setTimeout(() => {
                this.hideLoading();
                this.startAnimation();
            }, 1000);
            
        } catch (error) {
            console.error('❌ 初始化失败:', error);
            this.showError(error.message);
        }
    }
    
    checkWebGLSupport() {
        try {
            const canvas = document.createElement('canvas');
            const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
            if (!gl) return false;
            
            // 检查基本WebGL功能
            const renderer = gl.getParameter(gl.RENDERER);
            const vendor = gl.getParameter(gl.VENDOR);
            console.log(`✅ WebGL支持正常 - ${vendor}: ${renderer}`);
            return true;
        } catch (e) {
            console.error('WebGL检测失败:', e);
            return false;
        }
    }
    
    async setupScene() {
        const container = document.getElementById('three-canvas');
        if (!container) {
            throw new Error('找不到3D场景容器');
        }

        // 创建场景
        this.scene = new THREE.Scene();
        this.scene.background = new THREE.Color(0x0a0b1e);
        this.scene.fog = new THREE.Fog(0x0a0b1e, 50, 200);

        // 获取容器的实际尺寸
        const containerRect = container.getBoundingClientRect();
        const width = containerRect.width || container.clientWidth || 800;
        const height = containerRect.height || container.clientHeight || 600;

        console.log(`📐 场景容器尺寸: ${width}x${height}`);

        // 设置摄像机
        this.camera = new THREE.PerspectiveCamera(75, width / height, 0.1, 1000);
        this.camera.position.set(60, 50, 60);
        this.camera.lookAt(0, 25, 0);

        // 创建渲染器
        this.renderer = new THREE.WebGLRenderer({
            antialias: true,
            alpha: true,
            powerPreference: "high-performance"
        });

        this.renderer.setSize(width, height);
        this.renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
        this.renderer.shadowMap.enabled = true;
        this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;

        // 确保canvas正确添加到容器
        if (container.tagName.toLowerCase() === 'canvas') {
            // 如果容器本身就是canvas，替换它
            container.parentNode.replaceChild(this.renderer.domElement, container);
        } else {
            // 否则添加到容器中
            container.appendChild(this.renderer.domElement);
        }
        
        // 添加光照系统
        this.setupLighting();
        
        // 创建环境
        this.createEnvironment();
        
        // 设置鼠标控制
        this.setupMouseControls();
        
        return Promise.resolve();
    }
    
    setupLighting() {
        // 环境光
        const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
        this.scene.add(ambientLight);
        
        // 主光源
        const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
        directionalLight.position.set(50, 50, 25);
        directionalLight.castShadow = true;
        directionalLight.shadow.mapSize.width = 2048;
        directionalLight.shadow.mapSize.height = 2048;
        directionalLight.shadow.camera.near = 0.5;
        directionalLight.shadow.camera.far = 500;
        directionalLight.shadow.camera.left = -50;
        directionalLight.shadow.camera.right = 50;
        directionalLight.shadow.camera.top = 50;
        directionalLight.shadow.camera.bottom = -50;
        this.scene.add(directionalLight);
        
        // 辅助光源
        const pointLight = new THREE.PointLight(0x4facfe, 0.5, 100);
        pointLight.position.set(0, 40, 0);
        this.scene.add(pointLight);
        
        // 边缘光
        const rimLight = new THREE.DirectionalLight(0x4facfe, 0.3);
        rimLight.position.set(-50, 30, -25);
        this.scene.add(rimLight);
    }
    
    createEnvironment() {
        // 地面网格
        const groundGeometry = new THREE.PlaneGeometry(200, 200);
        const groundMaterial = new THREE.MeshLambertMaterial({
            color: 0x1a1a2e,
            transparent: true,
            opacity: 0.8
        });
        const ground = new THREE.Mesh(groundGeometry, groundMaterial);
        ground.rotation.x = -Math.PI / 2;
        ground.receiveShadow = true;
        this.scene.add(ground);
        
        // 网格线
        const gridHelper = new THREE.GridHelper(200, 40, 0x444444, 0x222222);
        gridHelper.material.transparent = true;
        gridHelper.material.opacity = 0.3;
        this.scene.add(gridHelper);
        
        // 背景粒子效果
        this.createBackgroundParticles();
    }
    
    createBackgroundParticles() {
        const particleCount = 500;
        const positions = new Float32Array(particleCount * 3);
        const colors = new Float32Array(particleCount * 3);
        
        const color1 = new THREE.Color(0x4facfe);
        const color2 = new THREE.Color(0xe6b3ff);
        
        for (let i = 0; i < particleCount; i++) {
            positions[i * 3] = (Math.random() - 0.5) * 400;
            positions[i * 3 + 1] = Math.random() * 100;
            positions[i * 3 + 2] = (Math.random() - 0.5) * 400;
            
            const color = Math.random() > 0.5 ? color1 : color2;
            colors[i * 3] = color.r;
            colors[i * 3 + 1] = color.g;
            colors[i * 3 + 2] = color.b;
        }
        
        const particleGeometry = new THREE.BufferGeometry();
        particleGeometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
        particleGeometry.setAttribute('color', new THREE.BufferAttribute(colors, 3));
        
        const particleMaterial = new THREE.PointsMaterial({
            size: 0.8,
            vertexColors: true,
            transparent: true,
            opacity: 0.6,
            blending: THREE.AdditiveBlending
        });
        
        this.particles = new THREE.Points(particleGeometry, particleMaterial);
        this.scene.add(this.particles);
    }
    
    createOSIModel() {
        Object.entries(this.layerData).forEach(([layerNum, data]) => {
            this.createLayerBuilding(parseInt(layerNum), data);
        });
        
        this.createConnections();
        console.log('✅ OSI模型创建完成');
    }
    
    createLayerBuilding(layerNum, data) {
        const { position, color, name } = data;
        
        // 主建筑
        const buildingGeometry = new THREE.BoxGeometry(8, 4, 8);
        const buildingMaterial = new THREE.MeshPhongMaterial({
            color: color,
            transparent: true,
            opacity: 0.8,
            emissive: color,
            emissiveIntensity: 0.1
        });
        
        const building = new THREE.Mesh(buildingGeometry, buildingMaterial);
        building.position.set(position.x, position.y, position.z);
        building.castShadow = true;
        building.receiveShadow = true;
        building.userData = { layerNum, type: 'building', name };
        
        this.scene.add(building);
        this.buildings.push(building);
        
        // 创建文字标签
        this.createTextLabel(layerNum, data);
        
        // 添加装饰效果
        this.createBuildingEffects(building, data);
    }
    
    createTextLabel(layerNum, data) {
        const canvas = document.createElement('canvas');
        const context = canvas.getContext('2d');
        canvas.width = 512;
        canvas.height = 256;
        
        // 背景
        const gradient = context.createLinearGradient(0, 0, 0, canvas.height);
        gradient.addColorStop(0, `#${data.color.toString(16).padStart(6, '0')}80`);
        gradient.addColorStop(1, `#${data.color.toString(16).padStart(6, '0')}20`);
        context.fillStyle = gradient;
        context.fillRect(0, 0, canvas.width, canvas.height);
        
        // 边框
        context.strokeStyle = `#${data.color.toString(16).padStart(6, '0')}`;
        context.lineWidth = 4;
        context.strokeRect(2, 2, canvas.width - 4, canvas.height - 4);
        
        // 文字
        context.fillStyle = '#ffffff';
        context.font = 'bold 36px Arial';
        context.textAlign = 'center';
        context.shadowColor = 'rgba(0,0,0,0.5)';
        context.shadowBlur = 4;
        context.fillText(`第${layerNum}层`, canvas.width / 2, 70);
        
        context.font = 'bold 28px Arial';
        context.fillStyle = `#${data.color.toString(16).padStart(6, '0')}`;
        context.fillText(data.name, canvas.width / 2, 110);
        
        context.font = '20px Arial';
        context.fillStyle = '#cccccc';
        context.fillText(data.english, canvas.width / 2, 140);
        
        context.font = '18px Arial';
        context.fillText(data.deviceType, canvas.width / 2, 170);
        
        const texture = new THREE.CanvasTexture(canvas);
        const spriteMaterial = new THREE.SpriteMaterial({ 
            map: texture,
            transparent: true,
            opacity: 0.9
        });
        const sprite = new THREE.Sprite(spriteMaterial);
        
        sprite.position.set(data.position.x, data.position.y + 8, data.position.z);
        sprite.scale.set(12, 6, 1);
        
        this.scene.add(sprite);
        this.labels.push(sprite);
    }
    
    createBuildingEffects(building, data) {
        // 发光底座
        const baseGeometry = new THREE.RingGeometry(4, 6, 32);
        const baseMaterial = new THREE.MeshBasicMaterial({
            color: data.color,
            transparent: true,
            opacity: 0.3,
            side: THREE.DoubleSide
        });
        const base = new THREE.Mesh(baseGeometry, baseMaterial);
        base.position.set(building.position.x, building.position.y - 2.5, building.position.z);
        base.rotation.x = -Math.PI / 2;
        this.scene.add(base);
        
        // 能量柱
        const pillarGeometry = new THREE.CylinderGeometry(0.2, 0.2, 8, 8);
        const pillarMaterial = new THREE.MeshBasicMaterial({
            color: data.color,
            emissive: data.color,
            emissiveIntensity: 0.5,
            transparent: true,
            opacity: 0.7
        });
        
        for (let i = 0; i < 4; i++) {
            const angle = (i / 4) * Math.PI * 2;
            const pillar = new THREE.Mesh(pillarGeometry, pillarMaterial.clone());
            pillar.position.set(
                building.position.x + Math.cos(angle) * 5,
                building.position.y,
                building.position.z + Math.sin(angle) * 5
            );
            this.scene.add(pillar);
        }
    }
    
    createConnections() {
        for (let layer = 1; layer < 7; layer++) {
            const startPos = this.layerData[layer].position;
            const endPos = this.layerData[layer + 1].position;
            
            // 主连接线
            const points = [
                new THREE.Vector3(startPos.x, startPos.y + 2, startPos.z),
                new THREE.Vector3(endPos.x, endPos.y - 2, endPos.z)
            ];
            
            const geometry = new THREE.BufferGeometry().setFromPoints(points);
            const material = new THREE.LineBasicMaterial({
                color: 0x4facfe,
                transparent: true,
                opacity: 0.8,
                linewidth: 3
            });
            
            const line = new THREE.Line(geometry, material);
            this.scene.add(line);
            
            // 数据流粒子
            this.createDataFlow(startPos, endPos);
        }
    }
    
    createDataFlow(startPos, endPos) {
        const particleCount = 20;
        const positions = new Float32Array(particleCount * 3);
        
        for (let i = 0; i < particleCount; i++) {
            const t = i / particleCount;
            const x = startPos.x + (endPos.x - startPos.x) * t;
            const y = startPos.y + (endPos.y - startPos.y) * t;
            const z = startPos.z + (endPos.z - startPos.z) * t;
            
            positions[i * 3] = x;
            positions[i * 3 + 1] = y;
            positions[i * 3 + 2] = z;
        }
        
        const particleGeometry = new THREE.BufferGeometry();
        particleGeometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
        
        const particleMaterial = new THREE.PointsMaterial({
            color: 0x4facfe,
            size: 0.5,
            transparent: true,
            opacity: 0.8,
            blending: THREE.AdditiveBlending
        });
        
        const dataFlow = new THREE.Points(particleGeometry, particleMaterial);
        this.scene.add(dataFlow);
    }
    
    setupMouseControls() {
        const canvas = this.renderer.domElement;
        let isMouseDown = false;
        let mouseX = 0, mouseY = 0;
        let cameraAngle = 0;
        let cameraHeight = 50;
        
        canvas.addEventListener('mousedown', (e) => {
            isMouseDown = true;
            mouseX = e.clientX;
            mouseY = e.clientY;
            canvas.style.cursor = 'grabbing';
        });
        
        canvas.addEventListener('mousemove', (e) => {
            if (!isMouseDown) return;
            
            const deltaX = e.clientX - mouseX;
            const deltaY = e.clientY - mouseY;
            
            cameraAngle += deltaX * 0.01;
            cameraHeight = Math.max(20, Math.min(80, cameraHeight + deltaY * 0.1));
            
            const radius = 80;
            this.camera.position.x = Math.cos(cameraAngle) * radius;
            this.camera.position.z = Math.sin(cameraAngle) * radius;
            this.camera.position.y = cameraHeight;
            this.camera.lookAt(0, 25, 0);
            
            mouseX = e.clientX;
            mouseY = e.clientY;
        });
        
        canvas.addEventListener('mouseup', () => {
            isMouseDown = false;
            canvas.style.cursor = 'grab';
        });
        
        canvas.addEventListener('wheel', (e) => {
            e.preventDefault();
            const zoomFactor = e.deltaY > 0 ? 1.1 : 0.9;
            this.camera.position.multiplyScalar(zoomFactor);
            
            // 限制缩放范围
            const distance = this.camera.position.length();
            if (distance < 30) {
                this.camera.position.normalize().multiplyScalar(30);
            } else if (distance > 200) {
                this.camera.position.normalize().multiplyScalar(200);
            }
            
            this.camera.lookAt(0, 25, 0);
        });
        
        canvas.style.cursor = 'grab';
    }
    
    setupControls() {
        // 视图控制按钮
        document.querySelectorAll('.view-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const viewType = e.currentTarget.dataset.view;
                this.changeView(viewType);
            });
        });
        
        // 模拟控制按钮
        const startBtn = document.getElementById('start-simulation');
        if (startBtn) {
            startBtn.addEventListener('click', () => this.startSimulation());
        }
        
        const resetBtn = document.getElementById('reset-simulation');
        if (resetBtn) {
            resetBtn.addEventListener('click', () => this.resetSimulation());
        }
        
        const toggleBtn = document.getElementById('toggle-labels');
        if (toggleBtn) {
            toggleBtn.addEventListener('click', () => this.toggleLabels());
        }
    }
    
    bindEvents() {
        // 窗口大小调整
        window.addEventListener('resize', () => this.onWindowResize());
        
        // 3D对象点击
        this.setupRaycasting();
        
        // 键盘快捷键
        document.addEventListener('keydown', (e) => {
            switch(e.key) {
                case '1': case '2': case '3': case '4': 
                case '5': case '6': case '7':
                    this.focusOnLayer(parseInt(e.key));
                    break;
                case 'r': case 'R':
                    this.changeView('overview');
                    break;
                case ' ':
                    e.preventDefault();
                    this.startSimulation();
                    break;
            }
        });
    }
    
    setupRaycasting() {
        const raycaster = new THREE.Raycaster();
        const mouse = new THREE.Vector2();
        
        this.renderer.domElement.addEventListener('click', (event) => {
            const rect = this.renderer.domElement.getBoundingClientRect();
            mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1;
            mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1;
            
            raycaster.setFromCamera(mouse, this.camera);
            const intersects = raycaster.intersectObjects(this.buildings);
            
            if (intersects.length > 0) {
                const object = intersects[0].object;
                const layerNum = object.userData.layerNum;
                this.focusOnLayer(layerNum);
                this.showLayerInfo(layerNum);
            }
        });
    }
    
    changeView(viewType) {
        this.currentView = viewType;
        
        // 更新按钮状态
        document.querySelectorAll('.view-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        const activeBtn = document.querySelector(`[data-view="${viewType}"]`);
        if (activeBtn) activeBtn.classList.add('active');
        
        // 切换相机视角
        const positions = {
            overview: { x: 60, y: 50, z: 60 },
            building: { x: 40, y: 35, z: 40 },
            datacenter: { x: 25, y: 25, z: 25 },
            network: { x: 80, y: 60, z: 80 }
        };
        
        const pos = positions[viewType] || positions.overview;
        this.animateCamera(pos.x, pos.y, pos.z, 0, 25, 0);
    }
    
    animateCamera(x, y, z, targetX, targetY, targetZ) {
        const duration = 2000;
        const startPos = this.camera.position.clone();
        const endPos = new THREE.Vector3(x, y, z);
        const startTime = Date.now();
        
        const animate = () => {
            const elapsed = Date.now() - startTime;
            const progress = Math.min(elapsed / duration, 1);
            const eased = this.easeInOutCubic(progress);
            
            this.camera.position.lerpVectors(startPos, endPos, eased);
            this.camera.lookAt(targetX, targetY, targetZ);
            
            if (progress < 1) {
                requestAnimationFrame(animate);
            }
        };
        
        animate();
    }
    
    easeInOutCubic(t) {
        return t < 0.5 ? 4 * t * t * t : (t - 1) * (2 * t - 2) * (2 * t - 2) + 1;
    }
    
    focusOnLayer(layerNum) {
        const layerData = this.layerData[layerNum];
        if (layerData) {
            const pos = layerData.position;
            this.animateCamera(
                pos.x + 20, 
                pos.y + 15, 
                pos.z + 20,
                pos.x,
                pos.y,
                pos.z
            );
            
            // 高亮选中层
            this.highlightLayer(layerNum);
        }
    }
    
    highlightLayer(layerNum) {
        this.buildings.forEach(building => {
            if (building.userData.layerNum === layerNum) {
                building.material.emissiveIntensity = 0.3;
            } else {
                building.material.emissiveIntensity = 0.1;
            }
        });
    }
    
    showLayerInfo(layerNum) {
        const data = this.layerData[layerNum];
        if (!data) return;
        
        // 更新信息面板
        const layerDetails = document.getElementById('layer-details');
        if (layerDetails) {
            layerDetails.innerHTML = `
                <div class="layer-detail-card">
                    <div class="layer-header">
                        <span class="layer-number">${layerNum}</span>
                        <div class="layer-info">
                            <h3>${data.name}</h3>
                            <p class="layer-english">${data.english}</p>
                        </div>
                    </div>
                    <div class="layer-content">
                        <div class="section">
                            <h4>📋 功能描述</h4>
                            <p>${data.description}</p>
                        </div>
                        <div class="section">
                            <h4>🔧 设备类型</h4>
                            <p>${data.deviceType}</p>
                        </div>
                        <div class="section">
                            <h4>🌐 相关协议</h4>
                            <div class="protocol-tags">
                                ${data.protocols.map(protocol => 
                                    `<span class="protocol-tag">${protocol}</span>`
                                ).join('')}
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }
    }
    
    async startSimulation() {
        if (this.isSimulating) return;
        
        this.isSimulating = true;
        console.log('🚀 开始OSI数据传输模拟...');
        
        // 更新UI状态
        const startBtn = document.getElementById('start-simulation');
        if (startBtn) {
            startBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 模拟中...';
            startBtn.disabled = true;
        }
        
        // 创建数据包
        const packet = this.createDataPacket();
        
        // 模拟数据下行传输 (7层 -> 1层)
        for (let layer = 7; layer >= 1; layer--) {
            await this.animatePacketToLayer(packet, layer);
            await this.sleep(1000);
        }
        
        // 网络传输
        await this.animateNetworkTransmission(packet);
        
        // 模拟数据上行传输 (1层 -> 7层)
        for (let layer = 1; layer <= 7; layer++) {
            await this.animatePacketToLayer(packet, layer);
            await this.sleep(1000);
        }
        
        // 清理和重置
        this.scene.remove(packet);
        this.isSimulating = false;
        
        if (startBtn) {
            startBtn.innerHTML = '<i class="fas fa-play"></i> 启动生命流';
            startBtn.disabled = false;
        }
        
        console.log('✅ 模拟完成');
    }
    
    createDataPacket() {
        const geometry = new THREE.SphereGeometry(1);
        const material = new THREE.MeshBasicMaterial({
            color: 0x00ff88,
            emissive: 0x00ff88,
            emissiveIntensity: 0.5,
            transparent: true,
            opacity: 0.8
        });
        
        const packet = new THREE.Mesh(geometry, material);
        packet.position.set(-30, 50, -20);
        this.scene.add(packet);
        
        // 添加光晕
        const glowGeometry = new THREE.SphereGeometry(1.5);
        const glowMaterial = new THREE.MeshBasicMaterial({
            color: 0x00ff88,
            transparent: true,
            opacity: 0.3
        });
        const glow = new THREE.Mesh(glowGeometry, glowMaterial);
        packet.add(glow);
        
        return packet;
    }
    
    async animatePacketToLayer(packet, layerNum) {
        const layerData = this.layerData[layerNum];
        const targetPos = new THREE.Vector3(
            layerData.position.x,
            layerData.position.y + 8,
            layerData.position.z
        );
        
        this.highlightLayer(layerNum);
        await this.animateObjectToPosition(packet, targetPos, 1500);
        this.showLayerProcessingEffect(layerNum);
    }
    
    showLayerProcessingEffect(layerNum) {
        const layerData = this.layerData[layerNum];
        const effectGeometry = new THREE.RingGeometry(6, 10, 32);
        const effectMaterial = new THREE.MeshBasicMaterial({
            color: layerData.color,
            transparent: true,
            opacity: 0.8,
            side: THREE.DoubleSide
        });
        
        const effect = new THREE.Mesh(effectGeometry, effectMaterial);
        effect.position.set(
            layerData.position.x,
            layerData.position.y + 0.5,
            layerData.position.z
        );
        effect.rotation.x = -Math.PI / 2;
        this.scene.add(effect);
        
        // 动画效果
        let scale = 0.5;
        const animate = () => {
            scale += 0.05;
            effect.scale.setScalar(scale);
            effect.material.opacity -= 0.02;
            
            if (effect.material.opacity > 0) {
                requestAnimationFrame(animate);
            } else {
                this.scene.remove(effect);
            }
        };
        animate();
    }
    
    async animateNetworkTransmission(packet) {
        const startPos = packet.position.clone();
        const endPos = new THREE.Vector3(30, 5, 30);
        await this.animateObjectToPosition(packet, endPos, 2000);
    }
    
    async animateObjectToPosition(object, targetPos, duration) {
        return new Promise(resolve => {
            const startPos = object.position.clone();
            const startTime = Date.now();
            
            const animate = () => {
                const elapsed = Date.now() - startTime;
                const progress = Math.min(elapsed / duration, 1);
                const eased = this.easeInOutCubic(progress);
                
                object.position.lerpVectors(startPos, targetPos, eased);
                
                if (progress < 1) {
                    requestAnimationFrame(animate);
                } else {
                    resolve();
                }
            };
            animate();
        });
    }
    
    resetSimulation() {
        this.isSimulating = false;
        
        // 重置建筑高亮
        this.buildings.forEach(building => {
            building.material.emissiveIntensity = 0.1;
        });
        
        // 重置按钮
        const startBtn = document.getElementById('start-simulation');
        if (startBtn) {
            startBtn.innerHTML = '<i class="fas fa-play"></i> 启动生命流';
            startBtn.disabled = false;
        }
        
        console.log('🔄 模拟重置');
    }
    
    toggleLabels() {
        this.labelsVisible = !this.labelsVisible;
        this.labels.forEach(label => {
            label.visible = this.labelsVisible;
        });
        
        const toggleBtn = document.getElementById('toggle-labels');
        if (toggleBtn) {
            const icon = this.labelsVisible ? 'fas fa-tags' : 'fas fa-eye-slash';
            toggleBtn.innerHTML = `<i class="${icon}"></i> 标签显示`;
        }
    }
    
    startAnimation() {
        const animate = () => {
            this.animationId = requestAnimationFrame(animate);
            
            // 粒子动画
            if (this.particles) {
                this.particles.rotation.y += 0.001;
                
                const positions = this.particles.geometry.attributes.position.array;
                for (let i = 1; i < positions.length; i += 3) {
                    positions[i] += Math.sin(Date.now() * 0.001 + i) * 0.01;
                }
                this.particles.geometry.attributes.position.needsUpdate = true;
            }
            
            // 建筑呼吸效果
            this.buildings.forEach((building, index) => {
                const pulse = Math.sin(Date.now() * 0.002 + index) * 0.05 + 0.95;
                building.material.opacity = pulse;
            });
            
            // 更新时间显示
            this.updateTimeDisplay();
            
            // 渲染场景
            try {
                this.renderer.render(this.scene, this.camera);
            } catch (error) {
                console.error('渲染错误:', error);
            }
        };
        
        animate();
    }
    
    updateTimeDisplay() {
        const timeElement = document.getElementById('simulation-time');
        if (timeElement) {
            const now = new Date();
            const timeString = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`;
            timeElement.textContent = timeString;
        }
        
        // 更新FPS计数器
        this.updateFPSCounter();
        
        // 更新内存使用情况
        this.updateMemoryUsage();
    }
    
    updateFPSCounter() {
        if (!this.fpsTimer) {
            this.fpsTimer = Date.now();
            this.frameCount = 0;
        }
        
        this.frameCount++;
        const elapsed = Date.now() - this.fpsTimer;
        
        if (elapsed >= 1000) {
            const fps = Math.round(this.frameCount * 1000 / elapsed);
            const fpsElement = document.getElementById('fps-counter');
            if (fpsElement) {
                fpsElement.textContent = `${fps} FPS`;
                
                // 根据FPS调整颜色
                if (fps >= 50) {
                    fpsElement.style.color = 'var(--success-color)';
                } else if (fps >= 30) {
                    fpsElement.style.color = 'var(--warning-color)';
                } else {
                    fpsElement.style.color = 'var(--danger-color)';
                }
            }
            
            this.fpsTimer = Date.now();
            this.frameCount = 0;
        }
    }
    
    updateMemoryUsage() {
        if (performance.memory) {
            const memory = performance.memory;
            const used = Math.round(memory.usedJSHeapSize / 1024 / 1024);
            const memoryElement = document.getElementById('memory-usage');
            if (memoryElement) {
                memoryElement.textContent = `${used}MB`;
                
                // 根据内存使用情况调整颜色
                if (used < 100) {
                    memoryElement.style.color = 'var(--success-color)';
                } else if (used < 200) {
                    memoryElement.style.color = 'var(--warning-color)';
                } else {
                    memoryElement.style.color = 'var(--danger-color)';
                }
            }
        }
    }
    
    onWindowResize() {
        const container = document.getElementById('three-canvas');
        if (!container || !this.renderer || !this.camera) return;

        // 获取容器的实际尺寸
        const containerRect = container.getBoundingClientRect();
        const width = containerRect.width || container.clientWidth || 800;
        const height = containerRect.height || container.clientHeight || 600;

        console.log(`🔄 窗口大小调整: ${width}x${height}`);

        // 更新摄像机宽高比
        this.camera.aspect = width / height;
        this.camera.updateProjectionMatrix();

        // 更新渲染器尺寸
        this.renderer.setSize(width, height);
        this.renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
    }
    
    updateProgress(percent, message) {
        const progressFill = document.querySelector('.progress-fill');
        const progressText = document.querySelector('.progress-text');
        const loadingDetails = document.querySelector('.loading-details');
        const progressIndicator = document.querySelector('.progress-indicator');
        const progressPercentage = document.querySelector('.progress-percentage');
        
        if (progressFill) progressFill.style.width = percent + '%';
        if (progressIndicator) progressIndicator.style.width = percent + '%';
        if (progressText) progressText.textContent = message;
        if (loadingDetails) loadingDetails.textContent = message;
        if (progressPercentage) progressPercentage.textContent = percent + '%';
    }
    
    hideLoading() {
        const startup = document.getElementById('startup-screen');
        const loading = document.getElementById('loading-overlay');
        
        if (startup) {
            startup.style.opacity = '0';
            setTimeout(() => startup.style.display = 'none', 800);
        }
        
        if (loading) {
            loading.style.opacity = '0';
            setTimeout(() => loading.style.display = 'none', 800);
        }
    }
    
    showError(message) {
        const startup = document.getElementById('startup-screen');
        if (startup) {
            startup.innerHTML = `
                <div style="text-align: center; color: #ef4444; padding: 40px;">
                    <h2>❌ 初始化失败</h2>
                    <p>${message}</p>
                    <div style="margin: 20px 0; text-align: left; max-width: 400px; margin-left: auto; margin-right: auto;">
                        <h3>解决方案：</h3>
                        <ul>
                            <li>✓ 更新浏览器到最新版本</li>
                            <li>✓ 启用硬件加速功能</li>
                            <li>✓ 使用Chrome、Firefox或Edge浏览器</li>
                            <li>✓ 检查显卡驱动是否最新</li>
                        </ul>
                    </div>
                    <button onclick="location.reload()" style="
                        margin-top: 20px; 
                        padding: 12px 24px; 
                        background: linear-gradient(45deg, #4facfe, #00f2fe); 
                        color: white; 
                        border: none; 
                        border-radius: 8px; 
                        cursor: pointer;
                        font-size: 16px;
                        font-weight: bold;
                    ">🔄 重新加载</button>
                </div>
            `;
        }
    }
    
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    // 检查Three.js是否加载
    if (typeof THREE === 'undefined') {
        console.error('❌ Three.js库加载失败');
        const startup = document.getElementById('startup-screen');
        if (startup) {
            startup.innerHTML = `
                <div style="text-align: center; color: #ef4444; padding: 40px;">
                    <h2>❌ Three.js库加载失败</h2>
                    <p>请检查网络连接后刷新页面</p>
                    <button onclick="location.reload()" style="
                        margin-top: 20px; 
                        padding: 12px 24px; 
                        background: linear-gradient(45deg, #4facfe, #00f2fe); 
                        color: white; 
                        border: none; 
                        border-radius: 8px; 
                        cursor: pointer;
                        font-size: 16px;
                    ">🔄 重新加载</button>
                </div>
            `;
        }
        return;
    }
    
    console.log(`🎯 Three.js版本: ${THREE.REVISION}`);
    console.log('🚀 启动OSI 3D生物仿生艺术模拟器...');
    
    // 创建模拟器实例
    window.osiSimulator = new OSI3DSimulator();
    
    // 初始化高级UI功能
    initAdvancedUI();
});

// 高级UI功能初始化
function initAdvancedUI() {
    initTabSwitching();
    initModalSystem();
    initAdvancedHints();
    initPanelToggle();
    initFullscreenMode();
    initInteractionModes();
}

// 标签页切换功能
function initTabSwitching() {
    const tabButtons = document.querySelectorAll('.tab-btn');
    const tabPanels = document.querySelectorAll('.tab-panel');
    
    tabButtons.forEach(button => {
        button.addEventListener('click', () => {
            const targetTab = button.dataset.tab;
            
            // 移除所有活动状态
            tabButtons.forEach(btn => btn.classList.remove('active'));
            tabPanels.forEach(panel => panel.classList.remove('active'));
            
            // 激活当前选中的标签
            button.classList.add('active');
            const targetPanel = document.querySelector(`[data-tab="${targetTab}"]`);
            if (targetPanel) {
                targetPanel.classList.add('active');
            }
            
            // 添加反馈动画
            button.style.transform = 'scale(0.95)';
            setTimeout(() => {
                button.style.transform = '';
            }, 150);
        });
    });
}

// 模态框系统
function initModalSystem() {
    const modal = document.getElementById('device-modal');
    const closeModal = document.getElementById('close-modal');
    const cancelBtn = document.getElementById('modal-cancel');
    
    if (closeModal) {
        closeModal.addEventListener('click', hideModal);
    }
    
    if (cancelBtn) {
        cancelBtn.addEventListener('click', hideModal);
    }
    
    if (modal) {
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                hideModal();
            }
        });
    }
    
    // ESC键关闭模态框
    document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape' && modal && modal.classList.contains('active')) {
            hideModal();
        }
    });
}

function showModal(title, content) {
    const modal = document.getElementById('device-modal');
    const titleElement = document.getElementById('device-title');
    const contentElement = document.getElementById('device-details');
    
    if (modal && titleElement && contentElement) {
        titleElement.textContent = title;
        contentElement.innerHTML = content;
        modal.classList.add('active');
        document.body.style.overflow = 'hidden';
    }
}

function hideModal() {
    const modal = document.getElementById('device-modal');
    if (modal) {
        modal.classList.remove('active');
        document.body.style.overflow = '';
    }
}

// 高级提示系统
function initAdvancedHints() {
    const hints = document.getElementById('advanced-hints');
    const toggle = hints?.querySelector('.hints-toggle');
    
    if (toggle) {
        toggle.addEventListener('click', () => {
            hints.classList.toggle('visible');
            const icon = toggle.querySelector('i');
            if (icon) {
                icon.classList.toggle('fa-angle-up');
                icon.classList.toggle('fa-angle-down');
            }
        });
    }
    
    // 延迟显示提示
    setTimeout(() => {
        if (hints) {
            hints.classList.add('visible');
        }
    }, 5000);
}

// 面板切换功能
function initPanelToggle() {
    const toggle = document.getElementById('panel-toggle');
    const panel = document.querySelector('.info-panel');
    
    if (toggle && panel) {
        toggle.addEventListener('click', () => {
            panel.classList.toggle('collapsed');
            const icon = toggle.querySelector('i');
            if (icon) {
                icon.classList.toggle('fa-angle-right');
                icon.classList.toggle('fa-angle-left');
            }
        });
    }
}

// 全屏模式
function initFullscreenMode() {
    const fullscreenBtn = document.getElementById('fullscreen-btn');
    
    if (fullscreenBtn) {
        fullscreenBtn.addEventListener('click', toggleFullscreen);
    }
    
    // 监听全屏状态变化
    document.addEventListener('fullscreenchange', updateFullscreenButton);
}

function toggleFullscreen() {
    if (!document.fullscreenElement) {
        document.documentElement.requestFullscreen().catch(err => {
            console.log(`Error attempting to enable fullscreen: ${err.message}`);
        });
    } else {
        document.exitFullscreen();
    }
}

function updateFullscreenButton() {
    const btn = document.getElementById('fullscreen-btn');
    if (btn) {
        const icon = btn.querySelector('i');
        if (document.fullscreenElement) {
            icon.className = 'fas fa-compress';
            btn.title = '退出全屏';
        } else {
            icon.className = 'fas fa-expand';
            btn.title = '全屏模式';
        }
    }
}

// 交互模式切换
function initInteractionModes() {
    const modes = document.querySelectorAll('.interaction-mode');
    
    modes.forEach(mode => {
        mode.addEventListener('click', () => {
            // 移除所有活动状态
            modes.forEach(m => m.classList.remove('active'));
            // 激活当前模式
            mode.classList.add('active');
            
            // 更新状态显示
            const modeText = mode.querySelector('span').textContent;
            const statusElement = document.getElementById('interaction-mode');
            if (statusElement) {
                statusElement.textContent = `交互: ${modeText}模式`;
            }
            
            // 添加反馈效果
            mode.style.transform = 'scale(0.95)';
            setTimeout(() => {
                mode.style.transform = '';
            }, 150);
        });
    });
}

// 交互反馈系统
class InteractionFeedback {
    constructor() {
        this.createFeedbackOverlay();
        this.toastQueue = [];
        this.isShowingToast = false;
    }
    
    createFeedbackOverlay() {
        const overlay = document.createElement('div');
        overlay.className = 'feedback-overlay';
        overlay.id = 'feedback-overlay';
        document.body.appendChild(overlay);
    }
    
    // 水波纹效果
    createRipple(x, y, color = 'rgba(79, 172, 254, 0.4)') {
        const overlay = document.getElementById('feedback-overlay');
        const ripple = document.createElement('div');
        ripple.className = 'ripple-effect';
        ripple.style.left = x + 'px';
        ripple.style.top = y + 'px';
        ripple.style.background = `radial-gradient(circle, ${color}, transparent)`;
        
        overlay.appendChild(ripple);
        
        ripple.addEventListener('animationend', () => {
            overlay.removeChild(ripple);
        });
    }
    
    // 显示Toast通知
    showToast(type, title, message, duration = 3000) {
        this.toastQueue.push({ type, title, message, duration });
        if (!this.isShowingToast) {
            this.processToastQueue();
        }
    }
    
    processToastQueue() {
        if (this.toastQueue.length === 0) {
            this.isShowingToast = false;
            return;
        }
        
        this.isShowingToast = true;
        const toast = this.toastQueue.shift();
        this.displayToast(toast);
    }
    
    displayToast({ type, title, message, duration }) {
        const toast = document.createElement('div');
        toast.className = `toast-notification ${type}`;
        
        const icons = {
            success: 'fas fa-check-circle',
            warning: 'fas fa-exclamation-triangle',
            error: 'fas fa-times-circle',
            info: 'fas fa-info-circle'
        };
        
        toast.innerHTML = `
            <div class="toast-header">
                <i class="${icons[type] || icons.info}"></i>
                <span>${title}</span>
            </div>
            <div class="toast-body">${message}</div>
            <button class="toast-close">
                <i class="fas fa-times"></i>
            </button>
        `;
        
        document.body.appendChild(toast);
        
        // 显示动画
        requestAnimationFrame(() => {
            toast.classList.add('show');
        });
        
        // 关闭按钮事件
        const closeBtn = toast.querySelector('.toast-close');
        closeBtn.addEventListener('click', () => {
            this.hideToast(toast);
        });
        
        // 自动隐藏
        setTimeout(() => {
            this.hideToast(toast);
        }, duration);
    }
    
    hideToast(toast) {
        toast.classList.remove('show');
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
            this.processToastQueue();
        }, 400);
    }
    
    // 成功反馈
    success(title, message) {
        this.showToast('success', title, message);
    }
    
    // 警告反馈
    warning(title, message) {
        this.showToast('warning', title, message);
    }
    
    // 错误反馈
    error(title, message) {
        this.showToast('error', title, message);
    }
    
    // 信息反馈
    info(title, message) {
        this.showToast('info', title, message);
    }
}

// 初始化反馈系统
window.feedback = new InteractionFeedback();

// 为主要交互添加反馈
document.addEventListener('click', (e) => {
    // 为按钮点击添加水波纹效果
    if (e.target.matches('button, .view-btn, .sim-btn, .tab-btn, .interaction-mode')) {
        const rect = e.target.getBoundingClientRect();
        const x = e.clientX;
        const y = e.clientY;
        window.feedback.createRipple(x, y);
    }
});

// 键盘快捷键系统
class KeyboardShortcuts {
    constructor() {
        this.shortcuts = {
            'KeyH': () => this.showHelp(),
            'KeyF': () => toggleFullscreen(),
            'Escape': () => this.hideAllModals(),
            'Space': (e) => {
                e.preventDefault();
                if (window.osiSimulator) {
                    window.osiSimulator.startSimulation();
                }
            }
        };
        
        this.init();
    }
    
    init() {
        document.addEventListener('keydown', (e) => {
            const key = e.code;
            if (this.shortcuts[key]) {
                this.shortcuts[key](e);
            }
            
            // 数字键快捷方式
            if (/Digit[1-7]/.test(key)) {
                const layerNum = parseInt(key.replace('Digit', ''));
                if (window.osiSimulator) {
                    window.osiSimulator.focusOnLayer(layerNum);
                    window.feedback.info('层级聚焦', `已聚焦到第${layerNum}层`);
                }
            }
        });
    }
    
    showHelp() {
        // 显示键盘快捷键帮助
        window.feedback.info('键盘快捷键', 'H: 帮助 | F: 全屏 | 空格: 启动模拟 | 1-7: 层级聚焦');
    }
    
    hideAllModals() {
        // 隐藏所有模态框
        const modals = document.querySelectorAll('.modal-overlay, .keyboard-shortcuts');
        modals.forEach(modal => {
            modal.classList.remove('active');
        });
        document.body.style.overflow = '';
    }
}

// 初始化键盘快捷键
new KeyboardShortcuts();

// 主题系统
class ThemeManager {
    constructor() {
        this.themes = {
            dark: {
                name: '深空模式',
                primary: '#0a0b1e',
                secondary: '#1e1b2e',
                accent: '#4facfe',
                text: '#f8fafc'
            },
            light: {
                name: '星云模式',
                primary: '#f8fafc',
                secondary: '#e2e8f0',
                accent: '#4facfe',
                text: '#1e293b'
            },
            ocean: {
                name: '深海模式',
                primary: '#0f172a',
                secondary: '#1e293b',
                accent: '#06b6d4',
                text: '#f1f5f9'
            },
            forest: {
                name: '森林模式',
                primary: '#14532d',
                secondary: '#166534',
                accent: '#22c55e',
                text: '#f0fdf4'
            },
            sunset: {
                name: '日落模式',
                primary: '#431407',
                secondary: '#7c2d12',
                accent: '#f97316',
                text: '#fff7ed'
            }
        };
        
        this.currentTheme = localStorage.getItem('osi-theme') || 'dark';
        this.init();
    }
    
    init() {
        this.applyTheme(this.currentTheme);
        this.bindEvents();
    }
    
    bindEvents() {
        // 设置按钮事件
        const settingsBtn = document.getElementById('settings-btn');
        if (settingsBtn) {
            settingsBtn.addEventListener('click', () => this.showThemeSelector());
        }
        
        // 快捷键切换主题
        document.addEventListener('keydown', (e) => {
            if (e.key === 'T' && e.shiftKey) {
                this.cycleTheme();
            }
        });
    }
    
    applyTheme(themeName) {
        const theme = this.themes[themeName];
        if (!theme) return;
        
        const root = document.documentElement;
        
        // 添加主题类名
        root.className = root.className.replace(/theme-\w+/g, '');
        root.classList.add(`theme-${themeName}`);
        
        // 动态更新CSS变量
        root.style.setProperty('--theme-primary', theme.primary);
        root.style.setProperty('--theme-secondary', theme.secondary);
        root.style.setProperty('--theme-accent', theme.accent);
        root.style.setProperty('--theme-text', theme.text);
        
        this.currentTheme = themeName;
        localStorage.setItem('osi-theme', themeName);
        
        // 显示主题切换反馈
        if (window.feedback) {
            window.feedback.success('主题切换', `已切换到${theme.name}`);
        }
        
        // 更新3D场景颜色（如果存在）
        if (window.osiSimulator) {
            this.updateSceneTheme(theme);
        }
    }
    
    updateSceneTheme(theme) {
        // 更新3D场景的颜色方案
        const simulator = window.osiSimulator;
        if (simulator.scene) {
            // 更新背景色
            const bgColor = new THREE.Color(theme.primary);
            simulator.scene.background = bgColor;
            
            // 更新雾效颜色
            if (simulator.scene.fog) {
                simulator.scene.fog.color = bgColor;
            }
            
            // 更新粒子颜色
            if (simulator.particles) {
                const colors = simulator.particles.geometry.attributes.color.array;
                const accentColor = new THREE.Color(theme.accent);
                
                for (let i = 0; i < colors.length; i += 3) {
                    colors[i] = accentColor.r;
                    colors[i + 1] = accentColor.g;
                    colors[i + 2] = accentColor.b;
                }
                simulator.particles.geometry.attributes.color.needsUpdate = true;
            }
        }
    }
    
    cycleTheme() {
        const themeNames = Object.keys(this.themes);
        const currentIndex = themeNames.indexOf(this.currentTheme);
        const nextIndex = (currentIndex + 1) % themeNames.length;
        this.applyTheme(themeNames[nextIndex]);
    }
    
    showThemeSelector() {
        const themeOptions = Object.entries(this.themes)
            .map(([key, theme]) => `
                <div class="theme-option ${key === this.currentTheme ? 'active' : ''}" 
                     data-theme="${key}">
                    <div class="theme-preview" style="
                        background: ${theme.primary};
                        border: 2px solid ${theme.accent};
                    "></div>
                    <div class="theme-info">
                        <div class="theme-name">${theme.name}</div>
                        <div class="theme-desc">${this.getThemeDescription(key)}</div>
                    </div>
                    ${key === this.currentTheme ? '<i class="fas fa-check"></i>' : ''}
                </div>
            `).join('');
        
        const content = `
            <div class="theme-selector">
                <h3>选择主题</h3>
                <div class="theme-grid">
                    ${themeOptions}
                </div>
                <div class="theme-actions">
                    <button class="theme-btn" onclick="themeManager.resetTheme()">
                        <i class="fas fa-undo"></i> 重置默认
                    </button>
                    <button class="theme-btn" onclick="themeManager.randomTheme()">
                        <i class="fas fa-random"></i> 随机主题
                    </button>
                </div>
                <div class="theme-shortcuts">
                    <p><kbd>Shift</kbd> + <kbd>T</kbd> 快速切换主题</p>
                </div>
            </div>
        `;
        
        if (window.showModal) {
            window.showModal('主题设置', content);
            
            // 绑定主题选择事件
            setTimeout(() => {
                document.querySelectorAll('.theme-option').forEach(option => {
                    option.addEventListener('click', () => {
                        const themeName = option.dataset.theme;
                        this.applyTheme(themeName);
                        if (window.hideModal) window.hideModal();
                    });
                });
            }, 100);
        }
    }
    
    getThemeDescription(themeName) {
        const descriptions = {
            dark: '经典深空主题，专注护眼',
            light: '明亮星云主题，清新自然',
            ocean: '深海蓝调，宁静深邃',
            forest: '森林绿意，生机盎然',
            sunset: '日落橙红，温暖活力'
        };
        return descriptions[themeName] || '';
    }
    
    resetTheme() {
        this.applyTheme('dark');
        if (window.hideModal) window.hideModal();
    }
    
    randomTheme() {
        const themeNames = Object.keys(this.themes);
        const randomTheme = themeNames[Math.floor(Math.random() * themeNames.length)];
        this.applyTheme(randomTheme);
        if (window.hideModal) window.hideModal();
    }
}

// 初始化主题管理器
window.themeManager = new ThemeManager();