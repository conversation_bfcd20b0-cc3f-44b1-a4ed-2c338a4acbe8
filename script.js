// OSI 3D 生物仿生艺术模拟器 - 主控制器
class OSI3DSimulator {
    constructor() {
        // 核心Three.js对象
        this.scene = null;
        this.camera = null;
        this.renderer = null;
        
        // 状态管理
        this.isSimulating = false;
        this.currentView = 'overview';
        this.animationId = null;
        this.focusedLayer = null;
        
        // 3D对象集合
        this.layerMeshes = [];
        this.particleSystems = [];
        this.dataPackets = [];
        
        // 性能监控
        this.stats = {
            fps: 0,
            frameCount: 0,
            lastTime: performance.now(),
            memory: 0
        };
        
        // OSI层级数据
        this.layerData = {
            7: { 
                name: "应用层", 
                english: "Application", 
                color: 0xE6B3FF, 
                description: "神经网络中枢",
                protocols: ["HTTP", "HTTPS", "FTP", "SMTP", "DNS"],
                devices: ["Web服务器", "邮件服务器", "DNS服务器"],
                bioAnalogy: "大脑皮层"
            },
            6: { 
                name: "表示层", 
                english: "Presentation", 
                color: 0xFF6B9D, 
                description: "转译生物器官",
                protocols: ["SSL", "TLS", "JPEG", "MPEG", "ASCII"],
                devices: ["加密设备", "压缩器", "格式转换器"],
                bioAnalogy: "感觉器官"
            },
            5: { 
                name: "会话层", 
                english: "Session", 
                color: 0x00E5FF, 
                description: "对话细胞集群",
                protocols: ["NetBIOS", "RPC", "SQL", "NFS"],
                devices: ["会话管理器", "认证服务器"],
                bioAnalogy: "神经突触"
            },
            4: { 
                name: "传输层", 
                english: "Transport", 
                color: 0x69F0AE, 
                description: "循环系统节点",
                protocols: ["TCP", "UDP", "SPX", "SCTP"],
                devices: ["网关", "防火墙", "代理服务器"],
                bioAnalogy: "循环系统"
            },
            3: { 
                name: "网络层", 
                english: "Network", 
                color: 0xFFD54F, 
                description: "导航蜂群",
                protocols: ["IP", "ICMP", "IGMP", "ARP"],
                devices: ["路由器", "三层交换机"],
                bioAnalogy: "神经网络"
            },
            2: { 
                name: "数据链路层", 
                english: "Data Link", 
                color: 0xFF5722, 
                description: "微生物群落",
                protocols: ["Ethernet", "PPP", "Frame Relay"],
                devices: ["交换机", "网桥", "网卡"],
                bioAnalogy: "细胞膜"
            },
            1: { 
                name: "物理层", 
                english: "Physical", 
                color: 0x9C27B0, 
                description: "菌丝网络基质",
                protocols: ["电信号", "光信号", "无线信号"],
                devices: ["集线器", "中继器", "网线", "光纤"],
                bioAnalogy: "DNA双螺旋"
            }
        };
        
        this.init();
    }
    
    async init() {
        try {
            console.log('🚀 开始初始化OSI 3D模拟器...');
            
            // 显示启动画面
            this.showStartupScreen();
            
            // 检查WebGL支持
            if (!this.checkWebGLSupport()) {
                throw new Error('当前浏览器不支持WebGL');
            }
            
            // 初始化3D场景
            await this.setupScene();
            
            // 创建OSI层级
            this.createOSILayers();
            
            // 生成层级标签
            this.generateLayerLabels();
            
            // 初始化信息面板
            this.initializeInfoPanels();
            
            // 设置事件监听
            this.setupEventListeners();
            
            // 开始渲染循环
            this.startRenderLoop();
            
            // 隐藏启动画面
            setTimeout(() => this.hideStartupScreen(), 2000);
            
            console.log('✅ OSI 3D模拟器初始化完成');
            
        } catch (error) {
            console.error('❌ 初始化失败:', error);
            this.showError('初始化失败: ' + error.message);
        }
    }
    
    checkWebGLSupport() {
        try {
            const canvas = document.createElement('canvas');
            const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
            if (!gl) return false;
            
            const renderer = gl.getParameter(gl.RENDERER);
            const vendor = gl.getParameter(gl.VENDOR);
            console.log(`✅ WebGL支持正常 - ${vendor}: ${renderer}`);
            return true;
        } catch (e) {
            console.error('WebGL检测失败:', e);
            return false;
        }
    }
    
    showStartupScreen() {
        const progressFill = document.getElementById('progress-fill');
        const progressText = document.getElementById('progress-text');
        
        let progress = 0;
        const interval = setInterval(() => {
            progress += Math.random() * 15;
            if (progress > 100) progress = 100;
            
            if (progressFill) progressFill.style.width = progress + '%';
            
            if (progress >= 100) {
                clearInterval(interval);
                if (progressText) progressText.textContent = '初始化完成！';
            }
        }, 200);
    }
    
    hideStartupScreen() {
        const startupScreen = document.getElementById('startup-screen');
        if (startupScreen) {
            startupScreen.classList.add('hidden');
        }
    }
    
    async setupScene() {
        const container = document.getElementById('three-canvas');
        if (!container) {
            throw new Error('找不到3D场景容器');
        }
        
        const rect = container.getBoundingClientRect();
        const width = rect.width || 800;
        const height = rect.height || 600;
        
        console.log(`📐 场景容器尺寸: ${width}x${height}`);
        
        // 创建场景
        this.scene = new THREE.Scene();
        this.scene.background = new THREE.Color(0x0a0b1e);
        this.scene.fog = new THREE.Fog(0x0a0b1e, 50, 200);
        
        // 创建摄像机
        this.camera = new THREE.PerspectiveCamera(75, width / height, 0.1, 1000);
        this.camera.position.set(40, 35, 40);
        this.camera.lookAt(0, 10, 0);
        
        // 创建渲染器
        this.renderer = new THREE.WebGLRenderer({ 
            antialias: true,
            alpha: true,
            powerPreference: "high-performance"
        });
        this.renderer.setSize(width, height);
        this.renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
        this.renderer.shadowMap.enabled = true;
        this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
        
        container.appendChild(this.renderer.domElement);
        
        // 添加光照系统
        this.setupLighting();
        
        // 创建环境
        this.createEnvironment();
        
        // 设置控制
        this.setupControls();
    }
    
    setupLighting() {
        // 环境光
        const ambientLight = new THREE.AmbientLight(0x404040, 0.4);
        this.scene.add(ambientLight);
        
        // 主方向光
        const mainLight = new THREE.DirectionalLight(0xffffff, 1.2);
        mainLight.position.set(50, 100, 50);
        mainLight.castShadow = true;
        mainLight.shadow.mapSize.width = 2048;
        mainLight.shadow.mapSize.height = 2048;
        this.scene.add(mainLight);
        
        // 辅助光源
        const fillLight = new THREE.DirectionalLight(0x4facfe, 0.3);
        fillLight.position.set(-30, 20, -30);
        this.scene.add(fillLight);
        
        // 为每个OSI层添加特色光源
        Object.entries(this.layerData).forEach(([layerNum, data]) => {
            const pointLight = new THREE.PointLight(data.color, 0.8, 20);
            pointLight.position.set(0, (parseInt(layerNum) - 1) * 3 + 3, 0);
            pointLight.userData = { 
                originalIntensity: 0.8,
                pulsePhase: Math.random() * Math.PI * 2,
                layerNum: layerNum
            };
            this.scene.add(pointLight);
        });
        
        console.log('💡 光照系统设置完成');
    }
    
    createEnvironment() {
        // 创建地面
        const groundGeometry = new THREE.PlaneGeometry(100, 100);
        const groundMaterial = new THREE.MeshLambertMaterial({ 
            color: 0x1a1b2e,
            transparent: true,
            opacity: 0.8
        });
        
        const ground = new THREE.Mesh(groundGeometry, groundMaterial);
        ground.rotation.x = -Math.PI / 2;
        ground.position.y = -2;
        ground.receiveShadow = true;
        
        // 添加网格线
        const gridHelper = new THREE.GridHelper(100, 50, 0x4facfe, 0x4facfe);
        gridHelper.material.opacity = 0.2;
        gridHelper.material.transparent = true;
        gridHelper.position.y = -1.9;
        
        this.scene.add(ground);
        this.scene.add(gridHelper);
        
        // 创建背景粒子
        this.createBackgroundParticles();
        
        console.log('🌍 环境创建完成');
    }
    
    createBackgroundParticles() {
        const particleCount = 300;
        const positions = new Float32Array(particleCount * 3);
        const colors = new Float32Array(particleCount * 3);
        
        for (let i = 0; i < particleCount; i++) {
            const i3 = i * 3;
            
            positions[i3] = (Math.random() - 0.5) * 200;
            positions[i3 + 1] = Math.random() * 100;
            positions[i3 + 2] = (Math.random() - 0.5) * 200;
            
            const color = new THREE.Color();
            color.setHSL(Math.random() * 0.3 + 0.5, 0.7, 0.5);
            colors[i3] = color.r;
            colors[i3 + 1] = color.g;
            colors[i3 + 2] = color.b;
        }
        
        const particleGeometry = new THREE.BufferGeometry();
        particleGeometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
        particleGeometry.setAttribute('color', new THREE.BufferAttribute(colors, 3));
        
        const particleMaterial = new THREE.PointsMaterial({
            size: 0.5,
            vertexColors: true,
            transparent: true,
            opacity: 0.6,
            blending: THREE.AdditiveBlending
        });
        
        const particles = new THREE.Points(particleGeometry, particleMaterial);
        this.scene.add(particles);
        this.particleSystems.push(particles);
    }

    createOSILayers() {
        Object.entries(this.layerData).forEach(([layerNum, data]) => {
            this.createLayerMesh(parseInt(layerNum), data);
        });

        console.log('🏗️ OSI层级创建完成');
    }

    createLayerMesh(layerNum, data) {
        // 创建有机形状的几何体
        const geometry = new THREE.BoxGeometry(8, 2, 8, 4, 2, 4);

        // 添加噪声变形，创造有机感
        const positions = geometry.attributes.position;
        for (let i = 0; i < positions.count; i++) {
            const x = positions.getX(i);
            const y = positions.getY(i);
            const z = positions.getZ(i);

            const noise = (Math.sin(x * 0.5) + Math.cos(z * 0.5)) * 0.2;
            positions.setY(i, y + noise);
        }
        geometry.computeVertexNormals();

        // 创建材质
        const material = new THREE.MeshPhongMaterial({
            color: data.color,
            transparent: true,
            opacity: 0.7,
            shininess: 100,
            specular: 0x4facfe,
            emissive: data.color,
            emissiveIntensity: 0.1
        });

        const mesh = new THREE.Mesh(geometry, material);
        mesh.position.y = (layerNum - 1) * 3;
        mesh.castShadow = true;
        mesh.receiveShadow = true;
        mesh.userData = {
            layerNum,
            data,
            type: 'osiLayer',
            originalPosition: mesh.position.clone(),
            pulsePhase: Math.random() * Math.PI * 2
        };

        this.scene.add(mesh);
        this.layerMeshes.push(mesh);

        // 创建发光边框
        const edges = new THREE.EdgesGeometry(geometry);
        const lineMaterial = new THREE.LineBasicMaterial({
            color: data.color,
            transparent: true,
            opacity: 0.8
        });

        const wireframe = new THREE.LineSegments(edges, lineMaterial);
        wireframe.position.copy(mesh.position);
        this.scene.add(wireframe);

        // 创建层级装饰
        this.createLayerDecorations(mesh, data);
    }

    createLayerDecorations(mesh, data) {
        // 为每个层级添加装饰元素
        const decorationCount = 3;
        for (let i = 0; i < decorationCount; i++) {
            const decorGeometry = new THREE.SphereGeometry(0.3, 8, 6);
            const decorMaterial = new THREE.MeshPhongMaterial({
                color: data.color,
                transparent: true,
                opacity: 0.6,
                emissive: data.color,
                emissiveIntensity: 0.2
            });

            const decoration = new THREE.Mesh(decorGeometry, decorMaterial);

            const angle = (i / decorationCount) * Math.PI * 2;
            const radius = 6;
            decoration.position.x = mesh.position.x + Math.cos(angle) * radius;
            decoration.position.y = mesh.position.y;
            decoration.position.z = mesh.position.z + Math.sin(angle) * radius;

            decoration.userData = {
                type: 'decoration',
                parentLayer: mesh.userData.layerNum,
                orbitAngle: angle,
                orbitSpeed: 0.01 + Math.random() * 0.02
            };

            this.scene.add(decoration);
        }
    }

    generateLayerLabels() {
        const labelsContainer = document.getElementById('layer-labels');
        if (!labelsContainer) return;

        labelsContainer.innerHTML = '';

        const positions = [
            { top: '10%', left: '20%' },
            { top: '20%', left: '70%' },
            { top: '30%', left: '25%' },
            { top: '40%', left: '65%' },
            { top: '50%', left: '30%' },
            { top: '60%', left: '60%' },
            { top: '70%', left: '35%' }
        ];

        Object.entries(this.layerData).forEach(([layerNum, data], index) => {
            const label = document.createElement('div');
            label.className = 'layer-label';
            label.dataset.layer = layerNum;
            label.style.top = positions[index].top;
            label.style.left = positions[index].left;

            label.innerHTML = `
                <div class="label-content">
                    <div class="label-header">
                        <span class="label-number">${layerNum}</span>
                    </div>
                    <div class="label-info">
                        <div class="label-name">${data.name}</div>
                        <div class="label-english">${data.english}</div>
                        <div class="label-description">${data.description}</div>
                    </div>
                </div>
            `;

            label.addEventListener('click', () => this.focusOnLayer(layerNum));
            labelsContainer.appendChild(label);
        });
    }

    initializeInfoPanels() {
        // 初始化层级详情
        this.generateLayerDetailsList();

        console.log('📊 信息面板初始化完成');
    }

    generateLayerDetailsList() {
        const layerDetailsContainer = document.getElementById('layer-details');
        if (!layerDetailsContainer) return;

        let detailsHTML = '';
        Object.entries(this.layerData).reverse().forEach(([layerNum, data]) => {
            detailsHTML += `
                <div style="margin-bottom: 1rem; padding: 1rem; background: rgba(255,255,255,0.05); border-radius: 8px; cursor: pointer;" onclick="window.osiSimulator.focusOnLayer(${layerNum})">
                    <div style="display: flex; align-items: center; gap: 1rem; margin-bottom: 0.5rem;">
                        <span style="width: 30px; height: 30px; background: #${data.color.toString(16).padStart(6, '0')}; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-weight: 600;">${layerNum}</span>
                        <div>
                            <h4 style="margin: 0; color: #ffffff;">${data.name}</h4>
                            <p style="margin: 0; font-size: 0.875rem; color: #94a3b8;">${data.english}</p>
                        </div>
                    </div>
                    <p style="margin: 0; font-size: 0.875rem; color: #cbd5e1;">${data.description}</p>
                    <div style="margin-top: 0.5rem;">
                        <small style="color: #94a3b8;">生物类比: ${data.bioAnalogy}</small>
                    </div>
                </div>
            `;
        });

        layerDetailsContainer.innerHTML = detailsHTML;
    }

    setupControls() {
        const canvas = this.renderer.domElement;
        let isMouseDown = false;
        let mouseX = 0, mouseY = 0;
        let cameraAngle = 0;
        let cameraRadius = 60;
        let cameraHeight = 35;

        canvas.addEventListener('mousedown', (e) => {
            isMouseDown = true;
            mouseX = e.clientX;
            mouseY = e.clientY;
            canvas.style.cursor = 'grabbing';
        });

        canvas.addEventListener('mousemove', (e) => {
            if (!isMouseDown) return;

            const deltaX = e.clientX - mouseX;
            const deltaY = e.clientY - mouseY;

            cameraAngle += deltaX * 0.01;
            cameraHeight += deltaY * 0.1;
            cameraHeight = Math.max(10, Math.min(80, cameraHeight));

            this.updateCameraPosition(cameraAngle, cameraRadius, cameraHeight);

            mouseX = e.clientX;
            mouseY = e.clientY;
        });

        canvas.addEventListener('mouseup', () => {
            isMouseDown = false;
            canvas.style.cursor = 'grab';
        });

        canvas.addEventListener('wheel', (e) => {
            e.preventDefault();
            cameraRadius += e.deltaY * 0.1;
            cameraRadius = Math.max(20, Math.min(100, cameraRadius));

            this.updateCameraPosition(cameraAngle, cameraRadius, cameraHeight);
        });

        // 双击聚焦
        canvas.addEventListener('dblclick', (e) => {
            this.handleDoubleClick(e);
        });

        canvas.style.cursor = 'grab';
    }

    updateCameraPosition(angle, radius, height) {
        this.camera.position.x = Math.cos(angle) * radius;
        this.camera.position.y = height;
        this.camera.position.z = Math.sin(angle) * radius;
        this.camera.lookAt(0, 10, 0);
    }

    handleDoubleClick(event) {
        const raycaster = new THREE.Raycaster();
        const mouse = new THREE.Vector2();

        const rect = this.renderer.domElement.getBoundingClientRect();
        mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1;
        mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1;

        raycaster.setFromCamera(mouse, this.camera);
        const intersects = raycaster.intersectObjects(this.layerMeshes);

        if (intersects.length > 0) {
            const layerNum = intersects[0].object.userData.layerNum;
            this.focusOnLayer(layerNum);
        }
    }

    setupEventListeners() {
        // 窗口大小调整
        window.addEventListener('resize', () => this.onWindowResize());

        // 仿真控制按钮
        document.getElementById('start-simulation')?.addEventListener('click', () => {
            this.toggleSimulation();
        });

        document.getElementById('reset-simulation')?.addEventListener('click', () => {
            this.resetSimulation();
        });

        // 标签页切换
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const targetTab = e.target.closest('.tab-btn').dataset.tab;
                this.switchTab(targetTab);
            });
        });

        // 视角控制
        document.querySelectorAll('.view-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                document.querySelectorAll('.view-btn').forEach(b => b.classList.remove('active'));
                e.target.closest('.view-btn').classList.add('active');

                const view = e.target.closest('.view-btn').dataset.view;
                this.switchView(view);
            });
        });

        // 交互模式切换
        document.querySelectorAll('.interaction-mode').forEach(mode => {
            mode.addEventListener('click', (e) => {
                document.querySelectorAll('.interaction-mode').forEach(m => m.classList.remove('active'));
                e.target.closest('.interaction-mode').classList.add('active');
            });
        });

        // 全屏按钮
        document.getElementById('fullscreen-btn')?.addEventListener('click', () => {
            this.toggleFullscreen();
        });
    }

    startRenderLoop() {
        const animate = () => {
            this.animationId = requestAnimationFrame(animate);

            // 更新性能统计
            this.updatePerformanceStats();

            // 更新动画
            this.updateAnimations();

            this.renderer.render(this.scene, this.camera);
        };

        animate();
        console.log('🎬 渲染循环已启动');
    }

    updatePerformanceStats() {
        const now = performance.now();
        this.stats.frameCount++;

        if (now - this.stats.lastTime >= 1000) {
            this.stats.fps = Math.round((this.stats.frameCount * 1000) / (now - this.stats.lastTime));
            this.stats.frameCount = 0;
            this.stats.lastTime = now;

            if (performance.memory) {
                this.stats.memory = Math.round(performance.memory.usedJSHeapSize / 1048576);
            }

            // 更新UI显示
            const fpsElement = document.getElementById('fps-display');
            if (fpsElement) {
                fpsElement.textContent = `${this.stats.fps} FPS`;
            }

            const memoryElement = document.getElementById('memory-usage');
            if (memoryElement) {
                memoryElement.textContent = `${this.stats.memory}MB`;
            }
        }
    }

    updateAnimations() {
        const time = performance.now() * 0.001;

        // 更新OSI层级动画
        this.layerMeshes.forEach(mesh => {
            const userData = mesh.userData;

            // 脉冲效果
            const pulseIntensity = Math.sin(time * 2 + userData.pulsePhase) * 0.1 + 0.9;
            mesh.scale.setScalar(pulseIntensity);

            // 轻微旋转
            mesh.rotation.y += 0.005 * (userData.layerNum / 7);

            // 材质发光强度变化
            if (mesh.material.emissiveIntensity !== undefined) {
                mesh.material.emissiveIntensity = 0.1 + Math.sin(time + userData.pulsePhase) * 0.05;
            }
        });

        // 更新粒子系统
        this.particleSystems.forEach(system => {
            system.rotation.y += 0.001;
        });

        // 更新装饰元素
        this.scene.children.forEach(child => {
            if (child.userData.type === 'decoration') {
                const userData = child.userData;
                userData.orbitAngle += userData.orbitSpeed;

                const parentLayer = this.layerMeshes.find(m => m.userData.layerNum === userData.parentLayer);
                if (parentLayer) {
                    const radius = 6;
                    child.position.x = parentLayer.position.x + Math.cos(userData.orbitAngle) * radius;
                    child.position.z = parentLayer.position.z + Math.sin(userData.orbitAngle) * radius;
                    child.position.y = parentLayer.position.y + Math.sin(time * 3 + userData.orbitAngle) * 0.5;
                }
            }
        });

        // 更新光照效果
        this.scene.children.forEach(child => {
            if (child.type === 'PointLight' && child.userData.layerNum) {
                const userData = child.userData;
                child.intensity = userData.originalIntensity + Math.sin(time * 2 + userData.pulsePhase) * 0.3;
            }
        });
    }

    onWindowResize() {
        const container = document.getElementById('three-canvas');
        if (!container || !this.renderer || !this.camera) return;

        const rect = container.getBoundingClientRect();
        const width = rect.width || 800;
        const height = rect.height || 600;

        this.camera.aspect = width / height;
        this.camera.updateProjectionMatrix();
        this.renderer.setSize(width, height);
    }

    focusOnLayer(layerNum) {
        console.log(`🎯 聚焦到第${layerNum}层`);
        this.focusedLayer = layerNum;

        const layerData = this.layerData[layerNum];
        if (layerData) {
            // 更新信息面板
            this.updateLayerInfo(layerNum, layerData);

            // 高亮层级
            this.highlightLayer(layerNum);
        }
    }

    highlightLayer(layerNum) {
        this.layerMeshes.forEach(mesh => {
            if (mesh.userData.layerNum == layerNum) {
                mesh.material.emissiveIntensity = 0.3;
                mesh.scale.setScalar(1.1);
            } else {
                mesh.material.emissiveIntensity = 0.05;
                mesh.scale.setScalar(0.9);
            }
        });
    }

    updateLayerInfo(layerNum, layerData) {
        // 切换到层级详情标签页
        this.switchTab('layers');

        // 更新层级详情显示
        const layerDetailsContainer = document.getElementById('layer-details');
        if (!layerDetailsContainer) return;

        layerDetailsContainer.innerHTML = `
            <div style="background: rgba(255,255,255,0.05); border: 1px solid #${layerData.color.toString(16).padStart(6, '0')}; border-radius: 8px; padding: 1.5rem; margin-bottom: 1rem;">
                <div style="display: flex; align-items: center; gap: 1rem; margin-bottom: 1rem;">
                    <span style="width: 40px; height: 40px; background: #${layerData.color.toString(16).padStart(6, '0')}; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-weight: 600; font-size: 1.125rem;">${layerNum}</span>
                    <div>
                        <h4 style="margin: 0; color: #ffffff; font-size: 1.125rem;">${layerData.name}</h4>
                        <p style="margin: 0; font-size: 0.875rem; color: #94a3b8;">${layerData.english}</p>
                    </div>
                </div>

                <div style="margin-bottom: 1rem;">
                    <p style="margin: 0; color: #cbd5e1; line-height: 1.6;">${layerData.description}</p>
                    <p style="margin: 0.5rem 0 0 0; color: #94a3b8; font-size: 0.875rem;"><strong>生物类比:</strong> ${layerData.bioAnalogy}</p>
                </div>

                <div style="margin-bottom: 1rem;">
                    <h5 style="margin: 0 0 0.5rem 0; color: #cbd5e1; font-size: 0.875rem;">主要协议:</h5>
                    <div style="display: flex; flex-wrap: wrap; gap: 0.25rem;">
                        ${layerData.protocols.map(protocol =>
                            `<span style="background: #${layerData.color.toString(16).padStart(6, '0')}; color: white; padding: 0.25rem 0.5rem; border-radius: 4px; font-size: 0.75rem; font-weight: 500;">${protocol}</span>`
                        ).join('')}
                    </div>
                </div>

                <div>
                    <h5 style="margin: 0 0 0.5rem 0; color: #cbd5e1; font-size: 0.875rem;">相关设备:</h5>
                    <ul style="margin: 0; padding-left: 1rem; color: #94a3b8; font-size: 0.875rem;">
                        ${layerData.devices.map(device => `<li>${device}</li>`).join('')}
                    </ul>
                </div>
            </div>
        `;
    }

    switchTab(tabName) {
        document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
        document.querySelectorAll('.tab-panel').forEach(panel => panel.classList.remove('active'));

        const targetBtn = document.querySelector(`[data-tab="${tabName}"]`);
        const targetPanel = document.querySelector(`.tab-panel[data-tab="${tabName}"]`);

        if (targetBtn && targetPanel) {
            targetBtn.classList.add('active');
            targetPanel.classList.add('active');
        }
    }

    switchView(view) {
        this.currentView = view;

        switch (view) {
            case 'overview':
                this.updateCameraPosition(0, 60, 35);
                break;
            case 'layers':
                this.updateCameraPosition(Math.PI / 4, 40, 50);
                break;
            case 'network':
                this.updateCameraPosition(Math.PI / 2, 80, 20);
                break;
        }

        // 更新状态显示
        const cameraPosition = document.getElementById('camera-position');
        if (cameraPosition) {
            const viewNames = {
                'overview': '生态总览',
                'layers': '层级聚焦',
                'network': '网络拓扑'
            };
            cameraPosition.textContent = `视角: ${viewNames[view]}`;
        }
    }

    toggleSimulation() {
        this.isSimulating = !this.isSimulating;
        const btn = document.getElementById('start-simulation');
        if (btn) {
            const icon = btn.querySelector('i');
            const text = btn.querySelector('span');
            if (this.isSimulating) {
                icon.className = 'fas fa-pause';
                text.textContent = '暂停生命流';
            } else {
                icon.className = 'fas fa-play';
                text.textContent = '启动生命流';
            }
        }

        // 更新状态显示
        const simulationStatus = document.getElementById('simulation-status');
        if (simulationStatus) {
            simulationStatus.textContent = this.isSimulating ? '生态系统运行中' : '生态系统就绪';
        }
    }

    resetSimulation() {
        this.isSimulating = false;

        // 重置所有动画状态
        this.layerMeshes.forEach(mesh => {
            mesh.scale.setScalar(1);
            mesh.rotation.set(0, 0, 0);
            if (mesh.material.emissiveIntensity !== undefined) {
                mesh.material.emissiveIntensity = 0.1;
            }
        });

        // 更新UI
        const btn = document.getElementById('start-simulation');
        if (btn) {
            const icon = btn.querySelector('i');
            const text = btn.querySelector('span');
            icon.className = 'fas fa-play';
            text.textContent = '启动生命流';
        }

        const simulationStatus = document.getElementById('simulation-status');
        if (simulationStatus) {
            simulationStatus.textContent = '生态系统就绪';
        }

        console.log('🔄 仿真已重置');
    }

    toggleFullscreen() {
        if (!document.fullscreenElement) {
            document.documentElement.requestFullscreen();
        } else {
            document.exitFullscreen();
        }
    }

    showError(message) {
        console.error(message);
        alert(message);
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    window.osiSimulator = new OSI3DSimulator();
    console.log('✅ OSI 3D模拟器已启动');
});
