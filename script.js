// OSI 3D 生物仿生艺术模拟器 - 兼容版本
// 可直接在浏览器中运行，无需服务器

/**
 * OSI 3D模拟器类
 * 兼容版本，使用传统JavaScript语法
 */
class OSI3DSimulator {
    constructor() {
        // 核心Three.js对象
        this.scene = null;
        this.camera = null;
        this.renderer = null;
        this.composer = null;
        this.controls = null;
        this.animationId = null;
        
        // 状态管理
        this.isSimulating = false;
        this.currentView = 'overview';
        this.focusedLayer = null;
        
        // 3D对象集合
        this.layerMeshes = [];
        this.particleSystems = [];
        
        // 性能监控
        this.stats = {
            fps: 0,
            frameCount: 0,
            lastTime: performance.now(),
            memory: 0
        };
        
        // OSI层级数据
        this.layerData = {
            7: { 
                name: "应用层", 
                english: "Application", 
                color: 0xE6B3FF, 
                description: "神经网络中枢",
                protocols: ["HTTP", "HTTPS", "FTP", "SMTP", "DNS"],
                devices: ["Web服务器", "邮件服务器", "DNS服务器"],
                bioAnalogy: "大脑皮层"
            },
            6: { 
                name: "表示层", 
                english: "Presentation", 
                color: 0xFF6B9D, 
                description: "转译生物器官",
                protocols: ["SSL", "TLS", "JPEG", "MPEG", "ASCII"],
                devices: ["加密设备", "压缩器", "格式转换器"],
                bioAnalogy: "感觉器官"
            },
            5: { 
                name: "会话层", 
                english: "Session", 
                color: 0x00E5FF, 
                description: "对话细胞集群",
                protocols: ["NetBIOS", "RPC", "SQL", "NFS"],
                devices: ["会话管理器", "认证服务器"],
                bioAnalogy: "神经突触"
            },
            4: { 
                name: "传输层", 
                english: "Transport", 
                color: 0x69F0AE, 
                description: "循环系统节点",
                protocols: ["TCP", "UDP", "SPX", "SCTP"],
                devices: ["网关", "防火墙", "代理服务器"],
                bioAnalogy: "循环系统"
            },
            3: { 
                name: "网络层", 
                english: "Network", 
                color: 0xFFD54F, 
                description: "导航蜂群",
                protocols: ["IP", "ICMP", "IGMP", "ARP"],
                devices: ["路由器", "三层交换机"],
                bioAnalogy: "神经网络"
            },
            2: { 
                name: "数据链路层", 
                english: "Data Link", 
                color: 0xFF5722, 
                description: "微生物群落",
                protocols: ["Ethernet", "PPP", "Frame Relay"],
                devices: ["交换机", "网桥", "网卡"],
                bioAnalogy: "细胞膜"
            },
            1: { 
                name: "物理层", 
                english: "Physical", 
                color: 0x9C27B0, 
                description: "菌丝网络基质",
                protocols: ["电信号", "光信号", "无线信号"],
                devices: ["集线器", "中继器", "网线", "光纤"],
                bioAnalogy: "DNA双螺旋"
            }
        };
        
        this.init();
    }
    
    async init() {
        try {
            console.log('🚀 开始初始化OSI 3D模拟器...');
            
            // 显示启动画面
            this.showStartupScreen();
            
            // 检查WebGL支持
            if (!this.checkWebGLSupport()) {
                throw new Error('当前浏览器不支持WebGL');
            }
            
            // 等待Three.js加载
            await this.waitForThreeJS();
            
            // 初始化3D场景
            await this.setupScene();
            
            // 创建OSI层级
            this.createOSILayers();
            
            // 生成层级标签
            this.generateLayerLabels();
            
            // 设置事件监听
            this.setupEventListeners();
            
            // 开始渲染循环
            this.startRenderLoop();
            
            // 隐藏启动画面
            setTimeout(() => this.hideStartupScreen(), 2000);
            
            console.log('✅ OSI 3D模拟器初始化完成');
            
        } catch (error) {
            console.error('❌ 初始化失败:', error);
            this.showError('初始化失败: ' + error.message);
        }
    }
    
    async waitForThreeJS() {
        return new Promise((resolve) => {
            if (typeof THREE !== 'undefined') {
                resolve();
            } else {
                const checkInterval = setInterval(() => {
                    if (typeof THREE !== 'undefined') {
                        clearInterval(checkInterval);
                        resolve();
                    }
                }, 100);
            }
        });
    }
    
    checkWebGLSupport() {
        try {
            const canvas = document.createElement('canvas');
            const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
            if (!gl) return false;
            
            const renderer = gl.getParameter(gl.RENDERER);
            const vendor = gl.getParameter(gl.VENDOR);
            console.log(`✅ WebGL支持正常 - ${vendor}: ${renderer}`);
            return true;
        } catch (e) {
            console.error('WebGL检测失败:', e);
            return false;
        }
    }
    
    showStartupScreen() {
        const progressFill = document.getElementById('progress-fill');
        const progressText = document.getElementById('progress-text');
        
        let progress = 0;
        const interval = setInterval(() => {
            progress += Math.random() * 15;
            if (progress > 100) progress = 100;
            
            if (progressFill) progressFill.style.width = progress + '%';
            
            if (progress >= 100) {
                clearInterval(interval);
                if (progressText) progressText.textContent = '初始化完成！';
            }
        }, 200);
    }
    
    hideStartupScreen() {
        const startupScreen = document.getElementById('startup-screen');
        if (startupScreen) {
            startupScreen.classList.add('hidden');
        }
    }
    
    async setupScene() {
        const container = document.getElementById('three-canvas');
        if (!container) {
            throw new Error('找不到3D场景容器');
        }
        
        const rect = container.getBoundingClientRect();
        const width = rect.width || 800;
        const height = rect.height || 600;
        
        console.log(`📐 场景容器尺寸: ${width}x${height}`);
        
        // 创建场景
        this.scene = new THREE.Scene();
        this.scene.background = new THREE.Color(0x0a0b1e);
        this.scene.fog = new THREE.Fog(0x0a0b1e, 50, 200);
        
        // 创建摄像机
        this.camera = new THREE.PerspectiveCamera(75, width / height, 0.1, 1000);
        this.camera.position.set(40, 35, 40);
        this.camera.lookAt(0, 10, 0);
        
        // 创建渲染器
        this.renderer = new THREE.WebGLRenderer({ 
            antialias: true,
            alpha: true,
            powerPreference: "high-performance"
        });
        this.renderer.setSize(width, height);
        this.renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
        this.renderer.shadowMap.enabled = true;
        this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
        
        container.appendChild(this.renderer.domElement);
        
        // 设置后处理（如果可用）
        this.setupPostProcessing();
        
        // 添加光照系统
        this.setupLighting();
        
        // 创建环境
        this.createEnvironment();
        
        // 设置控制器
        this.setupControls();
    }
    
    setupPostProcessing() {
        // 检查后处理支持
        if (typeof THREE.EffectComposer !== 'undefined') {
            try {
                this.composer = new THREE.EffectComposer(this.renderer);
                
                const renderPass = new THREE.RenderPass(this.scene, this.camera);
                this.composer.addPass(renderPass);
                
                console.log('🎨 后处理效果已启用');
            } catch (error) {
                console.log('⚠️ 后处理效果不可用，使用基础渲染');
            }
        } else {
            console.log('⚠️ 后处理效果不可用，使用基础渲染');
        }
    }
    
    setupLighting() {
        // 环境光
        const ambientLight = new THREE.AmbientLight(0x404040, 0.4);
        this.scene.add(ambientLight);
        
        // 主方向光
        const mainLight = new THREE.DirectionalLight(0xffffff, 1.2);
        mainLight.position.set(50, 100, 50);
        mainLight.castShadow = true;
        mainLight.shadow.mapSize.width = 2048;
        mainLight.shadow.mapSize.height = 2048;
        this.scene.add(mainLight);
        
        // 为每个OSI层添加特色光源
        Object.entries(this.layerData).forEach(([layerNum, data]) => {
            const pointLight = new THREE.PointLight(data.color, 0.8, 20);
            pointLight.position.set(0, (parseInt(layerNum) - 1) * 3 + 3, 0);
            pointLight.userData = { 
                originalIntensity: 0.8,
                pulsePhase: Math.random() * Math.PI * 2,
                layerNum: layerNum
            };
            this.scene.add(pointLight);
        });
        
        console.log('💡 光照系统设置完成');
    }
    
    createEnvironment() {
        // 创建地面
        const groundGeometry = new THREE.PlaneGeometry(100, 100);
        const groundMaterial = new THREE.MeshLambertMaterial({ 
            color: 0x1a1b2e,
            transparent: true,
            opacity: 0.8
        });
        
        const ground = new THREE.Mesh(groundGeometry, groundMaterial);
        ground.rotation.x = -Math.PI / 2;
        ground.position.y = -2;
        ground.receiveShadow = true;
        
        this.scene.add(ground);
        
        // 创建背景粒子
        this.createBackgroundParticles();

        console.log('🌍 环境创建完成');
    }

    createBackgroundParticles() {
        const particleCount = 300;
        const positions = new Float32Array(particleCount * 3);
        const colors = new Float32Array(particleCount * 3);

        for (let i = 0; i < particleCount; i++) {
            const i3 = i * 3;

            positions[i3] = (Math.random() - 0.5) * 200;
            positions[i3 + 1] = Math.random() * 100;
            positions[i3 + 2] = (Math.random() - 0.5) * 200;

            const color = new THREE.Color();
            color.setHSL(Math.random() * 0.3 + 0.5, 0.7, 0.5);
            colors[i3] = color.r;
            colors[i3 + 1] = color.g;
            colors[i3 + 2] = color.b;
        }

        const particleGeometry = new THREE.BufferGeometry();
        particleGeometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
        particleGeometry.setAttribute('color', new THREE.BufferAttribute(colors, 3));

        const particleMaterial = new THREE.PointsMaterial({
            size: 0.5,
            vertexColors: true,
            transparent: true,
            opacity: 0.6,
            blending: THREE.AdditiveBlending
        });

        const particles = new THREE.Points(particleGeometry, particleMaterial);
        this.scene.add(particles);
        this.particleSystems.push(particles);
    }

    createOSILayers() {
        Object.entries(this.layerData).forEach(([layerNum, data]) => {
            this.createLayerMesh(parseInt(layerNum), data);
        });

        console.log('🏗️ OSI层级创建完成');
    }

    createLayerMesh(layerNum, data) {
        // 创建有机形状的几何体
        const geometry = new THREE.BoxGeometry(8, 2, 8, 4, 2, 4);

        // 添加噪声变形，创造有机感
        const positions = geometry.attributes.position;
        for (let i = 0; i < positions.count; i++) {
            const x = positions.getX(i);
            const y = positions.getY(i);
            const z = positions.getZ(i);

            const noise = (Math.sin(x * 0.5) + Math.cos(z * 0.5)) * 0.2;
            positions.setY(i, y + noise);
        }
        geometry.computeVertexNormals();

        // 创建材质
        const material = new THREE.MeshPhongMaterial({
            color: data.color,
            transparent: true,
            opacity: 0.7,
            shininess: 100,
            specular: 0x4facfe,
            emissive: data.color,
            emissiveIntensity: 0.1
        });

        const mesh = new THREE.Mesh(geometry, material);
        mesh.position.y = (layerNum - 1) * 3;
        mesh.castShadow = true;
        mesh.receiveShadow = true;
        mesh.userData = {
            layerNum,
            data,
            type: 'osiLayer',
            originalPosition: mesh.position.clone(),
            pulsePhase: Math.random() * Math.PI * 2
        };

        this.scene.add(mesh);
        this.layerMeshes.push(mesh);

        // 创建发光边框
        const edges = new THREE.EdgesGeometry(geometry);
        const lineMaterial = new THREE.LineBasicMaterial({
            color: data.color,
            transparent: true,
            opacity: 0.8
        });

        const wireframe = new THREE.LineSegments(edges, lineMaterial);
        wireframe.position.copy(mesh.position);
        this.scene.add(wireframe);
    }

    generateLayerLabels() {
        const labelsContainer = document.getElementById('layer-labels');
        if (!labelsContainer) return;

        labelsContainer.innerHTML = '';

        const positions = [
            { top: '10%', left: '20%' },
            { top: '20%', left: '70%' },
            { top: '30%', left: '25%' },
            { top: '40%', left: '65%' },
            { top: '50%', left: '30%' },
            { top: '60%', left: '60%' },
            { top: '70%', left: '35%' }
        ];

        Object.entries(this.layerData).forEach(([layerNum, data], index) => {
            const label = document.createElement('div');
            label.className = 'layer-label';
            label.dataset.layer = layerNum;
            label.style.top = positions[index].top;
            label.style.left = positions[index].left;

            label.innerHTML = `
                <div class="label-content">
                    <div class="label-header">
                        <span class="label-number">${layerNum}</span>
                    </div>
                    <div class="label-info">
                        <div class="label-name">${data.name}</div>
                        <div class="label-english">${data.english}</div>
                        <div class="label-description">${data.description}</div>
                    </div>
                </div>
            `;

            label.addEventListener('click', () => this.focusOnLayer(layerNum));
            labelsContainer.appendChild(label);
        });
    }

    setupControls() {
        if (typeof THREE.OrbitControls !== 'undefined') {
            this.controls = new THREE.OrbitControls(this.camera, this.renderer.domElement);
            this.controls.enableDamping = true;
            this.controls.dampingFactor = 0.05;
            this.controls.screenSpacePanning = false;
            this.controls.minDistance = 20;
            this.controls.maxDistance = 100;
            this.controls.maxPolarAngle = Math.PI / 2;
        } else {
            // 简单的鼠标控制
            this.setupSimpleControls();
        }
    }

    setupSimpleControls() {
        const canvas = this.renderer.domElement;
        let isMouseDown = false;
        let mouseX = 0, mouseY = 0;
        let cameraAngle = 0;
        let cameraRadius = 60;
        let cameraHeight = 35;

        canvas.addEventListener('mousedown', (e) => {
            isMouseDown = true;
            mouseX = e.clientX;
            mouseY = e.clientY;
            canvas.style.cursor = 'grabbing';
        });

        canvas.addEventListener('mousemove', (e) => {
            if (!isMouseDown) return;

            const deltaX = e.clientX - mouseX;
            const deltaY = e.clientY - mouseY;

            cameraAngle += deltaX * 0.01;
            cameraHeight += deltaY * 0.1;
            cameraHeight = Math.max(10, Math.min(80, cameraHeight));

            this.updateCameraPosition(cameraAngle, cameraRadius, cameraHeight);

            mouseX = e.clientX;
            mouseY = e.clientY;
        });

        canvas.addEventListener('mouseup', () => {
            isMouseDown = false;
            canvas.style.cursor = 'grab';
        });

        canvas.addEventListener('wheel', (e) => {
            e.preventDefault();
            cameraRadius += e.deltaY * 0.1;
            cameraRadius = Math.max(20, Math.min(100, cameraRadius));

            this.updateCameraPosition(cameraAngle, cameraRadius, cameraHeight);
        });

        canvas.style.cursor = 'grab';
    }

    updateCameraPosition(angle, radius, height) {
        this.camera.position.x = Math.cos(angle) * radius;
        this.camera.position.y = height;
        this.camera.position.z = Math.sin(angle) * radius;
        this.camera.lookAt(0, 10, 0);
    }

    setupEventListeners() {
        // 窗口大小调整
        window.addEventListener('resize', () => this.onWindowResize());

        // 仿真控制按钮
        const startBtn = document.getElementById('start-simulation');
        if (startBtn) {
            startBtn.addEventListener('click', () => this.toggleSimulation());
        }

        const resetBtn = document.getElementById('reset-simulation');
        if (resetBtn) {
            resetBtn.addEventListener('click', () => this.resetSimulation());
        }
    }

    startRenderLoop() {
        const animate = () => {
            this.animationId = requestAnimationFrame(animate);

            // 更新控制器
            if (this.controls && this.controls.update) {
                this.controls.update();
            }

            // 更新动画
            this.updateAnimations();

            // 渲染
            if (this.composer) {
                this.composer.render();
            } else {
                this.renderer.render(this.scene, this.camera);
            }
        };

        animate();
        console.log('🎬 渲染循环已启动');
    }

    updateAnimations() {
        const time = performance.now() * 0.001;

        // 更新OSI层级动画
        this.layerMeshes.forEach(mesh => {
            const userData = mesh.userData;

            // 脉冲效果
            const pulseIntensity = Math.sin(time * 2 + userData.pulsePhase) * 0.1 + 0.9;
            mesh.scale.setScalar(pulseIntensity);

            // 轻微旋转
            mesh.rotation.y += 0.005 * (userData.layerNum / 7);

            // 材质发光强度变化
            if (mesh.material.emissiveIntensity !== undefined) {
                mesh.material.emissiveIntensity = 0.1 + Math.sin(time + userData.pulsePhase) * 0.05;
            }
        });

        // 更新粒子系统
        this.particleSystems.forEach(system => {
            system.rotation.y += 0.001;
        });
    }

    onWindowResize() {
        const container = document.getElementById('three-canvas');
        if (!container || !this.renderer || !this.camera) return;

        const rect = container.getBoundingClientRect();
        const width = rect.width || 800;
        const height = rect.height || 600;

        this.camera.aspect = width / height;
        this.camera.updateProjectionMatrix();
        this.renderer.setSize(width, height);

        if (this.composer) {
            this.composer.setSize(width, height);
        }
    }

    focusOnLayer(layerNum) {
        console.log(`🎯 聚焦到第${layerNum}层`);
        this.focusedLayer = layerNum;
    }

    toggleSimulation() {
        this.isSimulating = !this.isSimulating;
        const btn = document.getElementById('start-simulation');
        if (btn) {
            const icon = btn.querySelector('i');
            const text = btn.querySelector('span');
            if (this.isSimulating) {
                if (icon) icon.className = 'fas fa-pause';
                if (text) text.textContent = '暂停生命流';
            } else {
                if (icon) icon.className = 'fas fa-play';
                if (text) text.textContent = '启动生命流';
            }
        }
    }

    resetSimulation() {
        this.isSimulating = false;

        // 重置所有动画状态
        this.layerMeshes.forEach(mesh => {
            mesh.scale.setScalar(1);
            mesh.rotation.set(0, 0, 0);
            if (mesh.material.emissiveIntensity !== undefined) {
                mesh.material.emissiveIntensity = 0.1;
            }
        });

        console.log('🔄 仿真已重置');
    }

    showError(message) {
        console.error(message);
        alert(message);
    }
}

// 全局可用
window.OSI3DSimulator = OSI3DSimulator;
