<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OSI 3D 调试测试</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/three@0.128.0/build/three.min.js"></script>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #0a0b1e;
            color: white;
            font-family: Arial, sans-serif;
        }
        
        .debug-info {
            position: fixed;
            top: 10px;
            left: 10px;
            background: rgba(0,0,0,0.8);
            padding: 10px;
            border-radius: 5px;
            font-size: 12px;
            z-index: 1000;
        }
        
        .error {
            color: #ff4444;
        }
        
        .success {
            color: #44ff44;
        }
        
        .warning {
            color: #ffaa44;
        }
        
        #three-canvas {
            width: 100%;
            height: 400px;
            border: 1px solid #333;
            margin: 20px 0;
        }
        
        .controls {
            margin: 20px 0;
        }
        
        button {
            background: #4facfe;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
        }
        
        button:hover {
            background: #3d8bfe;
        }
    </style>
</head>
<body>
    <h1>🧬 OSI 3D 调试测试</h1>
    
    <div class="debug-info" id="debug-info">
        <div>调试信息加载中...</div>
    </div>
    
    <div class="controls">
        <button onclick="testBasicInit()">测试基础初始化</button>
        <button onclick="testThreeJS()">测试Three.js</button>
        <button onclick="testDataFlow()">测试数据流系统</button>
        <button onclick="testInteraction()">测试交互系统</button>
        <button onclick="testInfoPanel()">测试信息面板</button>
    </div>
    
    <div id="three-canvas"></div>
    
    <div id="test-results">
        <h3>测试结果:</h3>
        <div id="results-content"></div>
    </div>
    
    <script>
        let debugInfo = document.getElementById('debug-info');
        let resultsContent = document.getElementById('results-content');
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : type === 'warning' ? 'warning' : '';
            
            debugInfo.innerHTML += `<div class="${className}">[${timestamp}] ${message}</div>`;
            console.log(`[${type.toUpperCase()}] ${message}`);
            
            // 保持最新的10条消息
            const lines = debugInfo.children;
            if (lines.length > 10) {
                debugInfo.removeChild(lines[0]);
            }
        }
        
        function addResult(test, status, details = '') {
            const resultDiv = document.createElement('div');
            resultDiv.innerHTML = `
                <div style="margin: 10px 0; padding: 10px; background: rgba(255,255,255,0.1); border-radius: 5px;">
                    <strong>${test}:</strong> 
                    <span style="color: ${status === 'PASS' ? '#44ff44' : '#ff4444'}">${status}</span>
                    ${details ? `<br><small>${details}</small>` : ''}
                </div>
            `;
            resultsContent.appendChild(resultDiv);
        }
        
        function testBasicInit() {
            log('开始基础初始化测试', 'info');
            
            try {
                // 测试基本的JavaScript功能
                const testObj = {
                    test: 'value',
                    method: function() { return 'working'; }
                };
                
                if (testObj.method() === 'working') {
                    log('JavaScript基础功能正常', 'success');
                    addResult('JavaScript基础', 'PASS');
                } else {
                    throw new Error('JavaScript基础功能异常');
                }
                
                // 测试DOM操作
                const testDiv = document.createElement('div');
                testDiv.textContent = 'test';
                if (testDiv.textContent === 'test') {
                    log('DOM操作正常', 'success');
                    addResult('DOM操作', 'PASS');
                } else {
                    throw new Error('DOM操作异常');
                }
                
                // 测试Three.js加载
                if (typeof THREE !== 'undefined') {
                    log('Three.js库加载成功', 'success');
                    addResult('Three.js加载', 'PASS');
                } else {
                    throw new Error('Three.js库未加载');
                }
                
            } catch (error) {
                log(`基础初始化测试失败: ${error.message}`, 'error');
                addResult('基础初始化', 'FAIL', error.message);
            }
        }
        
        function testThreeJS() {
            log('开始Three.js测试', 'info');
            
            try {
                const container = document.getElementById('three-canvas');
                const scene = new THREE.Scene();
                const camera = new THREE.PerspectiveCamera(75, container.offsetWidth / container.offsetHeight, 0.1, 1000);
                const renderer = new THREE.WebGLRenderer();
                
                renderer.setSize(container.offsetWidth, container.offsetHeight);
                container.appendChild(renderer.domElement);
                
                // 创建一个简单的立方体
                const geometry = new THREE.BoxGeometry();
                const material = new THREE.MeshBasicMaterial({ color: 0x4facfe });
                const cube = new THREE.Mesh(geometry, material);
                scene.add(cube);
                
                camera.position.z = 5;
                
                // 渲染一帧
                renderer.render(scene, camera);
                
                log('Three.js场景创建成功', 'success');
                addResult('Three.js场景', 'PASS', '成功创建并渲染3D场景');
                
            } catch (error) {
                log(`Three.js测试失败: ${error.message}`, 'error');
                addResult('Three.js场景', 'FAIL', error.message);
            }
        }
        
        function testDataFlow() {
            log('开始数据流系统测试', 'info');
            
            try {
                if (typeof DataFlowSystem !== 'undefined') {
                    log('DataFlowSystem类已加载', 'success');
                    addResult('数据流系统加载', 'PASS');
                } else {
                    log('DataFlowSystem类未加载', 'warning');
                    addResult('数据流系统加载', 'FAIL', 'DataFlowSystem类未定义');
                }
            } catch (error) {
                log(`数据流系统测试失败: ${error.message}`, 'error');
                addResult('数据流系统', 'FAIL', error.message);
            }
        }
        
        function testInteraction() {
            log('开始交互系统测试', 'info');
            
            try {
                if (typeof InteractionSystem !== 'undefined') {
                    log('InteractionSystem类已加载', 'success');
                    addResult('交互系统加载', 'PASS');
                } else {
                    log('InteractionSystem类未加载', 'warning');
                    addResult('交互系统加载', 'FAIL', 'InteractionSystem类未定义');
                }
            } catch (error) {
                log(`交互系统测试失败: ${error.message}`, 'error');
                addResult('交互系统', 'FAIL', error.message);
            }
        }
        
        function testInfoPanel() {
            log('开始信息面板测试', 'info');
            
            try {
                if (typeof InfoPanelSystem !== 'undefined') {
                    log('InfoPanelSystem类已加载', 'success');
                    addResult('信息面板系统加载', 'PASS');
                } else {
                    log('InfoPanelSystem类未加载', 'warning');
                    addResult('信息面板系统加载', 'FAIL', 'InfoPanelSystem类未定义');
                }
            } catch (error) {
                log(`信息面板测试失败: ${error.message}`, 'error');
                addResult('信息面板系统', 'FAIL', error.message);
            }
        }
        
        // 页面加载完成后自动运行基础测试
        window.addEventListener('load', () => {
            log('页面加载完成，开始自动测试', 'info');
            setTimeout(() => {
                testBasicInit();
            }, 500);
        });
        
        // 错误捕获
        window.addEventListener('error', (event) => {
            log(`全局错误: ${event.error.message}`, 'error');
            addResult('全局错误捕获', 'FAIL', `${event.filename}:${event.lineno} - ${event.error.message}`);
        });
        
        log('调试系统初始化完成', 'success');
    </script>
    
    <!-- 加载系统文件进行测试 -->
    <script src="data-flow-system.js"></script>
    <script src="interaction-system.js"></script>
    <script src="info-panel-system.js"></script>
</body>
</html>
